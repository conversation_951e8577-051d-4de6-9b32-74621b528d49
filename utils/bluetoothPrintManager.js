/**
 * 蓝牙打印管理器 - 基于精臣云打印SDK
 */

// 引入精臣云打印SDK
const jcapi = uni.requireNativePlugin("JCSDK-JCApiModule");

class BluetoothPrintManager {
  constructor() {
    this.connectedDevice = null;
    this.deviceList = [];
    this.isInitialized = false;
    this.printQueue = [];
    this.isPrinting = false;
  }

  /**
   * 初始化SDK
   */
  initSDK() {
    if (this.isInitialized) return;
    
    try {
      jcapi.initSDK();
      this.isInitialized = true;
      
      // 监听打印错误回调
      jcapi.didReadPrintErrorInfo((result) => {
        console.log('打印错误回调:', result);
        if (result.code == 23) {
          // 打印机断开连接
          this.connectedDevice = null;
          uni.showToast({
            icon: 'none',
            title: '打印机连接断开',
            duration: 2000
          });
        } else {
          uni.showToast({
            icon: 'none',
            title: `打印错误: ${result.code}`,
            duration: 2000
          });
        }
      });
      
      // 监听页码回调
      jcapi.didReadPrintCountInfo((result) => {
        console.log('打印页码信息:', result);
      });
      
      console.log('蓝牙打印SDK初始化成功');
    } catch (error) {
      console.error('蓝牙打印SDK初始化失败:', error);
    }
  }

  /**
   * 搜索蓝牙设备
   */
  searchBluetoothDevices() {
    return new Promise((resolve, reject) => {
      uni.openBluetoothAdapter({
        success: () => {
          uni.showLoading({ title: "搜索设备中..." });
          
          jcapi.getBluetoothDevices((deviceList) => {
            uni.hideLoading();
            this.deviceList = deviceList || [];
            console.log('搜索到的设备:', this.deviceList);
            resolve(this.deviceList);
          });
        },
        fail: (error) => {
          uni.hideLoading();
          console.error('蓝牙适配器打开失败:', error);
          reject(new Error('请开启蓝牙功能'));
        }
      });
    });
  }

  /**
   * 按名称搜索设备
   */
  searchDevicesByName(names = ["B21", "B32", "B3S"]) {
    return new Promise((resolve, reject) => {
      try {
        jcapi.getBluetoothDevicesWithName({
          "name": names
        }, (deviceList) => {
          this.deviceList = deviceList || [];
          console.log('按名称搜索到的设备:', this.deviceList);
          resolve(this.deviceList);
        });
      } catch (error) {
        console.error('按名称搜索设备失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 连接设备
   */
  connectDevice(device) {
    return new Promise((resolve, reject) => {
      if (!device || !device.address) {
        reject(new Error('设备信息无效'));
        return;
      }

      uni.showLoading({ title: "连接中..." });
      
      jcapi.openPrinterByDevice({
        address: device.address,
        name: device.name || '蓝牙打印机',
        deviceType: 0 // 0-蓝牙，1-网络
      }, (result) => {
        uni.hideLoading();
        
        if (result.code === 0) {
          this.connectedDevice = device;
          console.log('设备连接成功:', device);
          uni.showToast({
            title: '连接成功',
            icon: 'success'
          });
          resolve(device);
        } else {
          console.error('设备连接失败:', result);
          uni.showToast({
            title: '连接失败',
            icon: 'error'
          });
          reject(new Error(`连接失败: ${result.code}`));
        }
      });
    });
  }

  /**
   * 断开连接
   */
  disconnectDevice() {
    try {
      jcapi.close();
      this.connectedDevice = null;
      console.log('设备连接已断开');
      uni.showToast({
        title: '已断开连接',
        icon: 'success'
      });
    } catch (error) {
      console.error('断开连接失败:', error);
    }
  }

  /**
   * 检查连接状态
   */
  isConnected() {
    try {
      const result = jcapi.isConnected();
      return result.value === 1; // 1-已连接，0-断开
    } catch (error) {
      console.error('检查连接状态失败:', error);
      return false;
    }
  }

  /**
   * 获取已连接的设备
   */
  getConnectedDevice() {
    return this.connectedDevice;
  }

  /**
   * 获取设备列表
   */
  getDeviceList() {
    return this.deviceList;
  }

  /**
   * 通用打印方法
   */
  async printContent(drawCallback, options = {}) {
    if (!this.connectedDevice) {
      throw new Error('请先连接打印机');
    }

    try {
      // 1. 初始化画板
      jcapi.initDrawingBoard({
        width: options.width || 50,    // 宽度(mm) - 5cm
        height: options.height || 30,  // 高度(mm) - 3cm
        rotate: options.rotate || 0    // 旋转角度
      });

      // 2. 执行绘制回调
      await drawCallback();

      // 3. 生成打印数据
      const imageJsonObj = jcapi.generateLabelJson();

      // 4. 开始打印任务
      return new Promise((resolve, reject) => {
        jcapi.startJob({
          totalCount: options.totalCount || 1,
          density: options.density || 5,      // 打印浓度 1-15
          labelType: options.labelType || 1,  // 标签类型：1-间隙纸，2-黑标纸，3-连续纸
          printMode: options.printMode || 1   // 打印模式：1-热敏，2-热转印
        }, (result) => {
          if (result.code === 0) {
            // 5. 发送打印数据
            jcapi.printData(imageJsonObj, {
              printQuantity: options.printQuantity || 1
            }, (printResult) => {
              if (printResult.code === 0) {
                console.log('打印成功');
                resolve(printResult);
              } else {
                console.error('打印失败:', printResult);
                reject(new Error(`打印失败: ${printResult.code}`));
              }
            });
          } else {
            console.error('启动打印任务失败:', result);
            reject(new Error(`启动打印任务失败: ${result.code}`));
          }
        });
      });
    } catch (error) {
      console.error('打印过程出错:', error);
      throw error;
    }
  }

  /**
   * 打印生产标签
   */
  async printProductionLabel(labelData, options = {}) {
    return this.printContent(() => {
      // 标签尺寸：50mm x 30mm
      const labelWidth = 50;
      const labelHeight = 30;
      
      // 绘制外边框
      jcapi.drawLabelGraph({
        x: 1,
        y: 1,
        width: labelWidth - 2,
        height: labelHeight - 2,
        graphType: 3, // 矩形
        rotate: 0,
        lineWidth: 1,
        lineType: 1 // 实线
      });

      // 标题：生产标签
      jcapi.drawLabelText({
        x: 2,
        y: 2,
        width: labelWidth - 4,
        height: 4,
        value: '生产标签',
        fontSize: 3,
        rotate: 0,
        lineMode: 1, // 单行
        textAlignHorizontal: 1, // 居中
        textAlignVertical: 1,   // 居中
        bold: 1
      });

      // 订单号（加粗）
      jcapi.drawLabelText({
        x: 2,
        y: 6,
        width: labelWidth - 4,
        height: 3,
        value: labelData.orderNo || '未选择工单',
        fontSize: 4,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0, // 左对齐
        textAlignVertical: 1,
        bold: 1
      });

      // 分隔线
      jcapi.drawLabelLine({
        x: 2,
        y: 9.5,
        width: labelWidth - 4,
        height: 0.5,
        rotate: 0,
        lineType: 1
      });

      // 料号
      jcapi.drawLabelText({
        x: 2,
        y: 10.5,
        width: labelWidth - 4,
        height: 2.5,
        value: `料号: ${labelData.materialCode || '无'}`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 品名
      jcapi.drawLabelText({
        x: 2,
        y: 13,
        width: labelWidth - 4,
        height: 2.5,
        value: `品名: ${labelData.materialName || '无'}`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 规格
      jcapi.drawLabelText({
        x: 2,
        y: 15.5,
        width: labelWidth - 4,
        height: 2.5,
        value: `规格: ${labelData.specification || '无'}`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 分隔线
      jcapi.drawLabelLine({
        x: 2,
        y: 18.5,
        width: labelWidth - 4,
        height: 0.5,
        rotate: 0,
        lineType: 1
      });

      // 数量信息
      const totalQty = (Number(labelData.goodQty) || 0) + (Number(labelData.badQty) || 0);
      jcapi.drawLabelText({
        x: 2,
        y: 19.5,
        width: labelWidth - 4,
        height: 2.5,
        value: `数量: ${totalQty} (良品:${labelData.goodQty || 0} 不良:${labelData.badQty || 0})`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 产线
      jcapi.drawLabelText({
        x: 2,
        y: 22,
        width: 23,
        height: 2.5,
        value: `产线: ${labelData.lineName || '未选择'}`,
        fontSize: 2,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 报工人
      jcapi.drawLabelText({
        x: 25,
        y: 22,
        width: 23,
        height: 2.5,
        value: `报工人: ${labelData.userName || '未扫描'}`,
        fontSize: 2,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 分隔线
      jcapi.drawLabelLine({
        x: 2,
        y: 25,
        width: labelWidth - 4,
        height: 0.5,
        rotate: 0,
        lineType: 1
      });

      // 时间
      const currentTime = new Date();
      const timeStr = `${currentTime.getFullYear()}-${(currentTime.getMonth()+1).toString().padStart(2,'0')}-${currentTime.getDate().toString().padStart(2,'0')} ${currentTime.getHours().toString().padStart(2,'0')}:${currentTime.getMinutes().toString().padStart(2,'0')}`;
      
      jcapi.drawLabelText({
        x: 2,
        y: 26,
        width: labelWidth - 4,
        height: 2.5,
        value: `时间: ${timeStr}`,
        fontSize: 2,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

    }, options);
  }

  /**
   * 批量打印
   */
  async printBatch(labelDataList, options = {}) {
    if (!Array.isArray(labelDataList) || labelDataList.length === 0) {
      throw new Error('打印数据不能为空');
    }

    const total = labelDataList.length;
    let successCount = 0;
    let failCount = 0;

    uni.showLoading({ title: `批量打印中 0/${total}` });

    try {
      for (let i = 0; i < labelDataList.length; i++) {
        const labelData = labelDataList[i];
        
        try {
          await this.printProductionLabel(labelData, {
            ...options,
            totalCount: 1,
            printQuantity: 1
          });
          
          successCount++;
          uni.showLoading({ title: `批量打印中 ${i + 1}/${total}` });
          
          // 打印间隔，避免打印机过载
          if (i < labelDataList.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.error(`第${i + 1}个标签打印失败:`, error);
          failCount++;
        }
      }
    } finally {
      uni.hideLoading();
    }

    const message = `批量打印完成！成功: ${successCount}, 失败: ${failCount}`;
    uni.showToast({
      title: message,
      icon: successCount > 0 ? 'success' : 'error',
      duration: 3000
    });

    return { successCount, failCount, total };
  }

  /**
   * 打印生产标签
   */
  async printProductionLabel(labelData, options = {}) {
    return this.printContent(() => {
      // 标签尺寸：50mm x 30mm
      const labelWidth = 50;
      const labelHeight = 30;

      // 绘制外边框
      jcapi.drawLabelGraph({
        x: 1,
        y: 1,
        width: labelWidth - 2,
        height: labelHeight - 2,
        graphType: 3, // 矩形
        rotate: 0,
        lineWidth: 1,
        lineType: 1 // 实线
      });

      // 标题：生产标签
      jcapi.drawLabelText({
        x: 2,
        y: 2,
        width: labelWidth - 4,
        height: 4,
        value: '生产标签',
        fontSize: 3,
        rotate: 0,
        lineMode: 1, // 单行
        textAlignHorizontal: 1, // 居中
        textAlignVertical: 1,   // 居中
        bold: 1
      });

      // 订单号（加粗）
      jcapi.drawLabelText({
        x: 2,
        y: 6,
        width: labelWidth - 4,
        height: 3,
        value: labelData.orderNo || '未选择工单',
        fontSize: 4,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0, // 左对齐
        textAlignVertical: 1,
        bold: 1
      });

      // 分隔线
      jcapi.drawLabelLine({
        x: 2,
        y: 9.5,
        width: labelWidth - 4,
        height: 0.5,
        rotate: 0,
        lineType: 1
      });

      // 料号
      jcapi.drawLabelText({
        x: 2,
        y: 10.5,
        width: labelWidth - 4,
        height: 2.5,
        value: `料号: ${labelData.materialCode || '无'}`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 品名
      jcapi.drawLabelText({
        x: 2,
        y: 13,
        width: labelWidth - 4,
        height: 2.5,
        value: `品名: ${labelData.materialName || '无'}`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 规格
      jcapi.drawLabelText({
        x: 2,
        y: 15.5,
        width: labelWidth - 4,
        height: 2.5,
        value: `规格: ${labelData.specification || '无'}`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 分隔线
      jcapi.drawLabelLine({
        x: 2,
        y: 18.5,
        width: labelWidth - 4,
        height: 0.5,
        rotate: 0,
        lineType: 1
      });

      // 数量信息
      const totalQty = (Number(labelData.goodQty) || 0) + (Number(labelData.badQty) || 0);
      jcapi.drawLabelText({
        x: 2,
        y: 19.5,
        width: labelWidth - 4,
        height: 2.5,
        value: `数量: ${totalQty} (良品:${labelData.goodQty || 0} 不良:${labelData.badQty || 0})`,
        fontSize: 2.5,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 产线
      jcapi.drawLabelText({
        x: 2,
        y: 22,
        width: 23,
        height: 2.5,
        value: `产线: ${labelData.lineName || '未选择'}`,
        fontSize: 2,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 报工人
      jcapi.drawLabelText({
        x: 25,
        y: 22,
        width: 23,
        height: 2.5,
        value: `报工人: ${labelData.userName || '未扫描'}`,
        fontSize: 2,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

      // 分隔线
      jcapi.drawLabelLine({
        x: 2,
        y: 25,
        width: labelWidth - 4,
        height: 0.5,
        rotate: 0,
        lineType: 1
      });

      // 时间
      const currentTime = new Date();
      const timeStr = `${currentTime.getFullYear()}-${(currentTime.getMonth()+1).toString().padStart(2,'0')}-${currentTime.getDate().toString().padStart(2,'0')} ${currentTime.getHours().toString().padStart(2,'0')}:${currentTime.getMinutes().toString().padStart(2,'0')}`;

      jcapi.drawLabelText({
        x: 2,
        y: 26,
        width: labelWidth - 4,
        height: 2.5,
        value: `时间: ${timeStr}`,
        fontSize: 2,
        rotate: 0,
        lineMode: 1,
        textAlignHorizontal: 0,
        textAlignVertical: 1
      });

    }, options);
  }

  /**
   * 批量打印
   */
  async printBatch(labelDataList, options = {}) {
    if (!Array.isArray(labelDataList) || labelDataList.length === 0) {
      throw new Error('打印数据不能为空');
    }

    const total = labelDataList.length;
    let successCount = 0;
    let failCount = 0;

    uni.showLoading({ title: `批量打印中 0/${total}` });

    try {
      for (let i = 0; i < labelDataList.length; i++) {
        const labelData = labelDataList[i];

        try {
          await this.printProductionLabel(labelData, {
            ...options,
            totalCount: 1,
            printQuantity: 1
          });

          successCount++;
          uni.showLoading({ title: `批量打印中 ${i + 1}/${total}` });

          // 打印间隔，避免打印机过载
          if (i < labelDataList.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.error(`第${i + 1}个标签打印失败:`, error);
          failCount++;
        }
      }
    } finally {
      uni.hideLoading();
    }

    const message = `批量打印完成！成功: ${successCount}, 失败: ${failCount}`;
    uni.showToast({
      title: message,
      icon: successCount > 0 ? 'success' : 'error',
      duration: 3000
    });

    return { successCount, failCount, total };
  }

  /**
   * 取消打印
   */
  cancelPrint() {
    return new Promise((resolve, reject) => {
      try {
        jcapi.cancelJob((result) => {
          if (result.code === 0) {
            console.log('取消打印成功');
            resolve(result);
          } else {
            console.error('取消打印失败:', result);
            reject(new Error(`取消打印失败: ${result.code}`));
          }
        });
      } catch (error) {
        console.error('取消打印出错:', error);
        reject(error);
      }
    });
  }
}

// 创建单例
const bluetoothPrintManager = new BluetoothPrintManager();

export default bluetoothPrintManager;
