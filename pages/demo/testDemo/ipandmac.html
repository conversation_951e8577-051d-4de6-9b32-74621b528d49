<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>js获取mac等主机信息</title>
	</head>

	<body>
		<object classid="CLSID:76A64158-CB41-11D1-8B02-00600806D9B6" id="locator"
			style="display:none;visibility:hidden"></object>
		<object classid="CLSID:75718C9A-F029-11d1-A1AC-00C04FB6C223" id="foo"
			style="display:none;visibility:hidden"></object>
		<form name="myForm">
			<br />MAC地址：<input type="text" name="macAddress">
			<br />IP地址：<input type="text" name="ipAddress">
			<br />计算机名：<input type="text" name="hostName">
			<br />当前用户名：<input type="text" id="userName">
		</form>
	</body>
</html>

<script language="javascript">
	var sMacAddr = "";
	var sIPAddr = "";
	var sDNSName = "";
	var service = locator.ConnectServer();
	service.Security_.ImpersonationLevel = 3;
	service.InstancesOfAsync(foo, 'Win32_NetworkAdapterConfiguration');
</script>
<script FOR="foo" EVENT="OnObjectReady(objObject,objAsyncContext)" LANGUAGE="JScript">
	if (objObject.IPEnabled != null && objObject.IPEnabled != "undefined" && objObject.IPEnabled == true) {

		if (objObject.IPEnabled && objObject.IPAddress(0) != null && objObject.IPAddress(0) != "undefined")
			sIPAddr = objObject.IPAddress(0);
		if (objObject.MACAddress != null && objObject.MACAddress != "undefined")
			sMacAddr = objObject.MACAddress;
		if (objObject.DNSHostName != null && objObject.DNSHostName != "undefined")
			sDNSName = objObject.DNSHostName;

	}
</script>

<script FOR="foo" EVENT="OnCompleted(hResult,pErrorObject, pAsyncContext)" LANGUAGE="JScript">
	myForm.macAddress.value = sMacAddr;
	myForm.ipAddress.value = sIPAddr;
	myForm.hostName.value = sDNSName;
</script>

<script>
	var WshShell = new ActiveXObject("WScript.Shell");
	x = document.getElementById("userName"); // 找到元素
	x.value = WshShell.ExpandEnvironmentStrings("%USERNAME%"); // 改变内容
</script>
