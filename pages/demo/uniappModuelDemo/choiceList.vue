<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<radio-group @change="radioChange">
				<label class="cu-form-group  " v-for="(item, index) in items" :key="item.value">
					<view>{{item.name}}</view>
					<view>
						<radio :value="item.value" :checked="index === current" />
					</view>
				</label>
			</radio-group>
			
			
			
			<radio-group @change="radioChange">
				<label class="cu-form-group margin-top " v-for="(item, index) in items" :key="item.value">
					<view>{{item.name}}</view>
					<view>
						<radio :value="item.value" :checked="index === current" />
					</view>
				</label>
			</radio-group>
			
			<radio-group @change="radioChange" class="margin-top">
				<view class="uni-list">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
							<view class="cu-form-group solid-bottom panel-full" >
								<view class="content">
									<view class="text-black cu-form-data-15">{{item[1]}}</view>
									<view class="text-black cu-form-data-15">计划/已接收: {{item[2]}} / {{item[3]}}</view>
									<view class="text-black cu-form-data-15">批次: {{item[4]}} -- {{item[5]}} </view>
								</view>
								<view>
									<radio :value="item.value" :checked="index === current" />
								</view>
							</view>
					</view>
				</view>	
				
			</radio-group>
		</view>
	</view>
</template>

<script>
	export default {
	
		onLoad(e) {
			console.log(e); //result 0提交成功  1 未查询到随工单 2 返回按钮
			// this.queryOrder();
		},
		data() {

			return {
				title: 'radio 单选框',
					items: [{
							value: 'USA',
							name: '美国'
						},
						{
							value: 'CHN',
							name: '中国',
							checked: 'true'
						}
						
					],
					current: 0,
					obj:[
						["","磷脂","1000kg","1000kg","20210101","秦皇岛",60],
						["","磷脂","1000kg","100kg","20210101","秦皇岛",20],
						["","磷脂","1000kg","110kg","20210101","秦皇岛",30],
						["","磷脂","1000kg","800kg","20210101","秦皇岛",40],
					]
				}

			},
		methods: {
			radioChange(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			}
		}
	}
</script>
