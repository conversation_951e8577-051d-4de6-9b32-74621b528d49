<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="title">备料剩余量：</view>
				<view class="detail-left cu-form-data-20">{{exampleValue}}</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">备料方式：</view>
				<view class="detail-left cu-form-data-17">{{exampleValue1}}</view>
				<text class=" uni-panel-icon uni-icon alin_x_center">&#xe470;</text>
			</view>
			
			<view class="cu-form-group margin-top">
				<view class="title">桶袋码：</view>
				<view class="detail-left cu-form-data-17">{{exampleValue2}}</view>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" @click="scanCode" color='#8f8f94' size="25" />
				
			</view>
			
			<view class="cu-form-group margin-top">
				<view class="title">输入框：</view>
				<input type="text" @confirm="enter" v-model="inputValue" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="reload" @click="reset" color='#8f8f94' size="25" />
				
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">数字选择框：</view>
				<view class="detail-left">
					<uni-number-box :value="numberValue" @change="change" />
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">开关按钮：</view>
				<view class="uni-list-cell uni-list-cell-pd">
					<view class="uni-list-cell-db" >{{open}}</view>
					<switch @change="switch2Change" />
				</view>
			</view>
			
			<!-- <view class="uni-flex uni-row">
				<view class="text" style="-webkit-flex: 1;flex: 1;">横向布局-平均分布</view>
				<view class="text" style="-webkit-flex: 1;flex: 1;">横向布局-平均分布</view>
			</view> -->
			<view class="cu-form-group margin-top uni-flex uni-row">
				<view class="title cu-avg">称量毛重：</view>
				<view class="cu-form-data-weight cu-avg" >{{exampleValue2}}</view>
				<view class="tag-view">
					<uni-tag text="打印" type="success" @click="getValue" />
				</view>
				<view class="tag-view">
					<uni-tag text="取值" type="success" @click="getValue" />
				</view>
			</view>
			
			<view class="tag-view panel-full margin-top">
				<uni-tag text="提交" type="success" @click="getValue" />
			</view>
	
			
			
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 备料记录
				</view>
			</view>
			<view class="uni-list">
				<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
						<view class="cu-form-group solid-bottom panel-full" :class="showStyle(item.status)">
							<view class="content">
								<view class="text-black cu-form-data-15">{{item.matDesc}}</view>
								<view class="text-black cu-form-data-15">计划/已接收: {{item.planQty}} / {{item.actQty}}</view>
								<view class="text-black cu-form-data-15">批次: {{item.batchNo}} -- {{item.productArea}} </view>
							</view>
						</view>
				</view>
			</view>	
		</view>
	</view>
</template>

<script>
	import permision from "@/common/js/permission.js"
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import UniNumberBox from '@/components/uni-ui/uni-number-box/uni-number-box.vue'
	
	export default {
		components:{uniTag,UniIcons,UniNumberBox},
		onLoad(e) {
			console.log(e); 
		},
		data() {

			return {
				exampleValue : "1000kg",
				exampleValue1 : "整桶备料",
				exampleValue2 : "100KG",
				numberValue : 0,
				scan : {
					"unicode": "e612"
				},
				obj:[
					{
						matDesc : "磷脂",
						planQty : "1000kg",
						actQty : "1000kg",
						batchNo : "磷脂",
						productArea : "秦皇岛",
						status : 60
					},
					{
						matDesc : "磷脂",
						planQty : "1000kg",
						actQty : "1000kg",
						batchNo : "磷脂",
						productArea : "秦皇岛",
						status : 30
					},
					{
						matDesc : "磷脂",
						planQty : "1000kg",
						actQty : "1000kg",
						batchNo : "磷脂",
						productArea : "秦皇岛",
						status : 20
					},
					{
						matDesc : "磷脂",
						planQty : "1000kg",
						actQty : "1000kg",
						batchNo : "磷脂",
						productArea : "秦皇岛",
						status : 60
					}
					
				],
				
				inputValue : "3123"
			}
		},
		methods: {
			switch2Change: function (e) {
				console.log('switch2 发生 change 事件，携带值为', e.detail.value)
			},
			change(value) {
				this.numberValue = value
			},
			
			enter(){
				uni.showToast({
					title:"回车事件",
					icon:"none"
				})
			},
			reset(){
				this.inputValue = ""
			},
			getValue(){
				this.request({
					url : '/prd/getKeyTest',
					data : this.iuputValue,
					success: res=>{
						this.obj = res.data
						uni.showToast({
							title:"请求成功",
							duration:3000
						})
					},
					error: res=>{
						console.log(res.data)
					}
				})
			},
			async scanCode() {
				let status = await this.checkPermission();
				if (status !== 1) {
				    return;
				}
				var that = this
				uni.scanCode({
					success: (res) => {
						that.strQRCode = res.result;
							console.log(that.strQRCode);
						    that.act(that.strQRCode);
					},
					fail: (err) => {
						
						uni.getSetting({
							success: (res) => {
								let authStatus = res.authSetting['scope.camera'];
								if (!authStatus) {
									uni.showModal({
										title: '授权失败',
										content: 'Hello uni-app需要使用您的相机，请在设置界面打开相关权限',
										success: (res) => {
											if (res.confirm) {
												uni.openSetting()
											}
										}
									})
								}
							}
						})
					
					}
				});
			}
			,
			async checkPermission(code) {
				let status = permision.isIOS ? await permision.requestIOS('camera') :
					await permision.requestAndroid('android.permission.CAMERA');
			
				if (status === null || status === 1) {
					status = 1;
				} else {
					uni.showModal({
						content: "需要相机权限",
						confirmText: "设置",
						success: function(res) {
							if (res.confirm) {
								permision.gotoAppSetting();
							}
						}
					})
				}
				return status;
			},
			showStyle(status) {
			    if (status == 60) {
			        return "green";
			    }else if(status == 30){
					return "yellow";
				};
			},
		}
	}
</script>
