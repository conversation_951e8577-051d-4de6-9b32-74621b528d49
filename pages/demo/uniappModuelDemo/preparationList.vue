<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-list">
				<!-- <checkbox-group @change="checkboxChange"> -->
				<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in matList" :key="index">
					<view class="uni-media-list" @click="turnToDetails(item)">
						<view class="uni-triplex-row">
								<view class="uni-triplex-left-80">
									<view class="page-list-left-row cu-form-data-17"><text>{{item.mat_desc}}({{item.mat_no}})__{{item.qty}}</text></view>
									<view class="page-list-left-row cu-form-data-17"><text>{{item.productArea}}/{{item.batchNo}}</text></view>
								</view>
								<view class="uni-triplex-right">
									<text class="uni-panel-icon uni-icon alin_x_center">&#xe470;</text>
								</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 错误信息
				</view>
			</view>
			
			<view class="cu-form-group text-box cu-form-data-17" scroll-y="true">
				<text>{{textArea}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
	
		onLoad(e) {
			console.log(e); //result 0提交成功  1 未查询到随工单 2 返回按钮
			this.queryOrder();
		},
		data() {

			return {
				now: new Date(),
				index: -1,
				matList: [{
					mat_no: '1002021400',
					mat_desc: '大豆油',
					qty: '120kg',
					productArea: '秦皇岛金海益海嘉里',
					batchNo: '20201121'
				}, {
					mat_no: '1002021400',
					mat_desc: '磷脂',
					qty: '120kg',
					productArea: '秦皇岛金海益海嘉里',
					batchNo: '20201121'
				}, {
					mat_no: '1002021400',
					mat_desc: '磷脂',
					qty: '120kg',
					productArea: '秦皇岛金海益海嘉里',
					batchNo: '20201121'
				}],
				textArea: "sddddddddddddddddddsdd十大发射点法大师傅发的发射点999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999"
				
				,

			}
		},
		methods: {
			formatState(status) {
				var stateDese = "";
				switch (status) {
					case 10:
						stateDese = "待执行";
						break;
					case 20:
						stateDese = "已分配";
						break;
					case 30:
						stateDese = "待确认";
						break;
					case 40:
						stateDese = "已确认";
						break;
					default:
						stateDese = "";
						break;
				}
				return stateDese;
			},
			queryOrder() {

			}
		}
	}
</script>
