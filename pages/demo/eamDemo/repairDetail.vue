<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="title">报修单号：</view>
				<input v-model="repairNo" />
			</view>
			<view class="cu-form-group">
				<view class="title">设备编码：</view>
				<input v-model="equipNo" />
			</view>
			<view class="cu-form-group">
				<view class="title">设备名称：</view>
				<input v-model="equipDesc" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">报修源：</view>
				<input v-model="repairSource"/>
			</view>
			<view class="cu-form-group">
				<view class="title">报修源单号：</view>
				<input v-model="repairSourceNo"  />
			</view>
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 故障现象
				</view>
			</view>
			<view class="cu-form-group">
				<textarea v-model="repairText" placeholder="输入字体" />
			</view>
			<view class="cu-form-group">
				<view class="title">报修人：</view>
				<input v-model="userName" @confirm="getUser()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<!-- 预览报修图片 -->
			<view class="group">
				<view class="uni-list list-pd">
					<view class="uni-list-cell cell-pd">
						<view class="uni-uploader">
							<view class="uni-uploader-head">
								<view class="uni-uploader-title">点击可预览图片</view>
								<view class="uni-uploader-info">{{repairImageList.length}}/4</view>
							</view>
							<view class="uni-uploader-body">
								<view class="uni-uploader__files">
									<block v-for="(image,index) in repairImageList" :key="index">
										<view class="uni-uploader__file">
											<image class="uni-uploader__img" :src="image" :data-src="image"
												@tap="previewImage"></image>
										</view>
									</block>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 维修 -->
			<radio-group class="cu-form-group" @change="radioChange">
				<label class="cu-form-group" v-for="(item, index) in items" :key="item.value">
					<view>{{item.name}}&nbsp;&nbsp;&nbsp;</view>
					<view>
						<radio :value="item.value" :checked="index === current" />
					</view>
				</label>
			</radio-group>
			<view class="cu-form-group">
				<view class="title">备件编号：</view>
				<input v-model="spareNo" />
			</view>
			<view class="cu-form-group">
				<view class="title">备件名称：</view>
				<input v-model="spareDesc" />
			</view>
			<view class="cu-form-group">
				<view class="title">备件数量：</view>
				<input v-model="spareQty" />
			</view>
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 处理方案
				</view>
			</view>
			<view class="cu-form-group">
				<textarea v-model="text" placeholder="输入字体" />
			</view>
			<!-- 上传图片 -->
			<view class="group">
				<view class="uni-list list-pd">
					<view class="uni-list-cell cell-pd">
						<view class="uni-uploader">
							<view class="uni-uploader-head">
								<view class="uni-uploader-title">点击可预览选好的图片</view>
								<view class="uni-uploader-info">{{imageList.length}}/4</view>
							</view>

							<view class="uni-uploader-body">
								<view class="uni-uploader__files">
									<block v-for="(image,index) in imageList" :key="index">
										<view class="uni-uploader__file">
											<image class="uni-uploader__img" :src="image" :data-src="image"
												@tap="previewImage"></image>
										</view>
									</block>
									<view class="uni-uploader__input-box">
										<view class="uni-uploader__input" @tap="chooseImage"></view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-form-group">
				<view class="title">维修人：</view>
				<input v-model="userNameD" @confirm="getUser()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<uni-tag text="清空" type="warning" @click="cancel" />
				<uni-tag text="确认报修" type="success" @click="submit()" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	var sourceType = [
		['camera'],
		['album'],
		['camera', 'album']
	]
	var sizeType = [
		['compressed'],
		['original'],
		['compressed', 'original']
	]
	export default {
		components: {
			uniTag
		},
		data() {
			return {
				// 维修
				repairImageList:[],
				items: [
					{
						value: 'true',
						name: '需要备件'
					},
					{
						value: 'false',
						name: '不需要备件'
					}
				],
				current: 0,
				spareNo:'',
				spareDesc:'',
				spareQty:'',
				text:'',
				userNameD:'',
				// 报修
				repairNo: '',
				equipNo: '',
				equipDesc: '',
				repairSource: '',
				repairSourceNo: '',
				repairText: '',
				userName: '',
				/* 图片参数 */
				imageList: [],
				imageDataList:[],
				sourceTypeIndex: 2,
				sourceType: ['拍照', '相册', '拍照或相册'],
				sizeTypeIndex: 2,
				sizeType: ['压缩', '原图', '压缩或原图']
			}
		},
		methods: {
			// 获取维修明细
			getDetailInfo() {

			},
			//
			sourceTypeChange: function(e) {
				this.sourceTypeIndex = e.target.value
			},
			sizeTypeChange: function(e) {
				this.sizeTypeIndex = e.target.value
			},
			countChange: function(e) {
				this.countIndex = e.target.value;
			},
			chooseImage: async function() {
				// #ifdef APP-PLUS
				// TODO 选择相机或相册时 需要弹出actionsheet，目前无法获得是相机还是相册，在失败回调中处理
				if (this.sourceTypeIndex !== 2) {
					let status = await this.checkPermission();
					if (status !== 1) {
						return;
					}
				}
				// #endif

				if (this.imageList.length === 4) {
					let isContinue = await this.isFullImg();
					console.log("是否继续?", isContinue);
					if (!isContinue) {
						return;
					}
				}

				uni.chooseImage({
					sourceType: sourceType[this.sourceTypeIndex],
					sizeType: sizeType[this.sizeTypeIndex],
					count: this.imageList.length > 0 ? 4 - this.imageList.length : 4,
					success: (res) => {
						this.imageList = this.imageList.concat(res.tempFilePaths);
						pathToBase64(res.tempFilePaths)
							.then(base64 => {
								this.imageDataList = this.imageDataList.concat(base64);
								console.log(base64);
							})
							.catch(error => {
								console.error(error);
							})
					},
					fail: (err) => {
						// #ifdef APP-PLUS
						if (err['code'] && err.code !== 0 && this.sourceTypeIndex === 2) {
							this.checkPermission(err.code);
						}
						// #endif
					}
				})
			},
			isFullImg: function() {
				return new Promise((res) => {
					uni.showModal({
						content: "已经有4张图片了,是否清空现有图片？",
						success: (e) => {
							if (e.confirm) {
								this.imageList = [];
								this.imageDataList = [];
								res(true);
							} else {
								res(false)
							}
						},
						fail: () => {
							res(false)
						}
					})
				})
			},
			previewImage: function(e) {
				var current = e.target.dataset.src
				uni.previewImage({
					current: current,
					urls: this.imageList
				})
			},
			// radio
			radioChange(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			submit(){
				
			}
		}
	}
</script>
