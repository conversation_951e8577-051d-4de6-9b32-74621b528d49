<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="title">报修单号：</view>
				<input v-model="repairNo" @confirm="getRepairNo()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="title">设备编码：</view>
				<input v-model="equipNo" @confirm="getEquipNo()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="title">设备名称：</view>
				<input v-model="equipDesc" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="margin-top"></view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in equipList"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full " @click="getDetailInfo(item.equipNo)">
					<view style="width: 20%; ">
						<image style="width: 50px; height: 50px;" :src="item.image"></image>
					</view>
					<view style="width: 70%;">
						<view class = "text-black cu-form-data-10" >{{item.equipDesc}}</view>
						<view class = "text-gray cu-form-data-10" >{{item.equipNo}}</view>
						<view class = "text-gray cu-form-data-10" >{{item.repairTime}}</view>
					</view>
					<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
				</view>
			</view>
			<view style="height: 100upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom">
				<uni-tag text="查询" type="success" @click="submit" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				repairNo: '',
				equipNo: '',
				equipDesc: '',
				equipList:[
					{
						image:'../../static/images/none.jpg',
						equipDesc:'液压机BC01',
						equipNo:'SP124321',
						repairTime:'2022-5-19'
					},
					{
						image:'../../static/images/none.jpg',
						equipDesc:'水泵BC01',
						equipNo:'SP124321',
						repairTime:'2022-5-19'
					}
				]
			}
		},
		methods: {
			// 获取报修任务详情
			getDetailInfo(equipNo){
				uni.navigateTo({
					url:'repairDetail?equipNo='+equipNo+'&repairNo='+this.repairNo
				})
			}
		}
	}
</script>

<style>

</style>
