<template>
	<view>
		<button @click="searchBle">搜索蓝牙</button>
		<view style="margin-top: 30upx;" :key="index" v-for="(item,index) in devices">
			<button style="width: 400upx; color: #0081FF;" @click="onConn(item)">{{item.name}}</button>
		</view>
		<button style="margin-top: 100upx;" @click="senBleLabel()">标签打印</button>
		
		<button style="margin-top: 100upx;" @click="jumpOld()">BLE蓝牙</button>
		<textarea auto-height placeholder-style="color:#F76260" placeholder="请输入票据信息" v-model="piaojuText" />
		<button style="margin-top: 100upx;" @click="senBleLabel2()">票据打印</button>
				
		<canvas :style="{width: canvasWidth +'px', height: canvasHeight +'px'}" canvas-id="firstCanvas" id="firstCanvas" class="firstCanvas" />
	</view>


</template>

<script>
	import tsc from '@/static/libs/tsc.js'
	import esc from '@/static/libs/esc.js'
	import bluetoothTool from '@/static/libs/BluetoothTool.js'
	
	export default {
		data() {
			return {
				devices: [],
				currDev: null,
				connId: '',
				piaojuText:'',
				
				tableDomId: '',
				tableImgPath: '',
				
				canvasWidth: 800,
				canvasHeight: 600,
				
				msg: ''
			}
		},
		watch:{
			msg(){
				uni.showToast({
					title: this.msg
				})
			}
		},
		onReady() {
			this.renderCanvas()
		},
		mounted() {
			//#ifdef APP-PLUS
				// 蓝牙
				bluetoothTool.init({
					listenBTStatusCallback: (state)=> {
						if(state == 'STATE_ON') {
							let lastBleAddress = uni.getStorageSync('lastBleAddress')
							if(lastBleAddress) {
								uni.showLoading({
									title: '正在连接...'
								})
								console.log(lastBleAddress)
								bluetoothTool.connDevice(lastBleAddress,(result)=>{
									uni.hideLoading()
									uni.showToast({
										title: result?'连接成功！':'连接失败...'
									});
								})
							}
						}
					},
					discoveryDeviceCallback: this.onDevice,
					discoveryFinishedCallback: function() {
						that.msg = "搜索完成";
					},
					readDataCallback: function(dataByteArr) {
						// 读取蓝牙返回的数据
						/* if(that.receiveDataArr.length >= 200) {
							that.receiveDataArr = [];
						}
						that.receiveDataArr.push.apply(that.receiveDataArr, dataByteArr); */
					},
					connExceptionCallback: function(e) {
						console.log(e);
						that.msg = "设备连接失败";
					}
				});
			//#endif
		},
		methods: {
			destroyed: function() {
				console.log("destroyed----------")
				if (this.connId != '') {
					uni.closeBLEConnection({
						deviceId: this.connId,
						success(res) {
							console.log(res)
						}
					})
				}

			},
			searchBle() {
				var that = this
				console.log("initBule")
				// 使用openBluetoothAdapter 接口，免去主动申请权限的麻烦
				uni.openBluetoothAdapter({
					success(res) {
						this.devices = []
						console.log("打开 蓝牙模块，开始搜索模式...")
						console.log(res)
						bluetoothTool.discoveryNewDevice();
						//that.onDevice()
					}
				})
			},
			onDevice(newDevice){
				console.log("监听寻找到新设备的事件---------------")
				console.log(newDevice)
				if(newDevice.name && newDevice.name != 'null') {
					this.devices.push({
						name: newDevice.name,
						address: newDevice.address
					})
				}
			},
			stopFindBule() {
				console.log("停止搜寻附近的蓝牙外围设备---------------")
				uni.stopBluetoothDevicesDiscovery({
					success(res) {
						console.log(res)
					}
				})
			},
			onConn(item) {
				console.log("连接蓝牙---------------" + item.address)
				
				bluetoothTool.connDevice(item.address,(result)=>{
					if(result) {
						uni.setStorageSync('lastBleAddress', item.address)
					}
					console.log('连接结果：',result)
				});
			},
			senBleLabel() {				
				//标签模式
				uni.canvasGetImageData({
					canvasId: 'firstCanvas',
					x: 0,
					y: 0,
					width: this.canvasWidth,
					height: this.canvasHeight,
					success: (res)=> {
						uni.hideLoading()
						
						var command = tsc.jpPrinter.createNew()
						command.init()
						command.setSize(80, 60)
						command.setGap(2)
						command.setCls()
						command.setText(50, 10, "TSS24.BF2", 1, 1, "打印测试")
						command.setQR(50, 50, "L", 5, "A", "https://www.baidu.com")
						command.setBitmap(200, 0, 0, res)
						command.setBox(100, 100, 700, 500, 1)
						command.setPagePrint()
						let data = command.getData()
						bluetoothTool.sendByteData(data)
						console.log('发送完毕')
					},
					fail: function(res) {
						console.log(res)
					}
				})
				
			},
			senBleLabel2(){
				//票据模式
				var command = esc.jpPrinter.createNew()
				command.init()
                command.setText(this.piaojuText);
                command.setPrintAndFeedRow(1)
				bluetoothTool.sendData(command.getRawData())
			},
			renderCanvas() {
				let filePath = '../../static/xtHead.jpg'
				let that = this
				const firstCanvas = uni.createCanvasContext('firstCanvas', this);
				let scla = 1
				uni.getImageInfo({
					src: filePath,
					success(res) {
						console.log(res.width + "--" + res.height)
						that.canvasWidth = res.width * scla
						that.canvasHeight = res.height * scla
						console.log(res, that.canvasWidth, that.canvasHeight)
						firstCanvas.drawImage(filePath, 0, 0, that.canvasWidth, that.canvasHeight);
						firstCanvas.draw();
						that.$nextTick(() => { //获取画布像素数据
							
						})
					},
					fail(res) {
						console.log(res)
					}
				})
			},
			jumpOld () {
				uni.navigateTo({
					url: './miniAppPrint'
				})
			}
		}
	}
</script>

<style>

</style>
