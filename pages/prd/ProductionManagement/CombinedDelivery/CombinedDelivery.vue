<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<text style="color: #E3162E;">* </text>
				<view class="title">产线选择</view>
				<input v-model="LineName" disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-riqi_o xj_form_icon" />
				<text style="color: #E3162E;">* </text>
				<view class="title">日期选择</view>
				<picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange">
					<view style="color: red;">{{date}}</view>
				</picker>
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title">工单号</view>
				<input v-model="orderNo" @confirm="queryOrder()" disabled="true" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="queryCarrierInfo()" :focus="focusCarrier" placeholder="请手动扫描载具码" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<text class="cuIcon-titles text-xj text-sm">当前物料：{{currentMaterialName}}</text>
			</view>
			<view style="display: flex;flex-direction: row;">
				<view class="cu-form-group">
					<view class="title">已扫数量</view>
					<input disabled="true" v-model="scanedQty" />
				</view>
				<view class="cu-form-group">
					<view class="title">未扫数量</view>
					<input disabled="true" v-model="scanQty" />
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<text class="cuIcon-titles text-xj text-sm">工单：</text>
			</view>
			<view class="text-black cu-form-group" v-show="WOS.length">
				<view class="ds_row">
					<!-- <view style="height: 20%;">工单号：</view> -->
					<view style="height: 80%;margin-left: 2px;" v-for="(item,index) in WOS" :key="index">
						{{item}}
					</view>
				</view>
			</view>
			<view class="action margin-top" v-show="msg !== ''">
				<text class="cuIcon-title text-xj "></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<!-- 扩展 -->
			<view class="margin-top">
				<uni-segmented-control :current="current" :values="items" :style-type="styleType"
					:active-color="activeColor" @clickItem="onClickItem" />
				<view v-if="current === 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover"
						v-for="(item,index) in carrierMatinfo" :key="index">
						<view class="cu-form-group solid-bottom panel-full" @longtap="deleteItem(index)">
							<view class="content">
								<view class="text-black cu-form-data-10">工单号：{{item.WO}} </view>
								<view class="text-black cu-form-data-10">载具码：{{item.CarrierCode}} </view>
								<view class="text-black cu-form-data-10">料号：{{item.MaterialCode}} </view>
								<view class="text-black cu-form-data-10">名称：{{item.MaterialName}}</view>
								<view class="text-black cu-form-data-10">规格：{{item.Specification}}</view>
								<view class="text-black cu-form-data-10">数量：{{item.Qty}}</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="current === 1">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in WODetail"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full " :class="showStyle(item.ActualQty)">
							<view class="content">
								<view class="text-black cu-form-data-10">料号：{{item.MaterialCode}} </view>
								<view class="text-black cu-form-data-10">名称：{{item.MaterialName}}</view>
								<view class="text-black cu-form-data-10">规格：{{item.Specification}}</view>
								<view class="text-black cu-form-data-10">未发数量：{{item.ActualQty}}</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="current === 2">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in MaterialQtys"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full " :class="showStyle(item.ActualQty)">
							<view class="content">
								<view class="text-black cu-form-data-10">料号：{{item.MaterialCode}} </view>
								<view class="text-black cu-form-data-10">名称：{{item.MaterialName}}</view>
								<view class="text-black cu-form-data-10">规格：{{item.Specification}}</view>
								<view class="text-black cu-form-data-10">待发料数：{{item.ActualQty}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">全部清空</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="sendMaterial">确认发料
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				focusCarrier: false,
				currentMaterialId: null,
				currentMaterialName: '',
				currentMaterialQty: null,
				// 产线
				LineCode: '',
				LineName: '',
				LineId: null,
				// 载具码
				carrierNo: '',
				// 工单
				orderNo: '',
				orderName: '',
				orderId: '',
				/* 发料单详情 */
				// 工单
				// WOS: new Set(),
				WOS: [],
				WOSID: [],
				WODetail: [],
				// 发料载具
				carrierMatinfo: [],
				// 工单物料发料数
				MaterialQtys:[],
				// 时间筛选
				date: currentDate,
				// 消息提示
				msg: '',
				// 滑动模块
				items: ['扫描载具详情', '工单发料详情','工单物料发料数'],
				current: 0,
				colorIndex: 0,
				activeColor: '#009598',
				styleType: 'button'
			}
		},
		onLoad() {
			this.clearCach();
		},
		onShow() {
			let that = this;
			uni.getStorage({
				key: 'ordersInfo',
				success: async o => {
					await this.arrAssignment(o.data);
					this.orderNo = o.data.orderNo; // 这取值感觉有问题
					this.orderName = o.data.orderName; // 这取值感觉有问题
					this.orderId = o.data.Id; // 这取值感觉有问题
					if (this.WOS.length != 0) {
						that.dealArr();
						that.queryOrders();
						that.GetsMaterialQty();
					}
				}
			})
			uni.getStorage({
				key: 'lineInfo',
				success: o => {
					this.LineId = o.data.id;
					this.LineCode = o.data.LineCode;
					this.LineName = o.data.LineName;
				}
			})
		},
		computed: {
			/* date picker */
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			},
			// 已扫数量、未扫数量
			scanedQty() {
				let qty = 0;
				this.carrierMatinfo.forEach(item => {
					if (item.MaterialId == this.currentMaterialId) {
						qty += item.Qty;
					}
				})
				return qty;
			},
			scanQty() {
				let that = this;
				let total = 0;
				for (let i = 0; i < that.WODetail.length; i++) {
					if (that.WODetail[i].MaterialId == that.currentMaterialId) {
						total = that.WODetail[i].ActualQty - that.scanedQty;
					}
				}
				return total;
			}
		},
		methods: {
			deleteItem(index) {
				let that = this;
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要删除该项吗？', function(e) {
					//0==确认，否则取消  
					if (e.index == 0) {
						that.carrierMatinfo.splice(index, 1);
					} else {

					}
				}, {
					"title": '提示',
					"buttons": btns
				});
				// #endif
			},
			// 处理工单相关数组
			async dealArr() {
				this.WOS = await this.uniqueS(this.WOS)
				this.WOSID = await this.uniqueS(this.WOSID)
			},
			// 工单相关数组赋值
			arrAssignment(orders) {
				let self=this;
				return new Promise(resolve => {
					// orders.forEach(function (item, index) {
					// 	self.WOS.push(item.Wo)
					// 	self.WOSID.push(item.Id)					
					// });
					
					// 用最新的选中值覆盖
					let WOS = []
					let WOSID = []
					orders.forEach(function (item, index) {
						WOS.push(item.Wo)
						WOSID.push(item.Id)					
					});
					self.WOS = WOS
					self.WOSID = WOSID
					resolve('success')
				})
			},
			// 数组去重
			uniqueS(arr) {
				return new Promise(resolve => {
					resolve(Array.from(new Set(arr)));
				})
			},
			// 清除本地缓存
			clearCach() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.removeStorage({
					key: 'lineInfo'
				})
			},
			// 查询工单发料详情
			queryOrders() {
				this.request({
					url: '/ShipOrderDetail/GetPlanQty',
					method: 'POST',
					data: this.WOS,
					success: res => {
						this.WODetail = res.response.data;
					},
					error: res => {
						uni.showToast({
							title: '异常！',
							icon: 'error'
						})
						this.msg = res.msg
					}
				})
			},
			GetsMaterialQty(){
				this.request({
					url: '/ShipOrderDetail/GetsMaterialQty',
					method: 'POST',
					data: this.WOS,
					success: res => {
						this.MaterialQtys = res.response.data;
					},
					error: res => {
						uni.showToast({
							title: '异常！',
							icon: 'error'
						})
						this.msg = res.msg
					}
				})
			},
			getLine() {
				uni.removeStorage({
					key: 'lineInfo'
				})
				uni.navigateTo({
					url: '../../common/productionLine/productionLine'
				})
			},
			// 查询工单
			queryOrder() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.navigateTo({
					url: '../../common/orderQuery/multipleOrderQuery?LineName=' + this.LineName + '&startDate=' + this
						.date + '&woStart=' + 10
				})
			},
			/* 查询备料单信息 */
			queryShipOrderDetail() {
				this.request({
					url: '/ShipOrderDetail/Get',
					method: 'GET',
					data: {
						key: this.orderNo,
						intPageSize: 9999,
						page: 1
					},
					success: res => {
						this.invoiceDetails = res.response.data;
					},
					error: res => {
						uni.showToast({
							title: '异常！',
							icon: 'error'
						})
						this.msg = res.msg
					}
				})
			},
			/* 查询备料单发货载具信息 */
			queryProductionIssueDetail() {
				this.request({
					url: '/ProductionIssueDetail/Gets',
					method: 'GET',
					data: {
						wo: this.orderNo,
						intPageSize: 9999,
						page: 1
					},
					success: res => {
						this.vehicleDetails = res.response.data;
					},
					error: res => {
						uni.showToast({
							title: res.msg,
							icon: 'error'
						})
						this.msg = res.msg
					}
				})
			},
			// 查询载具信息
			queryCarrierInfo() {
				if (this.WOS.length == 0) {
					uni.showToast({
						title: '请先选择工单',
						icon: 'loading',
						duration: 500
					})
					this.orderNo = '';
					return
				}
				if (this.carrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.syncRequest({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo,
					},
					success: async res => {
						if (res.response === null) {
							uni.showToast({
								title: '未查询到数据',
								icon: 'error',
								duration: 1000
							})
							this.carrierNo = '';
							this.focus = true;
							this.msg = '未查询到数据';
							return;
						}
						if (res.response.IsInStorage === false) {
							uni.showToast({
								title: '当前载具不在仓库',
								icon: 'error',
								duration: 1000
							})
							this.carrierNo = '';
							this.focus = true;
							this.msg = '当前载具不在仓库';
							return;
						}
						if (res.response.IsInStorage === false) {
							uni.showToast({
								title: '当前载具不在仓库',
								icon: 'error',
								duration: 1000
							})
							this.carrierNo = '';
							this.msg = '当前载具不在仓库';
							this.focus = true;
							return;
						}
						if (res.response.Qty === 0) {
							uni.showToast({
								title: '当前载具无物料',
								icon: 'error',
								duration: 1000
							})
							this.carrierNo = '';
							this.msg = '当前载具无物料';
							this.focus = true;
							return;
						}
						this.currentMaterialId = res.response.MaterialId;
						this.currentMaterialName = res.response.MaterialName;
						this.currentMaterialQty = res.response.Qty;
						await this.isGetMateils();
						await this.isExcess();
						this.focus = true;
						let temp = {
							Id: res.response.CarrierId,
							CarrierCode: res.response.CarrierCode,
							WO: res.response.WO,
							Status: res.response.Status,
							MaterialCode: res.response.MaterialCode,
							MaterialId: res.response.MaterialId,
							MaterialName: res.response.MaterialName,
							Specification: res.response.Specification,
							Qty: res.response.Qty,
							MaterialId: res.response.MaterialId,
							IsFeeding: res.response.IsFeeding,
							IsInStorage: res.response.IsInStorage
						};
						if (this.isRepeat(res.response.CarrierCode, this.carrierMatinfo)) {
							this.carrierMatinfo.push(temp);
							this.carrierNo = '';
							this.focus = true;
							this.msg = res.msg;
						} else {
							uni.showToast({
								title: '重复扫码！',
								icon: 'error'
							})
						}
					},
					error: res => {
						this.msg = res.msg;
						// this.cancel();
					}
				})
			},
			// 判断扫描物料 是否过量
			isExcess() {
				let that = this;
				// 已扫总数
				let scanedQty = 0;
				for (let i = 0; i < that.WODetail.length; i++) {
					if (that.WODetail[i].MaterialId == that.currentMaterialId) {
						that.carrierMatinfo.forEach((item, index) => {
							if (item.MaterialId == that.currentMaterialId) {
								scanedQty = item.Qty;
							}
						})
						scanedQty = that.currentMaterialQty + scanedQty;
						return new Promise((resolve, reject) => {
							if (that.WODetail[i].ActualQty < scanedQty) {
								that.msg = '扫描物料过量！';
								reject('fail')
							} else {
								resolve('success')
							}
						})
					}
				}
			},
			// 查询当前载具物料是否为工单所需 
			isGetMateils() {
				let that = this;
				return new Promise((resolve, reject) => {
					that.request({
						url: '/ProductionIssueDetail/GetMateils',
						method: 'POST',
						data: {
							wos: that.WOS,
							mars: [Number(that.currentMaterialId)]
						},
						success: res => {
							that.msg = res.msg;
							resolve('OK');
						},
						error: res => {
							uni.showToast({
								title: res.msg,
								icon: 'error'
							})
							that.msg = res.msg;
							reject('fail');
						}
					})
				})
			},
			/* 多个载具发料 */
			sendMaterial() {
				let that = this;
				if (this.WOS.length == 0) {
					uni.showToast({
						title: '请先选择工单！',
						icon: 'error'
					})
				} else if (this.carrierMatinfo.length == 0) {
					uni.showToast({
						title: '请扫码载具！',
						icon: 'error'
					})
				} else {


					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要提交吗？', function(e) {
						//0==确认，否则取消  
						if (e.index == 0) {
							that.reqMergePost()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要提交吗？',
						success: function(res) {
							if (res.confirm) {
								that.reqMergePost()
							} else if (res.cancel) {}
						}
					})
					// #endif

				}
			},
			// 合并发料请求
			reqMergePost() {
				this.request({
					url: '/ProductionIssueDetail/MergePost',
					method: 'POST',
					data: {
						WO: this.WOS,
						WOS: this.WOSID,
						carriers: this.carrierMatinfo
					},
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'success'
						})
						this.msg = res.msg;
						this.cancelStandard();
					},
					error: res => {
						uni.showToast({
							title: res.msg,
							icon: 'error'
						})
						this.msg = res.msg
					}
				})
			},
			//
			isRepeat(carrierCode) {
				for (let i = 0; i < this.carrierMatinfo.length; i++) {
					if (carrierCode == this.carrierMatinfo[i].CarrierCode) {
						return false;
					} else {
						return true;
					}
				}
				return true
			},
			/* 查询物料载具详情 */
			detailInfo() {
				console.log("详情展示");
			},
			// 取消，初始化页面
			cancel() {
				let that = this;
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要清空吗？', function(e) {
					console.log("Close confirm: " + e.index);
					//0==确认，否则取消  
					if (e.index == 0) {
						that.LineCode = '';
						that.LineName = '';
						that.LineId = null;
						that.currentMaterialId = null;
						that.currentMaterialName = '';
						that.currentMaterialQty = null;
						that.carrierNo = '';
						that.orderNo = '';
						// that.msg = '';
						that.carrierMatinfo = [];
						that.WOS = [];
						that.WOSID = [];
						that.clearCach();
					} else {

					}
				}, {
					"title": '提示',
					"buttons": btns,
					// "verticalAlign":"bottom"
				});
				// #endif
			},
			cancelStandard() {
				this.focusCarrier = true;
				this.LineCode = '';
				this.LineName = '';
				this.LineId = null;
				this.currentMaterialId = null;
				this.currentMaterialName = '';
				this.currentMaterialQty = null;
				this.carrierNo = '';
				this.orderNo = '';
				this.carrierMatinfo = [];
				this.WOS = [];
				this.WOSID = [];
				this.clearCach();
			},
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex
				}
			},
			showStyle(status) {
				if (status !== 0) {
					return "yellow";
				} else {
					return "green";
				}
			},
			/* date picker */
			bindDateChange: function(e) {
				this.date = e.detail.value
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			onNavigationBarButtonTap(e) {
					if(!this.WOS||this.WOS.length<=0)
					{
						this.$error("请先选择工单！")
						return;
					}
					if(!this.carrierMatinfo||this.carrierMatinfo.length<=0)
					{
						this.$error("请扫载具码！")
						return;
					}
					debugger;
					var wo="";
					for (var i=0;i<this.WOS.length;i++)
					{ 
					    wo=wo+this.WOS[i]+",";
					}
					wo=wo.replace(/,$/gi,"");
					var carriers="";
					for (var i=0;i<this.carrierMatinfo.length;i++)
					{ 
					    carriers=carriers+this.carrierMatinfo[i].CarrierCode+",";
					}
					carriers=carriers.replace(/,$/gi,"");
				if (e.index == 0) {
					let pageUrl = '../../../index/miniAppPrint?wo=' + wo + '&carrierNo=' + carriers;
					uni.navigateTo({
						url: pageUrl
					});
				}
			},
		}
	}
</script>

<style>
	.ullist {
		display: flex;
		justify-content: space-around;
		font-size: 30rpx;
		color: $uni-text-color;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		background-color: #fff;
		width: 100%;
	}

	.active {
		color: $uni-color-primary;
		border-bottom: 4rpx solid $uni-color-primary;
	}
</style>
