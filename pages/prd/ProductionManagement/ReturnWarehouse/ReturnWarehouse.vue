<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title">部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title">产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25" />
			</view>
			<view class="cu-form-group" @click="querywarehouseList()">
				<view class="iconfont xj-cangku xj_form_icon" />
				<view class="title">退料仓库</view>
				<input v-model="storageLocation" disabled="true" placeholder="请手动选择仓库" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25" />
			</view>
			<view class="cu-form-group" @click="queryReturnOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title">退料单明细</view>
				<input v-model="returnOrderString" disabled="true" placeholder="请手动选择退料单明细" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25" />
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in returnOrder" :key="index">
				<view class="cu-form-group solid-bottom panel-full padding-xs">
					<view style="width: 80%">
						<view class="text-black ds uni-ellipsis">退料单号：{{ item.ProdReturnOrder }}</view>
						<view class="text-black uni-ellipsis">物料名称：{{ item.MaterialName }}(数量：{{ item.Qty }})</view>
						<view class="text-black uni-ellipsis">物料规格：{{ item.Specification }}</view>
						<view class="text-black uni-ellipsis">退料时间：{{ item.CreateTime }}</view>
						<view class="text-black uni-ellipsis">退料人：{{ item.CreateUserName }}</view>
					</view>

					<view>
						<text style="font-size: 25px; color: #8f8f94" @click="remove(item)">x</text>
					</view>
				</view>
			</view>
		</view>
		<view style="height: 200upx">
			<!-- 撑一下 -->
		</view>
		<view class="uni-fixed-bottom xj_button_group margin-top">
			<view class="xj_button" style="width: 40%; color: black" @click="cancel">全部清空</view>
			<view class="xj_button" style="width: 60%; background-color: #009598" @click="submit">确认提交</view>
		</view>
	</view>
</template>

<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
import vTabs from '@/components/v-tabs/v-tabs.vue';
export default {
	components: {
		uniTag,
		UniIcons,
		vTabs
	},
	data() {
		return {
			returnNo: '',
			// 退料类型
			radio: null,
			returnOrderString: '',
			returnType: [
				{
					text: '工废',
					value: 1
				},
				{
					text: '料废',
					value: 2
				},
				{
					text: '合格',
					value: 3
				}
			],
			obj: []
		};
	},
	computed: {
		// 工单信息
		woId() {
			return this.$store.state.wo.wo.woId;
		},
		wo() {
			return this.$store.state.wo.wo.wo;
		},
		woName() {
			return this.$store.state.wo.wo.woName;
		},
		// 产线
		LineCode() {
			return this.$store.state.line.line.lineCode;
		},
		LineName() {
			return this.$store.state.line.line.lineName;
		},
		LineId() {
			return this.$store.state.line.line.lineId;
		},
		// 车间、部门
		DepartmentId() {
			return this.$store.state.department.department.departmentId;
		},
		DepartmentCode() {
			return this.$store.state.department.department.departmentCode;
		},
		DepartmentName() {
			return this.$store.state.department.department.departmentName;
		},
		// 仓库
		storageId() {
			return this.$store.state.storage.storage.storageId;
		},
		storageCode() {
			return this.$store.state.storage.storage.storageCode;
		},
		storageLocation() {
			return this.$store.state.storage.storage.storageName;
		},
		// 退料单号列表
		returnOrder() {
			return this.$store.state.returnOrder.returnOrder;
		}
	},
	onLoad(e) {
		this.clearStore();
		// this.getReturnData();
	},
	onShow() {
		// this.getReturnData();
	},
	methods: {
		getDetailInfo(id, MaterialReturnType) {
			uni.navigateTo({
				url: 'materialDetail?ProdReturnMainId=' + id + '&MaterialReturnType=' + MaterialReturnType
			});
		},
		// 清空数据
		cancel() {
			const that = this;
			uni.showModal({
				title: '提示',
				content: '确定清空已选条件吗？',
				success: function (res) {
					if (res.confirm) {
						that.clearStore();
					} else if (res.cancel) {
					}
				}
			});
		},
		// 清 store
		clearStore() {
			this.$store.commit('department/empty');
			this.$store.commit('line/empty');
			this.$store.commit('storage/empty');
			this.$store.commit('wo/empty');
			this.$store.commit('returnMaterial/empty');
			this.$store.commit('radios/emptyRadioRerurnType');
			this.$store.commit('returnOrder/empty');
		},
		//
		change(e) {
			const that = this;
			this.getReturnData();
		},
		// 跳转仓库列表
		querywarehouseList() {
			uni.navigateTo({
				url: '../../common/warehouseList/warehouseList?warehouseCode=' + this.storageCode
			});
		},
		// 查询部门
		queryDepartment() {
			uni.navigateTo({
				url: '../../common/department/department'
			});
		},
		// 查询产线
		getLine() {
			uni.navigateTo({
				url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
			});
		},
		// 查询退料单
		queryReturnOrder() {
			uni.navigateTo({
				// url: '../../common/returnOrder/returnOrder?departmentId=' + this.DepartmentId + '&warehouseCode=' + this.storageCode + '&lineId=' + this.LineId
				url: '../../common/returnOrder/returnOrder' + '?warehouseCode=' + this.storageCode
			});
		},
		submit() {
			const that = this;
			/* 	if (that.DepartmentId == null) {
				uni.showToast({
					title: '未获取到部门信息',
					icon: 'none'
				});
			} else if (that.LineId == null) {
				uni.showToast({
					title: '未获取到产线信息',
					icon: 'none'
				});
			} else if (that.storageId == null) {
				uni.showToast({
					title: '请选择退料仓库',
					icon: 'none'
				});
			} else */ if (that.returnOrder.length == 0) {
				uni.showToast({
					title: '请至少选择一个退料单',
					icon: 'none'
				});
			} else {
				that.request({
					url: '/ProdReturn/HandleReturnOrder',
					method: 'POST',
					data: that.returnOrder,
					success: (res) => {
						that.$audio.materialReturn.success();
						that.$error('请求成功！' + res.response);
						that.$store.commit('returnOrder/empty');
					},
					error: (res) => {
						that.$audio.materialReturn.error();
						that.$error(res.response);
					}
				});
			}
		},
		remove(item) {
			this.$store.commit('returnOrder/remove', item);
		}
	}
};
</script>

<style></style>
