<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-datetime-picker v-model="range" type="daterange" @change="change" @maskClick="maskClick" />
			<uni-search-bar :focus="true" v-model="searchValue" @confirm="queryCreateWOByMyself"></uni-search-bar>
			<ds-none :obj="obj"></ds-none>
			<view class="uni-list" v-if="obj.length != 0">
				<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
					<view class="cu-form-group solid-bottom panel-full">
						<view class="content">
							<strong>
								<view>工单号：{{item.Wo}}</view>
							</strong>
							<view class="uni-ellipsis">产线：{{item.LineName}} </view>
							<view class="uni-ellipsis">物料编码：{{item.MaterialCode}} </view>
							<view class="uni-ellipsis">物料名称：{{item.MaterialName}} </view>
							<view class="uni-ellipsis">物料数量：{{item.PlanQty}} </view>
							<view class="uni-ellipsis">源载具：{{item.BarCode}} </view>
							<view class="uni-ellipsis">源工单：{{item.SourceWo}} </view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	export default {
		components: {
			dsNone
		},
		data() {
			return {
				obj: [],
				range: [],
				searchValue:''
			}
		},
		onLoad() {
			this.initDate()
			this.queryCreateWOByMyself()
		},
		methods: {
			initDate() {
				let begin = this.getDate({
					format: true
				});
				let end = this.getDate({
					format: true
				})
				this.range = [begin, end];
			},
			maskClick(e) {
				this.queryCreateWOByMyself();
			},
			change(e) {
				this.range = e
				this.queryCreateWOByMyself()
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
			
				if (type === 'start') {
					day = day - 30;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			// 查询自己当天所建工单
			queryCreateWOByMyself() {
				this.request({
					url: '/WOMain/QuerySelfWoMain',
					method: 'GET',
					data: {
						key: this.searchValue,
						timeSpace: this.range[0] + ',' + this.range[1]
					},
					success: res => {
						this.obj = res.response
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
		}
	}
</script>

<style>

</style>
