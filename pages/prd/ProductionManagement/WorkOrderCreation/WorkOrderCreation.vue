<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<view class="iconfont xj-renyuan xj_form_icon" />
				<view class="title">创建人</view>
				<input v-model="UserName" @confirm="getUserInfo" placeholder="自动获取" />
			</view>
			<view class="cu-form-group" @click="queryCreateWOByMyself()">
				<view class="iconfont xj-type xj_form_icon" />
				<view class="title">创建历史</view>
				<input disabled="true" placeholder="查看登录人员当天创建工单"/>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" v-if="DepartmentName && radio == 1">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title">部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="自动加载" />
			</view>
			<view class="cu-form-group" v-if="LineName && radio == 1">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title">产线</view>
				<input v-model="LineName" disabled="true" placeholder="自动加载" />
			</view>
			<view class="cu-form-group" v-if="materialCode">
				<view class="iconfont xj-bianmubianma xj_form_icon" />
				<view class="title">物料码</view>
				<input v-model="materialCode" disabled="true" placeholder="自动加载" />
			</view>
			<view class="cu-form-group" v-if="materialName">
				<view class="iconfont xj-mingcheng xj_form_icon" />
				<view class="title">物料名称</view>
				<input v-model="materialName" disabled="true" placeholder="自动加载" />
			</view>
			<view class="cu-form-group" v-if="specification">
				<view class="iconfont xj-guigeguanli xj_form_icon" />
				<view class="title">规格</view>
				<input v-model="specification" disabled="true" placeholder="自动加载" />
			</view>
			<view class="cu-form-group" v-if="barcodeQty != null">
				<view class="iconfont xj-icon_caigoushuliang xj_form_icon" />
				<view class="title">物料数量</view>
				<input v-model="barcodeQty" disabled="true" type="number" placeholder="自动加载,也可以手填" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-type xj_form_icon" />
				<view style="width: 20%;">类型</view>
				<uni-data-checkbox @change="change" mode="button" v-model="radio" :localdata="returnType" />
			</view>
			<view class="cu-form-group" @click="queryOrder()" v-if="isShow">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title"><text class="text-red">*</text>源工单号</view>
				<input v-model="wo" disabled="true" placeholder="请手动选择工单" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryDepartment()" v-if="!isShow">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title"><text class="text-red">*</text>部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()" v-if="!isShow">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title"><text class="text-red">*</text>产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" v-if="!isShow">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view style="width: 28%;" class="text-blue"><text class="text-red">*</text>物料码/载具码</view>
				<input v-model="scanCode" @confirm="getMaterialCode()" :focus="focus" />
				<uni-icons @click="clearMat" class="uni-panel-icon uni-icon alin_x_center" type="closeempty"
					color='#8f8f94' size="25" />
			</view>
			<view class="margin-top"></view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in sourceWOMM"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full" >
					<view class="content">
						<view class="uni-ellipsis">物料编码：{{item.MaterialCode}} </view>
						<view class="uni-ellipsis">物料名称：{{item.MaterialName}} </view>
						<view class="uni-ellipsis">物料规格：{{item.Specification}} </view>
						<view class="uni-ellipsis">物料数量：{{item.PlanQty}} </view>
					</view>
				</view>
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in matList" :key="index">
				<view class="cu-form-group solid-bottom panel-full">
					<view class="content">
						<strong>
							<view class="text-black cu-form-data-10">条码：{{item.scanCode}}</view>
						</strong>
						<view class="uni-ellipsis">物料编码：{{item.MaterialCode}} </view>
						<view class="uni-ellipsis">物料名称：{{item.MaterialName}} </view>
						<view class="uni-ellipsis">物料规格：{{item.Specification}} </view>
						<view class="uni-ellipsis">批次号：{{item.LotNo}} </view>
						<view class="uni-ellipsis">物料数量：{{item.qty}} </view>
						<view class="uni-ellipsis">扫码时间：{{item.date}} </view>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">清空</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submit">确认提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTime
	} from '@/js/productUtil.js';
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				scanCode: '',
				focus: false,
				// 数量
				barcodeQty: null,
				isShow: true,
				// 退料类型
				radio: 1,
				returnType: [{
					text: '源工单',
					value: 1
				}, {
					text: '扫码获取',
					value: 2
				}],
				// 创建人
				UserId: null,
				UserName: '',
				// 物料集合
				matList: [],
				matItem:null,
				// 消息提示
				msg: '',
			}
		},
		onLoad() {
			this.getUserInfo()
			// this.getOrderInfo()
			this.$clearStore(this)
			
			this.initPage();
		},
		onShow() {
			
		},
		computed: {
			total() {
				let temp = 0
				for (let item of this.matList) {
					temp = temp + item.qty
				}
				return temp
			},
			sourceWOMM() {
				return this.$store.state.createWOMM.createWOMM
			},
			sourceWO() {
				return this.$store.state.createWO.createWO.sourceWO
			},
			materialId() {
				return this.$store.state.createWO.createWO.materialId
			},
			materialCode() {
				return this.$store.state.createWO.createWO.materialCode
			},
			materialName() {
				return this.$store.state.createWO.createWO.materialName
			},
			specification() {
				return this.$store.state.createWO.createWO.specification
			},
			planQty: {
				get() {
					return this.$store.state.createWO.createWO.planQty
				},
				set(val) {
					this.updatePlanQty(val)
				}
			},
			// 车间、部门
			DepartmentId() {
				return this.$store.state.department.department.departmentId;
			},
			DepartmentCode() {
				return this.$store.state.department.department.departmentCode;
			},
			DepartmentName() {
				return this.$store.state.department.department.departmentName;
			},
			// 产线
			LineCode() {
				return this.$store.state.line.line.lineCode;
			},
			LineName() {
				return this.$store.state.line.line.lineName;
			},
			LineId() {
				return this.$store.state.line.line.lineId;
			},
			// 工单信息
			woId() {
				return this.$store.state.wo.wo.woId;
			},
			wo() {
				return this.$store.state.wo.wo.wo;
			},
			woName() {
				return this.$store.state.wo.wo.woName;
			}
		},
		methods: {
			// 获取条码 对应的物料数量
			queryBarcodeQty(){
				this.request({
					url: '/WOMain/QueryBarcodeQty',
					method: 'GET',
					data: {
						code: this.scanCode
					},
					success: res => {
						this.barcodeQty = res.response.qty
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询自己当天所建工单
			queryCreateWOByMyself(){
				uni.navigateTo({
					url:'./history'
				})
			},
			// 修改数量
			updatePlanQty(val) {
				this.$store.commit('createWO/setPlanQty', {
					planQty: val
				})
			},
			// 查询部门
			queryDepartment() {
				uni.navigateTo({
					url: '../../common/department/department?isProduct=' + '2'
				})
			},
			// 产线
			getLine() {
				uni.navigateTo({
					url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			},
			pushItem(item){
				let temp = {
					scanCode: item.scanCode,
					MaterialId: item.MaterialId,
					MaterialCode: item.MaterialCode,
					MaterialName: item.MaterialName,
					Specification: item.Specification,
					LotNo: item.LotNo,
					qty: item.Qty,
					date: getTime()
				}
				
				this.matList.push(temp)
				
				this.$store.commit('createWO/setCreateWO', {
					sourceWO: '',
					lineId: item.lineId,
					departmentId: item.departmentId,
					materialId: item.MaterialId,
					materialCode: item.MaterialCode,
					materialName: item.MaterialName,
					specification: item.Specification,
					LotNo: item.LotNo,
					planQty: item.Qty,
				})
			},
			// 删除
			deleteItem(index) {
				let that = this;
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要删除所选项吗？', function(e) {
					//0==确认，否则取消  
					if (e.index == 0) {
						that.matList.splice(index, 1);
					} else {

					}
				}, {
					"title": '提示',
					"buttons": btns,
				});
				// #endif
			},
			// 物料码
			getMaterialCode() {
				// debugger
				let app = this
				app.focus = false
				
				this.barcodeQty = null
				
				 if (app.scanCode.includes('@')) {
					let temp = app.scanCode.split('@')
					if (!temp.length <= 7 && !temp.length >= 6) {
						app.$toast('扫描的二维码，不符合产品码格式')
						app.scanCode = ''
						app.focus = true
						return;
					} else {
						const mc = temp[1]
						const qty = parseInt(temp[4])
						app.queryMat(mc, qty)
						app.queryBarcodeQty()
					}
				}
				else{
					app.queryCarrierInfo()
				}
				//zxt
				//  else if (app.scanCode.includes('ZJ')) {
				//  	app.queryCarrierInfo()
				//  } 
				//  else {
				// 	app.$toast('扫描代码格式异常')
				// }
			},
			// 查询载具信息
			queryCarrierInfo() {
				if (this.scanCode == '') {
					this.$toast('未获取到载具码')
					return
				}
				const app = this
				app.request({
					url: '/Carrier/GetCarrier2',
					method: 'GET',
					data: {
						carrierCode: app.scanCode,
					},
					success: res => {
						console.log('获取载具',res.response)
						let code = app.scanCode
						//载具只有一个物料
						if(res.response && res.response.length==1){
							let data = res.response[0]
													
							let temp = {
								scanCode: app.scanCode,
								MaterialId: data.MaterialId,
								MaterialCode: data.MaterialCode,
								MaterialName: data.MaterialName,
								Specification: data.Specification,
								LotNo: data.LotNo,
								qty: data.Qty,
								date: getTime()
							}
							if (app.matList.length === 0) {
								app.matList.push(temp)
								console.log('扫描的物料信息',app.matList)
								
								app.$store.commit('createWO/setCreateWO', {
									sourceWO: app.wo,
									lineId: app.LineId,
									departmentId: app.DepartmentId,
									materialId: data.MaterialId,
									materialCode: data.MaterialCode,
									materialName: data.MaterialName,
									specification: data.Specification,
									LotNo: data.LotNo,
									planQty: app.total,
								})
								app.scanCode = ''
								app.focus = true
							} else {
								app.$error('不允许扫描多个载具生成工单', () => {
									app.scanCode = ''
									app.focus = true
								})
							}
										
						}
						//载具有多个物料
						else if(res.response && res.response.length>1){
							
							uni.setStorageSync('WorkOrderMaterial', res.response)
							
							let url = '../WorkOrderCreation/ContainerMaterial?scanCode='+code+'&lineId=' + app.LineId+'&departmentId='+app.DepartmentId
							
							console.log('多个物料',url,res.response)
							
					
							uni.navigateTo({
								url: '../WorkOrderCreation/ContainerMaterial?scanCode='+app.scanCode+'&lineId=' + app.LineId+'&departmentId='+app.DepartmentId
							})
							
							app.scanCode = ''
							app.focus = true
						}
						else{
							app.$error('载具中未存在物料', () => {
								app.scanCode = ''
								app.focus = true
							})
						}

						
					},
					error: res => {
						this.$error(res.msg, () => {
							app.scanCode = ''
							app.focus = true
						})
					}
				})
			},
			// 查询物料
			queryMat(mc, qty) {
				let app = this;
				app.request({
					url: '/Material/GetByCode',
					method: 'GET',
					data: {
						code: mc
					},
					success: res => {
						if (res.response == null) {
							app.$error('未查询到信息', () => {
								app.focus = true
								app.scanCode = ''
							})
							return;
						}
						let temp = {
							scanCode: app.scanCode,
							MaterialId: res.response.Id,
							MaterialCode: res.response.MaterialCode,
							MaterialName: res.response.MaterialName,
							Specification: res.response.Specification,
							LotNo:'',
							qty: qty,
							date: getTime()
						}
						if (app.matList.length === 0) {
							app.matList.push(temp)
							app.$store.commit('createWO/setCreateWO', {
								sourceWO: app.wo,
								materialId: res.response.Id,
								materialCode: res.response.MaterialCode,
								materialName: res.response.MaterialName,
								specification: res.response.Specification,
								planQty: app.total,
								lineId: app.LineId,
								departmentId: app.DepartmentId,
							})
							app.scanCode = ''
							app.focus = true
						} else {
							app.$error('不允许扫描多个条码生成工单', () => {
								app.scanCode = ''
								app.focus = true
							})
						}
					}
				})
			},
			// 复选框改变事件
			change(e) {
				this.$clearStore(this)
				this.matList = []
				if (e.detail.value == 1) {
					this.isShow = true
				} else {
					this.isShow = false
				}
			},
			// 查询工单
			queryOrder() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.navigateTo({
					url: '../../common/sgOrderQuery/sgOrderQuery'
				})
			},
			// 获取工单信息
			getOrderInfo() {
				this.request({
					url: '/CodeRulesMain/GetCodeRule?ruleCode=SG',
					method: 'GET',
					success: res => {
						if (res.response == null) {
							this.$toast('未获取到工单')
							return;
						}
						this.WO = res.response;
					}
				})
			},
			// 获取登录人员信息
			getUserInfo() {
				let user = uni.getStorageSync('user');
				if (this.UserName == '') {
					user = uni.getStorageSync('user')
				} else if (this.UserName != user) {
					user = this.UserName
				}
				this.request({
					url: '/EquipRepairMain/GetSysUser',
					method: 'GET',
					data: {
						acount: user
					},
					success: res => {
						this.UserId = res.response.Id;
						this.UserName = res.response.UserName;
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 创建工单
			submit() {
				let app = this;
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要提交吗？', function(e) {
					if (e.index == 0) {
						app.reqS();
					} else {

					}
				}, {
					"title": '提示',
					"buttons": btns,
				});
				// #endif
				// #ifdef H5
				this.reqS()
				// #endif
			},
			// 取消，初始化页面
			cancel() {
				this.initPage()
				this.barcodeQty = null
				this.$clearStore(this)
			},
			// 初始化界面
			initPage() {
				this.matList = []
				if(this.matItem)
					this.matList.pushItem(this.matItem)
				// 消息提示
				this.msg = ''
			},
			// 工单创建请求
			reqS() {
				const app = this
				if (app.DepartmentId == null) {
					app.$error('未获取到部门信息，请重试')
				} else if (app.LineId == null) {
					app.$error('未获取到产线信息，请重试')
				} else if (app.sourceWOMM.length == 0 && app.radio == 1) {
					app.$error('未获取到物料信息，请至少选中一个物料')
				} else if (app.planQty <= 0 && app.radio == 2) {
					app.$error('物料数量不合理，请检查一下')
				} else if (app.materialId == null && app.radio == 2) {
					app.$error('未获取到物料信息，请重试')
				} else {
					if(app.barcodeQty==null)
					{
						//没有就是 载具码的数量。
						app.barcodeQty=app.total;
					}
					let dd = {
							LineId: app.LineId || null,
							DepartmentId: app.DepartmentId || '',
							SourceWo: app.wo || '',
							BarCode: app.matList.length == 0 ? '' : app.matList[0].scanCode,
							MaterialList: app.radio == 2 ? [{
								MaterialId: app.materialId,
								LotNo: app.matList[0].LotNo,
								PlanQty: app.barcodeQty
							}] : app.sourceWOMM
						}
					console.log('创建工单',dd)
					
					app.request({
						url: '/WOMain/Post',
						method: 'POST',
						data: dd,
						success: res => {
							let showWOList = ',以下为生成工单号：\n'
							for (let i = 0; i < res.response.length; i++) {
								showWOList = showWOList + res.response[i].WO + '\n'
							}
							app.$error(res.msg + showWOList, () => {
								if (app.radio == 2) {
									app.matList = []
									app.$store.commit('createWO/setPlanQty', {
										planQty: 0
									})
								}
								app.$store.commit('createWOMM/empty')
								app.$store.commit('createWO/empty')
								app.$store.commit('material/empty')
							})
						},
						error: res => {
							app.$error(res.msg)
						}
					})
				}
			},
			clearMat() {
				const app = this
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要清除扫码物料信息吗？', function(e) {
					if (e.index == 0) {
						app.$store.commit('createWOMM/empty')
						app.$store.commit('createWO/empty')
						app.$store.commit('material/empty')
						app.matList = []
						app.barcodeQty = null
					}
				}, {
					"title": '提示',
					"buttons": btns,
				});
				// #endif
				// #ifdef H5
				uni.showModal({
					title: '提示',
					content: '确认要清除扫码物料信息吗？',
					success: function(res) {
						if (res.confirm) {
							app.$store.commit('createWOMM/empty')
							app.$store.commit('createWO/empty')
							app.$store.commit('material/empty')
							app.matList = []
							app.barcodeQty = null
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				})
				// #endif
			}
		}
	}
</script>
