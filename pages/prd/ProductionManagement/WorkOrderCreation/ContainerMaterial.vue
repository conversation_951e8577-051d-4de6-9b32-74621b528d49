<template>
	<view class="uni-common-mt cu-page">
		<view class="uni-padding-wrap">
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view>
							<radio :value="item.idx" :checked="item.checked" />
						</view>
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content" style="width: 85%;">
								<view class="cu-form-data-14 uni-ellipsis">物料编码: {{item.MaterialCode}}</view>
								<view class="cu-form-data-14 uni-ellipsis">物料名称: {{item.MaterialName}}</view>
								<view class="cu-form-data-14 uni-ellipsis">规格: {{item.Specification}}</view>
								<view class="cu-form-data-14 uni-ellipsis">数量: {{item.Qty}}</view>
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
		
		<button class="cu-bar" type="primary" @click="doWorkOrder()">确认</button>
	</view>
</template>

<script>
	import {getTime} from '@/js/productUtil.js'	
	import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		data() {
			return {
				obj: [],
				cur: null,
				scanCode:null,
				lineId:null,
				departmentId:null
			}
		},
		onLoad(e) {
			this.scanCode = e.scanCode
			this.lineId = e.lineId
			this.departmentId=e.departmentId
			
			this.obj=uni.getStorageSync('WorkOrderMaterial')
			
			for(let i=0;i<this.obj.length;i++){
				this.obj[i].idx=i
			}
			
			console.log('传递的物料',this.obj)
			//this.query();
		},
		onShow() {
			//this.query();
		},
		methods: {
				
			radioChange(e) {
				var items = this.obj,
			    idx= e.detail.value;
				this.cur = items[idx]
				console.log('选中',idx,this.cur)
			},
			doWorkOrder(){
				if(this.cur ){
					let temp = {
						lineId:this.lineId,
						departmentId:this.departmentId,
						scanCode: this.scanCode,
						MaterialId: this.cur.MaterialId,
						MaterialCode: this.cur.MaterialCode,
						MaterialName: this.cur.MaterialName,
						Specification: this.cur.Specification,
						LotNo: this.cur.LotNo,
						Qty: this.cur.Qty
					}
					
					console.log('确定值',temp)
				
					var pages = getCurrentPages();
					let nowPage = pages[ pages.length - 1];  //当前页页面实例
					let prevPage = pages[ pages.length - 2 ];  //上一页页面实例
					setTimeout(()=>{
						uni.navigateBack({
							delta:1,
							success: () => {
								prevPage.$vm.pushItem(temp)
							}
						})
					},100)				
					
				}
					
			}
		},

	}
</script>

<style>
.cu-bar{
	position: fixed;
	bottom: 0;
	width: 100%;
	z-index: 90;
	text-align: center;
}

.cu-page{
	padding-bottom: 50px;
}
</style>
