<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-list-cell margin-top" hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
				:key="index">
				<view class="cu-form-group padding panel-full "  :style="{background:(item.PlanQty!=item.ActualQty?'#e06666':'')}" >
					<view style="width: 80%; ">
						<!-- 	<view class="text-black cu-form-data-10" v-show="item.UMQty != 0">
							载具码：{{item.CarrierCode}}</view> -->
						<view class="uni-ellipsis cu-form-data-10">物料编码：{{item.MaterialCode}}</view>
						<view class="uni-ellipsis cu-form-data-10">物料名称：{{item.MaterialName}}</view>
						<view class="uni-ellipsis cu-form-data-10">规格型号：{{item.Specification}}</view>
						<view class="uni-ellipsis cu-form-data-10">上料工序：{{item.ProcessName}}</view>
						<view class="uni-ellipsis cu-form-data-10">需求数量：{{item.PlanQty}}</view>
						<view class="uni-ellipsis cu-form-data-10">已上料数：{{item.ActualQty}}</view>
						<view class="uni-ellipsis cu-form-data-10">剩余用量：{{item.UMQty}}</view>
					</view>
					<view style="width: 20%; ">
						<view @click="down(item)">
							<uni-tag size="small" text="下料" type="error" />
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				wo: '',
				obj: []
			}
		},
		onLoad(e) {
			this.wo = e.WO;
			this.query();
		},
		onShow() {
			this.query();
		},
		methods: {
			query() {
				this.request({
					// url: '/UPMaterial/Get',
					url: '/ShipOrderDetail/GetFeed',
					method: 'GET',
					data: {
						key: this.wo,
						intPageSize: 9999,
						page: 1
					},
					success: res => {
						this.obj = res.response.data;
					}
				})
			},
			down(item) {
				// let that = this;
				// // #ifdef APP-PLUS
				// let platform = uni.getSystemInfoSync().platform;
				// let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				// plus.nativeUI.confirm('确认要下料吗？', function(e) {
				// 	console.log("Close confirm: " + e.index);
				// 	//0==确认，否则取消  
				// 	if(e.index == 0){
				// 		if(item.LotNo == null){
				// 			uni.navigateTo({
				// 				url:'./addLotNo?item='+ encodeURIComponent(JSON.stringify(item))
				// 			})
				// 		} else {
				// 			that.reqDown(item);
				// 		}
				// 	} else {

				// 	}
				// }, {
				// 	"title": '提示',
				// 	"buttons": btns
				// });
				// // #endif
				// that.query();
				// TEST
				uni.navigateTo({
					url: './addLotNo?item=' + encodeURIComponent(JSON.stringify(item)) + '&wo=' + this.wo
				})
				// await that.reqDown(item);
				// that.query();
				// this.open();
			},
			// 下料请求
			reqDown(item) {
				this.request({
					url: '/DownMaterial/Post',
					method: 'POST',
					data: {
						UPMaterials: [item],
						CarrirCode: ''
					},
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'success'
						})
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			}
		}
	}
</script>


