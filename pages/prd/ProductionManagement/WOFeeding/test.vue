<template>
	<view>
		<button @click="open">测试</button>
	</view>
</template>

<script>
	export default {
		methods: {
			open() {
				// const f = () => console.log('同步函数执行');
				// (async () => f())().then(res => {
				// 	console.log(res);
				// 	console.log('回调函数执行')
				// })
				const f = () => console.log('now');
				(
				  () => new Promise(
				    resolve => resolve(f())
				  )
				)();
				console.log('next');
				// now
				// next
			}
		}
	}

</script>
