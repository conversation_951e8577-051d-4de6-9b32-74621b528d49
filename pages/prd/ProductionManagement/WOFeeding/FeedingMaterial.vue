<template>
	<view class="uni-common-mt cu-page">
		<view class="uni-padding-wrap">
			<checkbox-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view>
							<checkbox :value="item.MaterialCode" :checked="item.checked" />
						</view>
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content" style="width: 85%;">
								<view class="cu-form-data-14 uni-ellipsis">物料编码: {{item.MaterialCode}}</view>
								<view class="cu-form-data-14 uni-ellipsis">物料名称: {{item.MaterialName}}</view>
								<view class="cu-form-data-14 uni-ellipsis">规格: {{item.Specification}}</view>
							</view>
						</view>
					</view>
				</view>
			</checkbox-group>
		</view>
		
		<button class="cu-bar" type="primary" @click="doUPMaterial()">确认</button>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		data() {
			return {
				obj: [],
				wo:'',
				processId:0,
				carrierCode:''
			}
		},
		onLoad(e) {
			this.wo = e.WO
			this.processId = e.ProcessId
			this.carrierCode=e.CarrierCode
			
			this.obj=uni.getStorageSync('FeedingMaterial')
			
			console.log('传递的物料',this.obj)
			//this.query();
		},
		onShow() {
			//this.query();
		},
		methods: {
			query(){
				if(this.wo && this.carrierCode){
					this.request({
						url: '/UPMaterial/GetPalletInfo',
						method: 'post',
						data: {
							WO: this.wo,
							ProcessId: this.processId,
							CarrierCode: this.carrierCode,
						},
						success: res => {
							this.obj = res.response.data;
						},
						error: res =>{
							this.$error(res.msg)
						}
					})
				}

			},
			radioChange(e) {
				var items = this.obj,
				values = e.detail.value;
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if(values.includes(item.MaterialCode)){
						this.$set(item,'checked',true)
					}else{
						this.$set(item,'checked',false)
					}
				}
				console.log(values)
				console.log(items)
			},
			doUPMaterial(){
				var items = this.obj
				var list=[]
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					if(items[i].checked)
						list.push(items[i].MaterialCode)
				}
				
				console.log('选中的记录',list)
				if(!list || list.length==0) return
				//调用上料接口
				this.request({
					url: '/UPMaterial/Feeding2',
					method: 'post',
					data: {
						WO: this.wo,
						ProcessId: this.processId,
						CarrierCode: this.carrierCode,
						MaterialList:list
					},
					success: res => {
						console.log('返回值',res)
						if(res.success)
						uni.navigateBack({
							animationDuration: 1000
						})
						
					},
					error: res =>{
						this.$error(res.msg)
					}
				})
			}
		},

	}
</script>

<style>
.cu-bar{
	position: fixed;
	bottom: 0;
	width: 100%;
	z-index: 90;
	text-align: center;
}

.cu-page{
	padding-bottom: 50px;
}
</style>
