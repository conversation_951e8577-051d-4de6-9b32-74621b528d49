<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>载具码</view>
				<input v-model="code" placeholder="请扫描载具码" @confirm="reqDown()" :focus="focus" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				code: '',
				item: null,
				focus: true,
				wo: ''
			}
		},
		onLoad(e) {
			// console.log(e);
			this.item = JSON.parse(decodeURIComponent(e.item));
			this.wo = e.wo
		},
		methods: {
			// 下料请求
			reqDown(item) {
				const app = this
				new Promise((resolve, reject) => {
					this.request({
						url: '/DownMaterial/Post',
						method: 'POST',
						data: {
							UPMaterials: [{
								...app.item,
								WO: app.wo
							}],
							CarrirCode: this.code
						},
						success: res => {
							this.$error(res.msg)
							resolve('success');
							uni.navigateBack({
								animationDuration: 1000
							})
						},
						error: res => {
							this.$error(res.msg)
							reject('fail');
						}
					})
				})
			}
		}
	}
</script>
