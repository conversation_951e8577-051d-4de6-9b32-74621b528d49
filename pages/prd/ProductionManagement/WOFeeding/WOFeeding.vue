<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title">产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<text style="color: #E3162E;">*</text>
				<view class="title">工单号</view>
				<input v-model="orderNo" disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-a-Processtooling xj_form_icon" />
				<text style="color: #E3162E;">*</text>
				<view class="title">工序</view>
				<picker @change="bindPickerChangeStorage" :value="indexStorage" :range="storageList"
					:range-key="'ProcessName'">
					<view class="#">{{storageList[indexStorage].ProcessName}}</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-bianmubianma xj_form_icon" />
				<view class="title">成品料号</view>
				<input v-model="MaterialCode" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-mingcheng xj_form_icon" />
				<view class="title">物料名称</view>
				<input v-model="MaterialName" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-guigeguanli xj_form_icon" />
				<view class="title">物料规格</view>
				<input v-model="Specification" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group margin-top" @click="feedindDetail()">
				<view class="iconfont xj-mingxi xj_form_icon" />
				<view class="title">上料明细</view>
				<input disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-tiaoma xj_form_icon" />
				<text style="color: #E3162E;">*</text>
				<view class="title">上料条码</view>
				<input v-model="SNCode" placeholder="请扫描物料条码" @confirm="submit()" :focus="focus" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="action margin-top" v-show="msg !== ''">
				<text class="cuIcon-title text-xj"></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				SNCode: '',
				focus: false,
				// 工单信息
				orderNo: '',
				orderName: '',
				orderId: '',
				// 工序选择使用
				storageList: [{
					id: 0,
					ProcessName: '请先选择工单'
				}],
				indexStorage: 0,
				processId: null,
				woId: null,
				// 物料
				MaterialId: null,
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				// 
				orgId: null,
				//
				msg: '',
				// 产线
				LineCode: '',
				LineName: '',
				LineId: null,
			}
		},
		onLoad() {
			this.clearCach();
			this.cancel()
		},
		onShow() {
			// this.cancel();
			this.orgId = uni.getStorageSync('orgId');
			uni.getStorage({
				key: 'orderInfo',
				success: o => {
					if (this.orderNo != o.data.orderNo) {
						this.cancel();
					}

					this.orderNo = o.data.orderNo;
					this.orderName = o.data.orderName;
					this.orderId = o.data.Id;
					this.MaterialId = o.data.MaterialId;
					this.MaterialCode = o.data.MaterialCode;
					this.MaterialName = o.data.MaterialName;
					this.Specification = o.data.Specification;


					this.getWO();
				}
			})
			uni.getStorage({
				key: 'lineInfo',
				success: o => {
					this.LineId = o.data.id;
					this.LineCode = o.data.LineCode;
					this.LineName = o.data.LineName;
				}
			})
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		methods: {
			// 产线
			getLine() {
				uni.removeStorage({
					key: 'lineInfo',
				})
				uni.navigateTo({
					url: '../../common/productionLine/productionLine'
				})
			},
			feedindDetail() {
				let that = this;
				if (that.orderNo !== '') {
					uni.navigateTo({
						url: './feedingDetail?WO=' + that.orderNo
					})
				} else {
					uni.showToast({
						title: '请先选择工单',
						icon: 'loading'
					})
				}
			},
			// 清缓存
			clearCach() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.removeStorage({
					key: 'lineInfo'
				})
			},
			// 查询工单
			queryOrder() {
				uni.removeStorage({
					key: 'orderInfo',
					success() {
						console.log("工单缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../../common/orderQuery/orderQuery?LineName=' + this.LineName + '&woStart=20'
				})
			},
			// 获取工序
			getWO() {
				this.request({
					url: '/Process/GetWo',
					method: 'GET',
					data: {
						intPageSize: 9999,
						woid: this.orderId,
						page: 1
					},
					success: res => {
						let temp = res.response.data;
						let that = this;
						if (temp.length > 0) {
							that.storageList = [{
								id: 1,
								ProcessName: '请选择工序'
							}];
						}
						for (let i = 0; i < temp.length; i++) {
							that.storageList.push({
								id: temp[i].Id,
								ProcessName: temp[i].ProcessName
							});
						}
					},
					error: res => {
						uni.showToast({
							title: res.msg,
							icon: 'error'
						});
						that.msg = res.msg;
						that.cancel();
					}
				})
			},
			// 上料
			submit() {
				let that = this;
				that.msg = '';
				if (that.storageList[that.indexStorage].ProcessName == '请选择工序') {
					uni.showToast({
						title: '请选择工序！',
						icon: 'loading'
					})
				} else if (that.storageList[that.indexStorage].ProcessName == '请先选择工单') {
					uni.showToast({
						title: '请选择工序！',
						icon: 'loading'
					})
				} else if (this.orderNo == '') {
					uni.showToast({
						title: '请选择工单！',
						icon: 'loading'
					})
				} else {
					
					that.reqFeeding();
				}
			},
			// 上料请求
			reqFeeding() {
				this.focus = false
				const app = this
				//显示加载框
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				
				
				app.request({
					url: '/UPMaterial/GetPalletInfo',
					method: 'post',
					data: {
						WO: app.orderNo,
						ProcessId: app.processId,
						CarrierCode: app.SNCode
					},
					success: res => {
						let mlist = res.response.data;
						
						if(mlist && mlist.length>0){
							
						   let sllist=mlist.filter((item)=>{return item.Qty>0})
							console.log('上料物料',sllist)
							if(!sllist || sllist.length==0){
								app.$error('该托盘物料已全部上料')
								app.SNCode = ''
							}
							else{
								uni.setStorageSync('FeedingMaterial', sllist)
								
								uni.navigateTo({
									url: '../WOFeeding/FeedingMaterial?WO=' + app.orderNo+'&ProcessId='+app.processId+'&CarrierCode='+app.SNCode
								})
								app.SNCode = ''
							}

						}
						else{
							
							app.request({
								url: '/UPMaterial/Post',
								method: 'POST',
								data: {
									WO: app.orderNo,
									ProcessId: app.processId,
									CarrierCode: app.SNCode
								},
								success: res => {
									//隐藏加载框
									uni.hideLoading();
									app.SNCode = ''
									app.$error(res.msg, () => {
										app.focus = true
										app.msg = res.msg
									})
								},
								error: res => {
									//隐藏加载框
									uni.hideLoading();
									app.$error(res.msg, () => {
										app.focus = true
										app.msg = res.msg
										app.SNCode = ''
									})
								}
							})
							
						}
					},
					error: res =>{
						app.$error(res.msg)
					}
				})
				
				
			},
			// 取消，初始化页面
			cancel() {
				this.SNCode = ''
				this.orderNo = ''
				this.orderName = ''
				this.orderId = ''
				// 工序选择使用
				this.storageList = [{
					id: null,
					ProcessName: '请先选择工单'
				}];
				this.indexStorage = 0
				this.woId = null
				// 物料
				this.MaterialId = null
				this.MaterialCode = ''
				this.MaterialName = ''
				this.Specification = ''
				// 
				this.orgId = null
				this.user = ''
				this.msg = ''
			},
			/* picker */
			bindPickerChangeStorage: function(e, name) {
				let id = e.detail.value;
				// console.log('picker发送选择改变，携带值为', this.storageList[id].id);
				this.indexStorage = id;
				this.processId = this.storageList[id].id
			}
		}
	}
</script>

<style>

</style>
