<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;" v-if="planQty  != null">
					工单总数：{{planQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;">
					本工序可报总数：{{currentShouldQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;"
					v-if="lastCurrentTotalQty  != null">
					上道工序报工良品数：{{lastCurrentTotalQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;"
					v-if="lastCurrentBadQty  != null">
					上道工序报工不良品数：{{lastCurrentBadQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;" v-if="currentTotalQty != null">
					本工序良品总数：{{currentTotalQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;" v-if="currentBadQty != null">
					本道工序不良品数：{{currentBadQty}}
				</view>
			</view>
			<view class="cu-form-group margin-top" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title">部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="可选" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title">产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="可选" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>工单号</view>
				<input v-model="orderNo" disabled="true" placeholder="请选择工单号" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-a-Processtooling xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>工序</view>
				<picker @change="bindPickerChangeStorage" :value="indexStorage" :range="storageList"
					:range-key="'ProcessName'">
					<view class="#">{{storageList[indexStorage].ProcessName}}</view>
				</picker>
			</view>
			<view class="cu-form-group" v-if="MaterialCode">
				<view class="iconfont xj-bianmubianma xj_form_icon" />
				<view class="title">物料编码</view>
				<input v-model="MaterialCode" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group" v-if="MaterialName">
				<view class="iconfont xj-mingcheng xj_form_icon" />
				<view class="title">物料名称</view>
				<input v-model="MaterialName" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group" v-if="Specification">
				<view class="iconfont xj-guigeguanli xj_form_icon" />
				<view class="title">物料规格</view>
				<input v-model="Specification" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-zhuanru xj_form_icon" />
				<view class="title">
					<!-- <text style="color: #E3162E;">*</text> -->载具输入
				</view>
				<input v-model="FormCarrierCode" placeholder="请扫描载具码" :focus="focusZero" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-zhuanchu xj_form_icon" />
				<view class="title">
					<!-- <text style="color: #E3162E;">*</text> -->载具输出
				</view>
				<input v-model="carrierCode" placeholder="请扫描载具码" :focus="focusOne" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			
			<view class="cu-form-group">
				<view class="iconfont xj-zhuanchu xj_form_icon" />
				<view class="title">
					<!-- <text style="color: #E3162E;">*</text> -->灌胶类型
				</view>
				<picker @change="bindPickerChange" :value="index" range-key="ItemName" :range="array">
					<view class="uni-input">{{index<0?'':array[index].ItemName}}</view>
				</picker>
			</view>			
			
			<view class="cu-form-group">
				<view class="iconfont xj-buliang xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>良品数量</view>
				<input v-model="goodQty" placeholder="请手动输入良品数量" type="number" :focus="focusTwo" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-guizehegexingjiancha xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>不良数量</view>
				<input v-model="badQty" placeholder="请手动输入不良数量" type="number" :focus="focusThree" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-renyuan xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>报工人</view>
				<input v-model="userName" @confirm="getUserInfo()" placeholder="请扫描工号" :focus="focusFour" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="toggleAutoPrint">
				<view class="iconfont xj-dayin xj_form_icon" />
				<view class="title" style="flex: 1; text-align: left;">打印标签</view>
				<checkbox :checked="autoPrintAfterSubmit" @click.stop="toggleAutoPrint" color="#007aff" />
			</view>
			<view class="cu-form-group" @click="goToBluetoothManager">
				<view class="iconfont xj-shebei xj_form_icon" />
				<view>蓝牙打印机管理</view>
				<input disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryDetail()">
				<view class="iconfont xj-mingxi xj_form_icon" />
				<view>我的报工明细</view>
				<input disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="action margin-top" v-show="msg">
				<text class="cuIcon-title text-xj"></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<view class="xj_button_group margin-top">
				<view class="xj_button" style="width: 30%; color: black;" @click="cancel">全部清空</view>
				<view class="xj_button" style="width: 35%; background-color: #009598;" @click="submitActualQty">确认报工</view>
				<view class="xj_button" style="width: 35%; background-color: #007aff;" @click="doPrint">打印测试</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import bluetoothPrintManager from '@/utils/bluetoothPrintManager.js'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				array: [],
				index: -1,
				// 本道工序不良品数
				lastCurrentBadQty: null,
				// 本工序可报总数
				currentShouldQty: null,
				lastCurrentTotalQty: null,
				// 载具输入
				FormCarrierCode: '',
				// 焦点控制
				focusZero: true,
				focusOne: false,
				focusTwo: false,
				focusThree: false,
				focusFour: false,
				// 工单总数
				planQty: null,
				// 本道工序不良品数
				currentBadQty: null,
				// 本工序可报总数
				currentTotalQty: null,
				SNCode: '',
				focus: false,
				// 工单信息
				orderNo: '',
				orderName: '',
				orderId: '',
				// 工序选择使用
				storageList: [{
					id: 0,
					ProcessName: '请先选择工单'
				}],
				indexStorage: 0,
				processId: 0,
				woId: null,
				// 物料
				MaterialId: null,
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				// 
				orgId: null,
				goodQty: null,
				badQty: 0,
				carrierCode: '',
				msg: '',
				//
				userName: '',
				userId: '',
				PersonnelId: '',
				// 产线
				LineCode: '',
				LineName: '',
				LineId: null,
				/* department */
				DepartmentId: 0,
				DepartmentCode: '',
				DepartmentName: '',
				// 蓝牙设备信息
				currDev: null,
				// 报工成功后自动打印标签
				autoPrintAfterSubmit: false
			}
		},
		onLoad(e) {
			// this.clearCach();
			// this.cancel();
			uni.removeStorage({
				key: 'orderInfo'
			})
		},
		onShow() {
			this.orgId = uni.getStorageSync('orgId');
			this.lastCurrentBadQty = null
			this.lastCurrentTotalQty = null
			this.planQty = null
			this.currentBadQty = null
			this.currentTotalQty = null

			if(!this.array || this.array.length==0)
				this.getCableType()

			// 初始化蓝牙打印SDK
			bluetoothPrintManager.initSDK()
			
			uni.getStorage({
				key: 'orderInfo',
				success: o => {
					this.orderNo = o.data.orderNo;
					this.orderName = o.data.orderName;
					this.orderId = o.data.Id;
					this.MaterialId = o.data.MaterialId;
					this.MaterialCode = o.data.MaterialCode;
					this.MaterialName = o.data.MaterialName;
					this.Specification = o.data.Specification;
					this.planQty = o.data.PlanQty
					if(this.orderNo.startsWith('SG')){
						this.goodQty = o.data.PlanQty
					}
					this.getWO();
				}
			})
			uni.getStorage({
				key: 'departmentInfo',
				success: o => {
					this.BenefitOrgId = o.data.OrgId
					this.DepartmentId = o.data.Id
					this.DepartmentCode = o.data.DepartmentCode
					this.DepartmentName = o.data.DepartmentName
				}
			});
			uni.getStorage({
				key: 'lineInfo',
				success: o => {
					this.LineId = o.data.id;
					this.LineCode = o.data.LineCode;
					this.LineName = o.data.LineName;
				}
			})
			this.changeBlur()
		},
		// watch: {
		// 	orderNo() {
		// 		this.lastCurrentBadQty = null
		// 		this.lastCurrentTotalQty = null
		// 		this.planQty = null
		// 		this.currentBadQty = null
		// 		this.currentTotalQty = null
		// 	}
		// },
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		methods: {
			
			bindPickerChange(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			},

			// 自动打印勾选框切换
			toggleAutoPrint() {
				this.autoPrintAfterSubmit = !this.autoPrintAfterSubmit
				console.log('自动打印设置:', this.autoPrintAfterSubmit)
			},

			// 独立的打印按钮方法
			async doPrint() {
				// 检查必要信息是否完整
				if (!this.orderNo) {
					this.$toast('请先选择工单')
					return
				}
				if (!this.userName) {
					this.$toast('请先扫描报工人')
					return
				}

				try {
					// 检查是否已连接打印机
					if (!bluetoothPrintManager.isConnected()) {
						await this.connectBluetoothPrinter()
					}

					// 准备打印数据
					const labelData = this.prepareLabelData()

					// 执行打印
					await bluetoothPrintManager.printProductionLabel(labelData)

					this.$toast('标签打印成功')
				} catch (error) {
					console.error('打印失败:', error)
					this.$toast(error.message || '打印失败')
				}
			},

			// 连接蓝牙打印机
			async connectBluetoothPrinter() {
				try {
					// 先搜索设备
					const deviceList = await bluetoothPrintManager.searchBluetoothDevices()

					if (deviceList.length === 0) {
						throw new Error('未找到蓝牙打印机设备')
					}

					// 如果只有一个设备，直接连接
					if (deviceList.length === 1) {
						await bluetoothPrintManager.connectDevice(deviceList[0])
						return
					}

					// 多个设备时，让用户选择
					const deviceNames = deviceList.map(device => device.name || device.address)

					uni.showActionSheet({
						itemList: deviceNames,
						success: async (res) => {
							const selectedDevice = deviceList[res.tapIndex]
							await bluetoothPrintManager.connectDevice(selectedDevice)
						},
						fail: () => {
							throw new Error('用户取消选择设备')
						}
					})
				} catch (error) {
					console.error('连接蓝牙打印机失败:', error)
					throw error
				}
			},

			// 准备标签数据
			prepareLabelData() {
				return {
					orderNo: this.orderNo,
					materialCode: this.MaterialCode,
					materialName: this.MaterialName,
					specification: this.Specification,
					goodQty: this.goodQty,
					badQty: this.badQty,
					lineName: this.LineName,
					userName: this.userName,
					departmentName: this.DepartmentName,
					processName: this.storageList[this.indexStorage]?.ProcessName
				}
			},

			// 跳转到蓝牙设备管理页面
			goToBluetoothManager() {
				uni.navigateTo({
					url: '/pages/bluetooth/deviceManager'
				})
			},

			// 报工成功后执行打印
			async doPrintAfterSubmit() {
				try {
					// 检查是否已连接打印机
					if (!bluetoothPrintManager.isConnected()) {
						await this.connectBluetoothPrinter()
					}

					// 准备打印数据
					const labelData = this.prepareLabelData()

					// 执行打印
					await bluetoothPrintManager.printProductionLabel(labelData)

					this.$toast('报工成功，标签已打印')
				} catch (error) {
					console.error('自动打印失败:', error)
					this.$toast('报工成功，但标签打印失败: ' + (error.message || '未知错误'))
				}
			},

			// 执行打印逻辑
			executePrint() {
				const tsc = require('@/static/libs/tsc.js')
				var command = tsc.jpPrinter.createNew()
				command.init()

				// 设置标签尺寸：50mm长 x 30mm宽
				command.setSize(50, 30)
				command.setGap(2)
				command.setCls()

				// 第一行：生产订单号（加粗显示）
				command.setText(10, 15, "TSS16.BF2", 0, 2, 1, this.orderNo)

				// 第二行：料号
				let materialCode = this.MaterialCode || ''
				if (materialCode.length > 20) materialCode = materialCode.substring(0, 20) + '..'
				command.setText(10, 45, "TSS12.BF2", 0, 1, 1, `料号: ${materialCode}`)

				// 第三行：品名
				let materialName = this.MaterialName || ''
				if (materialName.length > 20) materialName = materialName.substring(0, 20) + '..'
				command.setText(10, 70, "TSS12.BF2", 0, 1, 1, `品名: ${materialName}`)

				// 第四行：规格
				let specification = this.Specification || ''
				if (specification.length > 20) specification = specification.substring(0, 20) + '..'
				command.setText(10, 95, "TSS12.BF2", 0, 1, 1, `规格: ${specification}`)

				// 第五行：数量
				let totalQty = (Number(this.goodQty) || 0) + (Number(this.badQty) || 0)
				command.setText(10, 120, "TSS12.BF2", 0, 1, 1, `数量: ${totalQty} (良品:${this.goodQty || 0} 不良:${this.badQty || 0})`)

				// 第六行：产线
				let lineName = this.LineName || ''
				if (lineName.length > 20) lineName = lineName.substring(0, 20) + '..'
				command.setText(10, 145, "TSS12.BF2", 0, 1, 1, `产线: ${lineName}`)

				// 第七行：报工人
				command.setText(10, 170, "TSS12.BF2", 0, 1, 1, `报工人: ${this.userName}`)

				// 第八行：报工时间
				let currentTime = new Date()
				let timeStr = `${currentTime.getFullYear()}-${(currentTime.getMonth()+1).toString().padStart(2,'0')}-${currentTime.getDate().toString().padStart(2,'0')} ${currentTime.getHours().toString().padStart(2,'0')}:${currentTime.getMinutes().toString().padStart(2,'0')}`
				command.setText(10, 195, "TSS12.BF2", 0, 1, 1, `时间: ${timeStr}`)

				// 外边框
				command.setBox(5, 5, 395, 235, 1)

				// 分隔线（美化布局）
				command.setBox(10, 35, 390, 36, 1) // 订单号下方横线

				command.setPagePrint()

				// 发送打印数据
				this.sendPrintData(command.getData())
				this.$toast('生产标签打印中...')
			},

			// 为打印获取已连接的蓝牙设备
			getConnectedBluetoothDeviceForPrint() {
				const that = this;

				// 首先检查蓝牙适配器状态
				uni.getBluetoothAdapterState({
					success: function(res) {
						console.log('蓝牙适配器状态:', res);
						if (res.available) {
							// 获取已连接的蓝牙设备
							uni.getConnectedBluetoothDevices({
								success: function(connectedRes) {
									console.log('已连接的蓝牙设备:', connectedRes);
									if (connectedRes.devices && connectedRes.devices.length > 0) {
										// 找到第一个连接的设备（通常是打印机）
										const device = connectedRes.devices[0];
										that.currDev = {
											deviceId: device.deviceId,
											name: device.name || '蓝牙打印机',
											services: []
										};

										// 获取设备服务
										that.getBLEServicesForPrint(device.deviceId);
									} else {
										that.$toast('未找到已连接的蓝牙设备，请先连接蓝牙打印机');
									}
								},
								fail: function(err) {
									console.log('获取已连接设备失败:', err);
									that.$toast('获取蓝牙设备失败，请检查蓝牙连接');
								}
							});
						} else {
							that.$toast('蓝牙未开启，请先开启蓝牙');
						}
					},
					fail: function(err) {
						console.log('获取蓝牙适配器状态失败:', err);
						// 尝试打开蓝牙适配器
						uni.openBluetoothAdapter({
							success: function(openRes) {
								console.log('蓝牙适配器已打开');
								that.getConnectedBluetoothDeviceForPrint(); // 递归调用
							},
							fail: function(openErr) {
								console.log('打开蓝牙适配器失败:', openErr);
								that.$toast('无法打开蓝牙，请检查设备蓝牙功能');
							}
						});
					}
				});
			},

			// 为打印获取蓝牙设备服务
			getBLEServicesForPrint(deviceId) {
				const that = this;
				console.log('获取蓝牙设备服务:', deviceId);

				uni.getBLEDeviceServices({
					deviceId: deviceId,
					success: function(res) {
						console.log('设备服务:', res);
						if (res.services && res.services.length > 0) {
							const service = res.services[0]; // 通常第一个服务就是打印服务
							that.getBLECharacteristicsForPrint(deviceId, service.uuid);
						} else {
							that.$toast('未找到设备服务');
						}
					},
					fail: function(err) {
						console.log('获取设备服务失败:', err);
						that.$toast('获取设备服务失败');
					}
				});
			},

			// 为打印获取蓝牙设备特征值
			getBLECharacteristicsForPrint(deviceId, serviceId) {
				const that = this;
				console.log('获取设备特征值:', deviceId, serviceId);

				uni.getBLEDeviceCharacteristics({
					deviceId: deviceId,
					serviceId: serviceId,
					success: function(res) {
						console.log('设备特征值:', res);
						if (res.characteristics && res.characteristics.length > 0) {
							// 找到可写的特征值
							const writeCharacteristic = res.characteristics.find(char =>
								char.properties.write || char.properties.writeNoResponse
							);

							if (writeCharacteristic) {
								that.currDev.services = [{
									serviceId: serviceId,
									characteristicId: writeCharacteristic.uuid
								}];

								console.log('蓝牙设备信息已更新:', that.currDev);

								// 连接成功后自动执行打印
								that.executePrint();
							} else {
								that.$toast('未找到可写的特征值');
							}
						} else {
							that.$toast('未找到设备特征值');
						}
					},
					fail: function(err) {
						console.log('获取设备特征值失败:', err);
						that.$toast('获取设备特征值失败');
					}
				});
			},
			// 电缆线类型
			getCableType() {
				this.request({
					url: '/SalaryDetails/GetDataDictionary',
					method: 'GET',
					data: {
						type:'BGLX'
					},
					success: res => {
						const temp = res.response
						console.log('电缆线:',temp)
						if(temp && temp.length>0){
							let d = {}
							d.ItemValue=''
							d.ItemName=''
							this.array.push(d)
							
							for(let i=0;i<temp.length;i++){
								let d = {}
								d.ItemValue=temp[i].ItemValue
								d.ItemName=temp[i].ItemName
								this.array.push(d)
							}
						}

					},
					error: res => {
						that.$error(res.msg, () => {
							that.msg = res.msg
							that.cancel()
						})
					}
				})
			},
			
			
			// 本道工序不良品数
			lastQueryCurrentBadQty(id) {
				this.request({
					url: '/PieceworkMain/GetProcessWork',
					method: 'GET',
					data: {
						type: 2,
						wo: this.orderNo,
						processId: id
					},
					success: res => {
						this.lastCurrentTotalQty = res.response.Qty
						this.lastCurrentBadQty = res.response.BadQty
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 本工序可报总数
			queryCurrentTotalQty(id) {
				this.request({
					url: '/PieceworkDetail/GetQty',
					method: 'GET',
					data: {
						wo: this.orderNo,
						processId: id
					},
					success: res => {
						this.currentShouldQty = res.response.dataCounts
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 本道工序不良品数
			queryCurrentBadQty(id) {
				this.request({
					url: '/PieceworkMain/GetProcessWork',
					method: 'GET',
					data: {
						type: 1,
						wo: this.orderNo,
						processId: id
					},
					success: res => {
						this.currentBadQty = res.response.BadQty
						this.currentTotalQty = res.response.Qty
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询收益部门
			queryDepartment() {
				this.clearCach();
				this.LineId = null;
				this.LineCode = null;
				this.LineName = null;
				uni.navigateTo({
					url: '../../common/department/department?isProduct=2'
				})
			},
			getLine() {
				uni.removeStorage({
					key: 'lineInfo'
				})
				uni.navigateTo({
					url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			},
			// 查询我的报工明细
			queryDetail() {
				if (this.userId == '' || typeof(this.userId) == 'undefined') {
					this.$error('请先扫码人员工号')
				} else {
					uni.navigateTo({
						url: '../../common/rwDetail/rwDetail?userId=' + this.userId + '&wo=' + this.orderNo +
							'&processId=' + this.processId
					})
				}
			},
			// 判断载具是否存在
			isExistCarrier() {
				if (this.carrierCode == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.focusTwo = false
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierCode
					},
					success: res => {
						this.changeBlur()
					},
					error: res => {
						this.$error(res.msg, () => {
							this.carrierCode = ''
							this.focusOne = true
						})
					}
				})
			},
			// 判断载具是否存在
			isExistCarrier() {
				if (this.FormCarrierCode == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.focusZero = false
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.FormCarrierCode
					},
					success: res => {
						this.changeBlur()
					},
					error: res => {
						this.$error(res.msg, () => {
							this.FormCarrierCode = ''
							this.focusZero = true
						})
					}
				})
			},
			// 清缓存
			clearCach() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.removeStorage({
					key: 'departmentInfo'
				})
				uni.removeStorage({
					key: 'lineInfo'
				})
			},
			// 获取登录人员信息
			getUserInfo() {
				let that = this;
				this.request({
					url: '/User/GetPesonByCode',
					method: 'GET',
					data: {
						code: this.userName
					},
					success: res => {
						if (res.response == null) {
							this.userId = ''
							this.userName = ''
							this.$error('查无此人')
							return
						}
						
						this.userId = res.response.Id
						this.userName = res.response.PersonnelName
						this.PersonnelId = res.response.WorkNo
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询工单
			queryOrder() {
				uni.removeStorage({
					key: 'orderInfo',
				})
				uni.navigateTo({
					url: '../conmon/sgOrderQuery/sgOrderQuery?LineName=' + this.LineName
				})
			},
			// 获取工序
			getWO() {
				this.request({
					url: '/Process/GetWo',
					method: 'GET',
					data: {
						intPageSize: 9999,
						woid: this.orderId,
						page: 1
					},
					success: res => {
						const temp = res.response.data
						this.storageList = [{
							id: 0,
							ProcessName: '请先选择工单'
						}]
						if (this.orderNo.startsWith('SG')) {
							this.storageList = []
							for (let i = 0; i < temp.length; i++) {
								if (Boolean(temp[i].IsBatch)) {
									this.storageList.push({
										id: temp[i].Id,
										ProcessName: temp[i].ProcessName
									})
									this.processId = temp[i].Id
									this.queryCurrentBadQty(temp[i].Id)
									this.queryCurrentTotalQty(temp[i].Id)
								}
							}
							this.goodQty = this.planQty
						} else {
							for (let i = 0; i < temp.length; i++) {
								if (Boolean(temp[i].IsBatch)) {
									this.storageList.push({
										id: temp[i].Id,
										ProcessName: temp[i].ProcessName
									})
								}
							}
							if (this.storageList.length < 2) {
								this.storageList = [{
									id: 1,
									ProcessName: '无可报工工序'
								}]
							} else if (this.storageList.length == 2) {
								this.storageList.shift()
								this.lastQueryCurrentBadQty(this.storageList[0].id)
								this.queryCurrentBadQty(this.storageList[0].id)
								this.queryCurrentTotalQty(this.storageList[0].id)
								this.processId = this.storageList[0].id
							} else {
								this.storageList.shift()
								this.storageList.unshift({
									id: 1,
									ProcessName: '请选择工序'
								})
							}
						}
					},
					error: res => {
						that.$error(res.msg, () => {
							that.msg = res.msg
							that.cancel()
						})
					}
				})
			},
			validateProduct() {
				this.focus = false
				this.request({
					url: '/SNStationInfo/GetSn',
					method: 'GET',
					data: {
						materiaId: this.MaterialId,
						sn: this.SNCode,
						wo: this.orderNo
					},
					success: res => {
						this.focus = true;
						this.msg = '扫描物料存在于工单';
					},
					error: res => {
						this.$error('当前物料不存在于该工单', () => {
							this.SNCode = ''
							this.focus = true
							this.msg = '当前物料不存在于该工单'
						})
					}
				})
			},
			// 提交真实数量
			submitActualQty() {
				let that = this;
				if (this.orderNo == '') {
					uni.showToast({
						title: '请选择工单！',
						icon: 'loading'
					})
					
				} else if (this.storageList[this.indexStorage].ProcessName == '请先选择工单' || this.storageList[this
						.indexStorage].ProcessName == '请选择工序') {
					uni.showToast({
						title: '未获取到具体工序，无法报工！',
						icon: 'loading'
					})
				} else if (this.processId == null) {
					uni.showToast({
						title: '未获取到具体工序，无法报工！！',
						icon: 'loading'
					})
				} else if (this.PersonnelId == '') {
					uni.showToast({
						title: '未获取到当前登录人员！',
						icon: 'loading'
					})
				} else if (this.goodQty === 0 || this.goodQty === '' || this.goodQty == null) {
					uni.showToast({
						title: '请输入良品数！',
						icon: 'loading'
					})
				} else if (this.badQty === '' || this.badQty == null) {
					uni.showToast({
						title: '请输入不良品数！',
						icon: 'loading'
					})
				} else {
					const isR = Number(that.goodQty) + Number(that.badQty)
					if (isR <= that.planQty) {
						// #ifdef APP-PLUS
						let platform = uni.getSystemInfoSync().platform;
						let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
						plus.nativeUI.confirm('确认要进行报工吗？', function(e) {
							//0==确认，否则取消  
							if (e.index == 0) {
								that.req();
							}
						}, {
							"title": '提示',
							"buttons": btns,
						});
						// #endif
						// #ifdef H5
						uni.showModal({
							title: '提示',
							content: '确认要进行报工吗？',
							success: function(res) {
								if (res.confirm) {
									that.req();
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						});
						// #endif
					} else {
						that.$error('不良数量+良品数量不允许超过可报工数量')
					}
				}
			},
			// 报工请求
			req() {
				
				
				this.request({
					url: '/PieceworkDetail/Post?type=' + 1,
					method: 'POST',
					data: {
						Wo: this.orderNo,
						ProcessId: this.processId,
						BadQty: Number(this.badQty) || 0,
						TotalQty: Number(this.badQty) + Number(this.goodQty) || 0,
						GoodQty: Number(this.goodQty) || 0,
						CarrierCode: this.carrierCode,
						FormCarrierCode: this.FormCarrierCode,
						WorkNo: this.PersonnelId,
						CableType: this.index<0?'':this.array[this.index].ItemValue
					},
					success: res => {
						this.$toast(res.msg)

						// 如果勾选了自动打印，则在报工成功后自动打印标签
						if (this.autoPrintAfterSubmit) {
							setTimeout(() => {
								this.doPrintAfterSubmit()
							}, 1000)
						}

						setTimeout(() => {
							this.msg = res.msg
							this.FormCarrierCode = ''
							this.carrierCode = ''
							this.planQty = Number(this.planQty) - (Number(this.badQty) + Number(this
								.goodQty))
							this.lastQueryCurrentBadQty(this.processId)
							this.queryCurrentBadQty(this.processId)
							this.queryCurrentTotalQty(this.processId)
						}, 2000)
					},
					error: res => {
						this.$error(res.msg, () => {
							this.msg = res.msg
						})
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.SNCode = ''
				this.orderNo = ''
				this.orderName = ''
				this.orderId = ''
				// 工序选择使用
				this.storageList = [{
					id: null,
					ProcessName: '请先选择工单'
				}];
				this.indexStorage = 0
				this.woId = null
				// 物料
				this.MaterialId = null
				this.MaterialCode = ''
				this.MaterialName = ''
				this.Specification = ''
				//
				this.orgId = null
				this.userName = ''
				this.userId = ''
				this.msg = ''
				this.carrierCode = ''
				this.goodQty = null
				this.badQty = 0
			},
			// 焦点处理
			changeBlur() {
				let that = this
				that.focusZero = false
				that.focusOne = false
				that.focusTwo = false
				that.focusThree = false
				that.focusFour = false
				if (this.orderNo == '') {
					return
				} else if (this.FormCarrierCode == '') {
					that.focusZero = true
					return
				} else if (this.carrierCode == '') {
					that.focusOne = true
					return
				} else if (this.goodQty == '') {
					that.focusTwo = true
					return
					// } else if (this.badQty == '') {
					// 	that.focusThree = true
					// 	return
				} else if (this.userName == '') {
					that.focusFour = true
				}
			},
			/* picker */
			bindPickerChangeStorage: function(e, name) {
				let id = e.detail.value;
				this.indexStorage = id;
				this.processId = this.storageList[id].id
				this.lastCurrentBadQty = null
				this.lastCurrentTotalQty = null
				// this.planQty = null
				this.currentBadQty = null
				this.currentTotalQty = null
				this.lastQueryCurrentBadQty(this.storageList[id].id)
				this.queryCurrentBadQty(this.storageList[id].id)
				this.queryCurrentTotalQty(this.storageList[id].id)
			}
		}
	}
</script>

<style>
.xj_button_group {
  display: flex;
  justify-content: space-between;
  padding: 0 30upx;
  gap: 10upx;
}

.xj_button {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  border-radius: 10upx;
  color: white;
  font-size: 28upx;
  font-weight: bold;
  box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.xj_button:active {
  transform: scale(0.95);
  box-shadow: 0 1upx 4upx rgba(0, 0, 0, 0.2);
}

/* 清空按钮样式 */
.xj_button[style*="color: black"] {
  background-color: #f5f5f5;
  border: 2upx solid #ddd;
}

/* 报工按钮样式 */
.xj_button[style*="background-color: #009598"] {
  background: linear-gradient(135deg, #009598 0%, #007a7c 100%);
}

/* 打印测试按钮样式 */
.xj_button[style*="background-color: #007aff"] {
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
}

/* 勾选框样式 */
.cu-form-group checkbox {
  transform: scale(1.3);
}

/* 可点击的表单组样式 */
.cu-form-group[onclick] {
  cursor: pointer;
}

.cu-form-group[onclick]:active {
  background-color: #f5f5f5;
}

/* 确保表单项内容左对齐 */
.cu-form-group {
  display: flex;
  align-items: center;
  text-align: left;
}

.cu-form-group .title {
  text-align: left !important;
  justify-content: flex-start !important;
}

/* 修复checkbox对齐问题 */
.cu-form-group checkbox {
  margin-left: auto;
  margin-right: 360upx;
}
</style>
