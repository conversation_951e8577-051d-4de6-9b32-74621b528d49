<template>
  <view>
    <view class="cu-form-group" @click="queryDepartment()">
      <view class="iconfont xj-bumen xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>部门</view>
      <input v-model="DepartmentName" disabled="true" placeholder="请选择部门" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
    </view>
    <view class="cu-form-group" @click="queryLine()">
      <view class="iconfont xj-chanxian xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>产线</view>
      <input v-model="LineName" disabled="true" placeholder="请选择产线" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
    </view>
    <view class="cu-form-group" @click="queryOrder()">
      <view class="iconfont xj-gongdan xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>工单号</view>
      <input v-model="orderNo" disabled="true" placeholder="请选择工单号" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
    </view>
    <view class="cu-form-group">
      <view class="iconfont xj-a-Processtooling xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>工序</view>
      <picker @change="bindPickerChangeStorage" :value="indexStorage" :range="storageList"
              :range-key="'ProcessName'">
        <view class="#">{{storageList[indexStorage] ? storageList[indexStorage].ProcessName : '请先选择工单'}}</view>
      </picker>
    </view>
    <view class="cu-form-group" v-if="MaterialCode">
      <view class="iconfont xj-bianmubianma xj_form_icon" />
      <view class="title">物料编码</view>
      <input v-model="MaterialCode" disabled="true" placeholder="选择工单后自动加载" />
    </view>
    <view class="cu-form-group" v-if="MaterialName">
      <view class="iconfont xj-mingcheng xj_form_icon" />
      <view class="title">物料名称</view>
      <input v-model="MaterialName" disabled="true" placeholder="选择工单后自动加载" />
    </view>
    <view class="cu-form-group" v-if="Specification">
      <view class="iconfont xj-guigeguanli xj_form_icon" />
      <view class="title">物料规格</view>
      <input v-model="Specification" disabled="true" placeholder="选择工单后自动加载" />
    </view>
    <view class="cu-form-group">
      <view class="iconfont xj-zhuanru xj_form_icon" />
      <view class="title">
        <!-- <text style="color: #E3162E;">*</text> -->载具输入
      </view>
      <input v-model="FormCarrierCode" placeholder="请扫描载具码" :focus="focusZero" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
    </view>
    <view class="cu-form-group">
      <view class="iconfont xj-zhuanchu xj_form_icon" />
      <view class="title">
        <!-- <text style="color: #E3162E;">*</text> -->载具输出
      </view>
      <input v-model="carrierCode" placeholder="请扫描载具码" :focus="focusOne" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
    </view>

    <view class="cu-form-group">
      <view class="iconfont xj-zhuanchu xj_form_icon" />
      <view class="title">
        <!-- <text style="color: #E3162E;">*</text> -->灌胶类型
      </view>
      <picker @change="bindPickerChange" :value="index" range-key="ItemName" :range="array">
        <view class="uni-input">{{index<0 || !array[index] ? '' : array[index].ItemName}}</view>
      </picker>
    </view>

    <view class="cu-form-group">
      <view class="iconfont xj-buliang xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>良品数量</view>
      <input v-model="goodQty" placeholder="请手动输入良品数量" type="number" :focus="focusTwo" />
    </view>
    <view class="cu-form-group">
      <view class="iconfont xj-guizehegexingjiancha xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>不良数量</view>
      <input v-model="badQty" placeholder="请手动输入不良数量" type="number" :focus="focusThree" />
    </view>
    <view class="cu-form-group">
      <view class="iconfont xj-renyuan xj_form_icon" />
      <view class="title"><text style="color: #E3162E;">*</text>报工人</view>
      <input v-model="userName" @confirm="getUserInfo()" placeholder="请扫描工号" :focus="focusFour" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
    </view>
    <view class="cu-form-group" @click="queryDetail()">
      <view class="iconfont xj-mingxi xj_form_icon" />
      <view>我的报工明细</view>
      <input disabled="true" />
      <uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
    </view>
    <view class="action margin-top" v-show="msg">
      <text class="cuIcon-title text-xj"></text> 消息提示：
      <text style="color:#E3162E;">{{msg}}</text>
    </view>
    <view class="xj_button_group margin-top">
      <view class="xj_button" style="width: 30%; color: black;" @click="cancel">全部清空</view>
      <view class="xj_button" style="width: 30%; background-color: #007aff;" @click="doPrint">打印标签</view>
      <view class="xj_button" style="width: 40%; background-color: #009598;" @click="submitActualQty">确认报工</view>
    </view>
  </view>
</template>

<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
export default {
  components: {
    uniTag,
    UniIcons
  },
  data() {
    return {
      array: [],
      index: -1,
      // 本道工序不良品数
      lastCurrentBadQty: null,
      // 本工序可报总数
      currentShouldQty: null,
      lastCurrentTotalQty: null,
      // 载具输入
      FormCarrierCode: '',
      // 焦点控制
      focusZero: true,
      focusOne: false,
      focusTwo: false,
      focusThree: false,
      focusFour: false,
      // 工单总数
      planQty: null,
      // 本道工序不良品数
      currentBadQty: null,
      // 本工序可报总数
      currentTotalQty: null,
      SNCode: '',
      focus: false,
      // 工单信息
      orderNo: '',
      orderName: '',
      orderId: '',
      // 工序选择使用
      storageList: [{
        id: 0,
        ProcessName: '请先选择工单'
      }],
      indexStorage: 0,
      processId: 0,
      woId: null,
      // 物料
      MaterialId: null,
      MaterialCode: '',
      MaterialName: '',
      Specification: '',
      //
      orgId: null,
      goodQty: null,
      badQty: 0,
      carrierCode: '',
      msg: '',
      //
      userName: '',
      userId: '',
      PersonnelId: '',
      // 产线
      LineCode: '',
      LineName: '',
      LineId: null,
      /* department */
      DepartmentId: 0,
      DepartmentCode: '',
      DepartmentName: '',
      // 蓝牙设备信息
      currDev: null
    }
  },
  onLoad(e) {
    // this.clearCach();
    // this.cancel();
    uni.removeStorage({
      key: 'orderInfo'
    })
  },
  onShow() {
    this.orgId = uni.getStorageSync('orgId');
    this.lastCurrentBadQty = null
    this.lastCurrentTotalQty = null
    this.planQty = null
    this.currentBadQty = null
    this.currentTotalQty = null

    if(!this.array || this.array.length==0)
      this.getCableType()

    uni.getStorage({
      key: 'orderInfo',
      success: o => {
        this.orderNo = o.data.orderNo;
        this.orderName = o.data.orderName;
        this.orderId = o.data.Id;
        this.MaterialId = o.data.MaterialId;
        this.MaterialCode = o.data.MaterialCode;
        this.MaterialName = o.data.MaterialName;
        this.Specification = o.data.Specification;
        this.planQty = o.data.PlanQty
        if(this.orderNo.startsWith('SG')){
          this.goodQty = o.data.PlanQty
        }
        this.getWO();
      }
    })
    uni.getStorage({
      key: 'departmentInfo',
      success: o => {
        this.BenefitOrgId = o.data.OrgId
        this.DepartmentId = o.data.Id
        this.DepartmentCode = o.data.DepartmentCode
        this.DepartmentName = o.data.DepartmentName
      }
    });
    uni.getStorage({
      key: 'lineInfo',
      success: o => {
        this.LineId = o.data.id;
        this.LineCode = o.data.LineCode;
        this.LineName = o.data.LineName;
      }
    })
    this.changeBlur()
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.cancel();
    uni.stopPullDownRefresh();
  },
  methods: {

    bindPickerChange(e) {
      console.log('picker发送选择改变，携带值为', e.detail.value)
      this.index = e.detail.value
    },
    // 电缆线类型
    getCableType() {
      this.request({
        url: '/SalaryDetails/GetDataDictionary',
        method: 'GET',
        data: {
          type:'BGLX'
        },
        success: res => {
          const temp = res.response
          console.log('电缆线:',temp)
          if(temp && temp.length>0){
            let d = {}
            d.ItemValue=''
            d.ItemName=''
            this.array.push(d)

            for(let i=0;i<temp.length;i++){
              let d = {}
              d.ItemValue=temp[i].ItemValue
              d.ItemName=temp[i].ItemName
              this.array.push(d)
            }
          }

        },
        error: res => {
          this.$error(res.msg, () => {
            this.msg = res.msg
            this.cancel()
          })
        }
      })
    },

    // 执行打印 - 标准版布局，5cm×3cm标签
    doPrint() {
      // 检查必要信息是否完整
      if (!this.orderNo) {
        this.$toast('请先选择工单')
        return
      }
      if (!this.userName) {
        this.$toast('请先扫描报工人')
        return
      }

      // 如果没有连接的蓝牙设备，尝试获取已连接的设备
      if (!this.currDev) {
        this.getConnectedBluetoothDevice()
        return
      }

      const tsc = require('@/static/libs/tsc.js')
      var command = tsc.jpPrinter.createNew()
      command.init()

      // 设置标签尺寸：50mm长 x 30mm宽
      command.setSize(50, 30)
      command.setGap(2)
      command.setCls()

      // 第一行：生产订单号（加粗显示）
      command.setText(10, 15, "TSS16.BF2", 0, 2, 1, this.orderNo)

      // 第二行：料号
      let materialCode = this.MaterialCode || ''
      if (materialCode.length > 20) materialCode = materialCode.substring(0, 20) + '..'
      command.setText(10, 45, "TSS12.BF2", 0, 1, 1, `料号: ${materialCode}`)

      // 第三行：品名
      let materialName = this.MaterialName || ''
      if (materialName.length > 20) materialName = materialName.substring(0, 20) + '..'
      command.setText(10, 70, "TSS12.BF2", 0, 1, 1, `品名: ${materialName}`)

      // 第四行：规格
      let specification = this.Specification || ''
      if (specification.length > 20) specification = specification.substring(0, 20) + '..'
      command.setText(10, 95, "TSS12.BF2", 0, 1, 1, `规格: ${specification}`)

      // 第五行：数量
      let totalQty = (Number(this.goodQty) || 0) + (Number(this.badQty) || 0)
      command.setText(10, 120, "TSS12.BF2", 0, 1, 1, `数量: ${totalQty} (良品:${this.goodQty || 0} 不良:${this.badQty || 0})`)

      // 第六行：产线
      let lineName = this.LineName || ''
      if (lineName.length > 20) lineName = lineName.substring(0, 20) + '..'
      command.setText(10, 145, "TSS12.BF2", 0, 1, 1, `产线: ${lineName}`)

      // 第七行：报工人
      command.setText(10, 170, "TSS12.BF2", 0, 1, 1, `报工人: ${this.userName}`)

      // 第八行：报工时间
      let currentTime = new Date()
      let timeStr = `${currentTime.getFullYear()}-${(currentTime.getMonth()+1).toString().padStart(2,'0')}-${currentTime.getDate().toString().padStart(2,'0')} ${currentTime.getHours().toString().padStart(2,'0')}:${currentTime.getMinutes().toString().padStart(2,'0')}`
      command.setText(10, 195, "TSS12.BF2", 0, 1, 1, `时间: ${timeStr}`)

      // 外边框
      command.setBox(5, 5, 395, 235, 1)

      // 分隔线（美化布局）
      command.setBox(10, 35, 390, 36, 1) // 订单号下方横线

      command.setPagePrint()

      // 发送打印数据
      this.sendPrintData(command.getData())
      this.$toast('生产标签打印中...')
    },

    // 发送打印数据到蓝牙打印机
    sendPrintData(data) {
      // 参考其他页面的打印实现
      let deviceId = this.currDev?.deviceId
      let serviceId = this.currDev?.services?.[0]?.serviceId
      let characteristicId = this.currDev?.services?.[0]?.characteristicId

      if (!deviceId || !serviceId || !characteristicId) {
        this.$toast('请先连接蓝牙打印机')
        return
      }

      this.senBlData(deviceId, serviceId, characteristicId, data)
    },

    // 蓝牙数据发送方法
    senBlData(deviceId, serviceId, characteristicId, uint8Array) {
      console.log('************deviceId = [' + deviceId + ']  serviceId = [' + serviceId + '] characteristics=[' + characteristicId + "]")
      var uint8Buf = Array.from(uint8Array);

      function split_array(datas, size) {
        var result = {};
        var j = 0
        if (datas.length < size) {
          size = datas.length
        }
        for (var i = 0; i < datas.length; i += size) {
          result[j] = datas.slice(i, i + size)
          j++
        }
        console.log(result)
        return result
      }

      var splitData = split_array(uint8Buf, 20);
      var that = this;
      var index = 0;
      var maxIndex = Object.keys(splitData).length;

      function sendNext() {
        if (index < maxIndex) {
          var buffer = new ArrayBuffer(splitData[index].length);
          var dataView = new DataView(buffer);
          for (var i = 0; i < splitData[index].length; i++) {
            dataView.setUint8(i, splitData[index][i]);
          }

          uni.writeBLECharacteristicValue({
            deviceId: deviceId,
            serviceId: serviceId,
            characteristicId: characteristicId,
            value: buffer,
            success: function(res) {
              console.log('发送成功', index);
              index++;
              setTimeout(sendNext, 100); // 延迟100ms发送下一包
            },
            fail: function(res) {
              console.log('发送失败', res);
              that.$toast('打印数据发送失败');
            }
          });
        } else {
          console.log('所有数据发送完成');
          that.$toast('打印数据发送完成');
        }
      }

      sendNext();
    },

    // 获取已连接的蓝牙设备
    getConnectedBluetoothDevice() {
      const that = this;

      // 首先检查蓝牙适配器状态
      uni.getBluetoothAdapterState({
        success: function(res) {
          console.log('蓝牙适配器状态:', res);
          if (res.available) {
            // 获取已连接的蓝牙设备
            uni.getConnectedBluetoothDevices({
              success: function(connectedRes) {
                console.log('已连接的蓝牙设备:', connectedRes);
                if (connectedRes.devices && connectedRes.devices.length > 0) {
                  // 找到第一个连接的设备（通常是打印机）
                  const device = connectedRes.devices[0];
                  that.currDev = {
                    deviceId: device.deviceId,
                    name: device.name || '蓝牙打印机',
                    services: []
                  };

                  // 获取设备服务
                  that.getBLEServices(device.deviceId);
                } else {
                  that.$toast('未找到已连接的蓝牙设备，请先连接蓝牙打印机');
                }
              },
              fail: function(err) {
                console.log('获取已连接设备失败:', err);
                that.$toast('获取蓝牙设备失败，请检查蓝牙连接');
              }
            });
          } else {
            that.$toast('蓝牙未开启，请先开启蓝牙');
          }
        },
        fail: function(err) {
          console.log('获取蓝牙适配器状态失败:', err);
          // 尝试打开蓝牙适配器
          uni.openBluetoothAdapter({
            success: function(openRes) {
              console.log('蓝牙适配器已打开');
              that.getConnectedBluetoothDevice(); // 递归调用
            },
            fail: function(openErr) {
              console.log('打开蓝牙适配器失败:', openErr);
              that.$toast('无法打开蓝牙，请检查设备蓝牙功能');
            }
          });
        }
      });
    },

    // 获取蓝牙设备服务
    getBLEServices(deviceId) {
      const that = this;
      console.log('获取蓝牙设备服务:', deviceId);

      uni.getBLEDeviceServices({
        deviceId: deviceId,
        success: function(res) {
          console.log('设备服务:', res);
          if (res.services && res.services.length > 0) {
            const service = res.services[0]; // 通常第一个服务就是打印服务
            that.getBLECharacteristics(deviceId, service.uuid);
          } else {
            that.$toast('未找到设备服务');
          }
        },
        fail: function(err) {
          console.log('获取设备服务失败:', err);
          that.$toast('获取设备服务失败');
        }
      });
    },

    // 获取蓝牙设备特征值
    getBLECharacteristics(deviceId, serviceId) {
      const that = this;
      console.log('获取设备特征值:', deviceId, serviceId);

      uni.getBLEDeviceCharacteristics({
        deviceId: deviceId,
        serviceId: serviceId,
        success: function(res) {
          console.log('设备特征值:', res);
          if (res.characteristics && res.characteristics.length > 0) {
            // 找到可写的特征值
            const writeCharacteristic = res.characteristics.find(char =>
              char.properties.write || char.properties.writeNoResponse
            );

            if (writeCharacteristic) {
              that.currDev.services = [{
                serviceId: serviceId,
                characteristicId: writeCharacteristic.uuid
              }];

              that.$toast('蓝牙打印机连接成功');
              console.log('蓝牙设备信息已更新:', that.currDev);

              // 连接成功后自动执行打印
              that.doPrint();
            } else {
              that.$toast('未找到可写的特征值');
            }
          } else {
            that.$toast('未找到设备特征值');
          }
        },
        fail: function(err) {
          console.log('获取设备特征值失败:', err);
          that.$toast('获取设备特征值失败');
        }
      });
    },

    // 本道工序不良品数
    lastQueryCurrentBadQty(id) {
      this.request({
        url: '/PieceworkMain/GetProcessWork',
        method: 'GET',
        data: {
          type: 2,
          wo: this.orderNo,
          processId: id
        },
        success: res => {
          this.lastCurrentTotalQty = res.response.Qty
          this.lastCurrentBadQty = res.response.BadQty
        },
        error: res => {
          this.$error(res.msg)
        }
      })
    },
    // 本工序可报总数
    queryCurrentTotalQty(id) {
      this.request({
        url: '/PieceworkDetail/GetQty',
        method: 'GET',
        data: {
          wo: this.orderNo,
          processId: id
        },
        success: res => {
          this.currentTotalQty = res.response.Qty
          this.currentBadQty = res.response.BadQty
        },
        error: res => {
          this.$error(res.msg)
        }
      })
    },
    // 工序选择
    bindPickerChangeStorage: function(e) {
      this.indexStorage = e.detail.value;
      this.processId = this.storageList[this.indexStorage].id;
      this.lastQueryCurrentBadQty(this.processId);
      this.queryCurrentTotalQty(this.processId);
    },
    // 获取工序
    getWO() {
      this.request({
        url: '/PieceworkMain/GetProcessByWO',
        method: 'GET',
        data: {
          wo: this.orderNo
        },
        success: res => {
          this.storageList = res.response;
          this.indexStorage = 0;
          this.processId = this.storageList[this.indexStorage].id;
          this.lastQueryCurrentBadQty(this.processId);
          this.queryCurrentTotalQty(this.processId);
        },
        error: res => {
          this.$error(res.msg)
        }
      })
    },
    // 获取用户信息
    getUserInfo() {
      if (!this.userName) {
        this.$error('请扫描工号')
        return
      }
      this.request({
        url: '/Personnel/GetPersonnelByCode',
        method: 'GET',
        data: {
          code: this.userName
        },
        success: res => {
          this.PersonnelId = res.response.Id;
          this.userName = res.response.PersonnelName;
          this.userId = res.response.PersonnelCode;
        },
        error: res => {
          this.$error(res.msg, () => {
            this.userName = ''
            this.PersonnelId = ''
            this.userId = ''
          })
        }
      })
    },
    // 查询部门
    queryDepartment() {
      uni.navigateTo({
        url: '../../../common/department/department'
      })
    },
    // 查询产线
    queryLine() {
      if (!this.DepartmentId) {
        this.$error('请先选择部门')
        return
      }
      uni.navigateTo({
        url: '../../../common/line/line?departmentId=' + this.DepartmentId
      })
    },
    // 查询工单
    queryOrder() {
      if (!this.LineId) {
        this.$error('请先选择产线')
        return
      }
      uni.navigateTo({
        url: '../../../common/order/order?lineId=' + this.LineId
      })
    },
    // 查询明细
    queryDetail() {
      if (!this.PersonnelId) {
        this.$error('请先扫描工号')
        return
      }
      uni.navigateTo({
        url: './batchNewspaperWorkerDetail?personnelId=' + this.PersonnelId
      })
    },
    // 提交报工
    submitActualQty() {
      if (!this.orderNo) {
        this.$error('请选择工单')
        return
      }
      if (!this.processId) {
        this.$error('请选择工序')
        return
      }
      if (!this.goodQty && !this.badQty) {
        this.$error('请输入数量')
        return
      }
      if (!this.PersonnelId) {
        this.$error('请扫描工号')
        return
      }

      let data = {
        WOId: this.orderId,
        ProcessId: this.processId,
        PersonnelId: this.PersonnelId,
        GoodQty: this.goodQty || 0,
        BadQty: this.badQty || 0,
        FormCarrierCode: this.FormCarrierCode,
        CarrierCode: this.carrierCode,
        CableType: this.index >= 0 && this.array[this.index] ? this.array[this.index].ItemValue : ''
      }

      this.request({
        url: '/PieceworkMain/PostPiecework',
        method: 'POST',
        data: data,
        success: res => {
          this.$success('报工成功', () => {
            this.cancel()
          })
        },
        error: res => {
          this.$error(res.msg)
        }
      })
    },
    // 清空
    cancel() {
      this.FormCarrierCode = ''
      this.carrierCode = ''
      this.goodQty = null
      this.badQty = 0
      this.userName = ''
      this.PersonnelId = ''
      this.userId = ''
      this.msg = ''
      this.index = -1
      this.changeBlur()
    },
    // 焦点控制
    changeBlur() {
      this.focusZero = true
      this.focusOne = false
      this.focusTwo = false
      this.focusThree = false
      this.focusFour = false
    }
  }
}
</script>

<style>
.xj_button_group {
  display: flex;
  justify-content: space-between;
  padding: 0 30upx;
}

.xj_button {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  border-radius: 10upx;
  color: white;
  font-size: 28upx;
}
</style>