<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;" v-if="planQty  != null">
					工单总数：{{planQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;"
					v-if="lastCurrentTotalQty  != null">
					上道工序报工良品数：{{lastCurrentTotalQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;"
					v-if="lastCurrentBadQty  != null">
					上道工序报工不良品数：{{lastCurrentBadQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;" v-if="currentTotalQty != null">
					本工序良品总数：{{currentTotalQty}}
				</view>
				<view class="uni-flex-item" style="font-size: 40upx;font-weight: 1000;" v-if="currentBadQty != null">
					本道工序不良品数：{{currentBadQty}}
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-bianmubianma xj_form_icon" />
				<view class="title">物料编码</view>
				<input v-model="MaterialCode" disabled="true" @confirm="" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-mingcheng xj_form_icon" />
				<view class="title">物料名称</view>
				<input v-model="MaterialName" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-guigeguanli xj_form_icon" />
				<view class="title">物料规格</view>
				<input v-model="Specification" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-guigeguanli xj_form_icon" />
				<view class="title">计划完成</view>
				<input v-model="PlanEndDate" disabled="true" placeholder="选择工单后自动加载" />
			</view>
			<view class="cu-form-group margin-top" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="可选" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="可选" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>工单号</view>
				<input v-model="orderNo" disabled="true" placeholder="请选择工单" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-a-Processtooling xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>工序</view>
				<picker @change="bindPickerChangeStorage" :value="indexStorage" :range="storageList"
					:range-key="'ProcessName'">
					<view class="#">{{storageList[indexStorage].ProcessName}}</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-renyuan xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>报工人</view>
				<input v-model="userName" placeholder="请手动扫描" @confirm="getUserInfo()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryDetail()">
				<view class="iconfont xj-mingxi xj_form_icon" />
				<view>我的报工明细</view>
				<input disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group solid-bottom margin-bottom">
				<view class="iconfont xj-tiaoma xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>产品码</view>
				<input v-model="SNCode" placeholder="请扫描单件码" @confirm="req()" :focus="focus" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="action margin-top" v-show="msg">
				<text class="cuIcon-title text-xj"></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in workers" :key="index">
				<view class="cu-form-group solid-bottom panel-full" @click="showDialog(index)">
					<view class="content">
						<strong>
							<view class="text-black cu-form-data-10">条码：{{item.SNCode}}</view>
						</strong>
						<view class="uni-ellipsis">物料名称：{{item.MaterialName}} </view>
						<view class="uni-ellipsis">状态：{{item.Status}} </view>
						<view class="uni-ellipsis">工序：{{item.processName}} </view>
						<view class="uni-ellipsis">报工时间：{{item.date}} </view>
						<view class="uni-ellipsis">不良项目：{{item.item}} </view>
					</view>
				</view>
			</view>
			<uni-dialog :show="isShow">
				<view class="cu-form-group">
					<view class="title">不良类型</view>
					<uni-data-select style="background-color: #FFFFFF; width: 200px;" v-model="value"
						:localdata="badGroup" @change="changeSelect" />
				</view>
				<view class="cu-form-group">
					<uni-data-checkbox multiple @change="changeRadio" mode="button" v-model="radio"
						:localdata="badItem" />
				</view>
				<view class="cu-form-group">
					<uni-tag text="取消" type="warning" @click="cancelDialog()" />
					<uni-tag text="确认" type="success" @click="submit()" />
				</view>
			</uni-dialog>
		</view>
	</view>
</template>

<script>
	import {
		getTime
	} from '@/js/productUtil.js';
	import uniDialog from '@/components/uni-dialog.vue'
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons,
			uniDialog
		},
		data() {
			return {
				msg: '',
				// 本道工序不良品数
				lastCurrentBadQty: null,
				// 本工序可报总数
				lastCurrentTotalQty: null,
				// 工单总数
				planQty: null,
				// 本道工序不良品数
				currentBadQty: null,
				// 本工序可报总数
				currentTotalQty: null,
				isShow: false,
				// 报工数组
				workers: [],
				// 不良项目
				value: null,
				badGroup: [],
				radio: [],
				badItem: [],
				// 
				SNCode: '',
				focus: false,
				// 工单信息
				orderNo: '',
				orderName: '',
				orderId: '',
				// 工序选择使用
				storageList: [{
					id: 0,
					ProcessName: '请先选择工单'
				}],
				indexStorage: 0,
				processId: null,
				processName: '',
				woId: null,
				// 物料
				MaterialId: null,
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				PlanEndDate:'',
				// 
				orgId: null,
				// 
				userId: '',
				userName: '',
				personId: null,
				PersonnelId: null,
				//
				msg: '',
				// 产线
				LineCode: '',
				LineName: '',
				LineId: null,
				/* department */
				DepartmentId: 0,
				DepartmentCode: '',
				DepartmentName: ''
			}
		},
		onLoad() {
			// this.clearCach()
			// this.queryDepartment()
			uni.removeStorage({
				key: 'orderInfo'
			})
		},
		onShow() {
			// this.cancel();
			this.orgId = uni.getStorageSync('orgId');
			this.lastCurrentBadQty = null
			this.lastCurrentTotalQty = null
			this.planQty = null
			this.currentBadQty = null
			this.currentTotalQty = null
			uni.getStorage({
				key: 'orderInfo',
				success: o => {
					this.orderNo = o.data.orderNo;
					this.orderName = o.data.orderName;
					this.orderId = o.data.Id;
					this.MaterialId = o.data.MaterialId;
					this.MaterialCode = o.data.MaterialCode;
					this.MaterialName = o.data.MaterialName;
					this.Specification = o.data.Specification;
					this.PlanEndDate = o.data.PlanEndDate;
					this.planQty = o.data.PlanQty
					if(this.indexStorage == 0){
						this.getWO()
					}
				}
			})
			uni.getStorage({
				key: 'departmentInfo',
				success: o => {
					this.BenefitOrgId = o.data.OrgId
					this.DepartmentId = o.data.Id
					this.DepartmentCode = o.data.DepartmentCode
					this.DepartmentName = o.data.DepartmentName
				}
			});
			uni.getStorage({
				key: 'lineInfo',
				success: o => {
					this.LineId = o.data.id;
					this.LineCode = o.data.LineCode;
					this.LineName = o.data.LineName;
				}
			})
		},
		// watch: {
		// 	orderNo() {
		// 		this.lastCurrentBadQty = null
		// 		this.lastCurrentTotalQty = null
		// 		this.planQty = null
		// 		this.currentBadQty = null
		// 		this.currentTotalQty = null
		// 	}
		// },
		methods: {
			// 本工序可报总数
			// queryCurrentTotalQty(id) {
			// 	this.request({
			// 		url: '/PieceworkDetail/GetQty',
			// 		method: 'GET',
			// 		data: {
			// 			wo: this.orderNo,
			// 			processId: id
			// 		},
			// 		success: res => {
			// 			this.planQty = res.response.dataCounts
			// 		},
			// 		error: res => {
			// 			this.$error(res.msg)
			// 		}
			// 	})
			// },
			// 本道工序
			queryCurrentBadQty(id) {
				this.request({
					url: '/PieceworkMain/GetProcessWork',
					method: 'GET',
					data: {
						type: 1,
						wo: this.orderNo,
						processId: id
					},
					success: res => {
						this.currentBadQty = res.response.BadQty
						this.currentTotalQty = res.response.Qty
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 上道工序
			lastQueryCurrentBadQty(id) {
				this.request({
					url: '/PieceworkMain/GetProcessWork',
					method: 'GET',
					data: {
						type: 2,
						wo: this.orderNo,
						processId: id
					},
					success: res => {
						this.lastCurrentTotalQty = res.response.Qty
						this.lastCurrentBadQty = res.response.BadQty
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询我的报工明细
			queryDetail() {
				if (this.userId == '' || typeof(this.userId) == 'undefined') {
					this.$error('请先扫码人员工号')
				} else {
					uni.navigateTo({
						url: './detail?userId=' + this.PersonnelId + '&wo=' + this.orderNo + '&processName=' + this
							.processName
					})
				}
			},
			// 初始化弹窗数据
			showDialog(index) {
				const processId = this.workers[index].processId
				const departmentId = this.workers[index].DepartmentId
				const historyId = this.workers[index].historyId
				uni.setStorageSync('HI', historyId)
				uni.setStorageSync('INDEX', index)
				this.isShow = true
				this.getBadGroupList(processId, departmentId)
			},
			// 显示弹窗
			cancelDialog() {
				this.isShow = false
				uni.removeStorageSync('HI')
				uni.removeStorageSync('INDEX')
			},
			submit() {
				this.request({
					url: '/SNStationInfo/SetSinglePassStatus',
					method: 'POST',
					data: {
						id: this.processId,
						badItemId: this.radio,
						historyId: uni.getStorageSync('HI')
					},
					success: res => {
						this.$error(res.msg, () => {
							this.cancelDialog()
							const index = uni.getStorageSync('INDEX')
							let item = ''
							for (let i = 0; i < this.badItem.length; i++) {
								for (let j = 0; j < this.radio.length; j++) {
									if (this.badItem[i].value == this.radio[j]) {
										item += this.badItem[i].text
									}
								}
							}
							this.workers[index].item = item
						})
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// clearDialogData() {},
			changeSelect(e) {
				const badId = e
				if (badId) {
					this.getBadItemList(badId)
				}
			},
			changeRadio() {},
			getBadGroupList(processId, departmentId) {
				this.badGroup = []
				this.request({
					url: '/BadItem/GetBadGroupList',
					method: 'GET',
					data: {
						processId: processId,
						dId: departmentId
					},
					success: res => {
						for (let i = 0; i < res.response.length; i++) {
							let temp = {
								text: res.response[i].BadGroupName,
								value: res.response[i].Id,
							};
							this.badGroup.push(temp);
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			getBadItemList(badId) {
				this.badItem = []
				this.request({
					url: '/BadItem/GetBadItemList',
					method: 'GET',
					data: {
						processId: this.processId,
						dId: this.DepartmentId,
						id: badId
					},
					success: res => {
						for (let i = 0; i < res.response.length; i++) {
							let temp = {
								text: res.response[i].BadDesc,
								value: res.response[i].Id,
							};
							this.badItem.push(temp);
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询收益部门
			queryDepartment() {
				this.clearCach();
				this.LineId = null;
				this.LineCode = null;
				this.LineName = null;
				uni.navigateTo({
					url: '../../common/department/department'
				})
			},
			getLine() {
				uni.removeStorage({
					key: 'lineInfo',
				})
				uni.navigateTo({
					url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			},
			// 清缓存
			clearCach() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.removeStorage({
					key: 'departmentInfo'
				})
				uni.removeStorage({
					key: 'lineInfo'
				})
			},
			// 获取登录人员信息
			getUserInfo() {
				// let user = uni.getStorageSync('user');
				this.request({
					url: '/User/GetPesonByCode',
					method: 'GET',
					data: {
						code: this.userName
					},
					success: res => {
						if (res.response == null) {
							this.userId = '';
							this.userName = '';
							this.personId = null;
							this.$error('查无此人')
							return;
						}
						this.userId = res.response.Id;
						this.userName = res.response.PersonnelName;
						this.personId = res.response.PersonnelCode;
						this.PersonnelId = res.response.WorkNo
					},
					error: res => {
						this.$error(res.response.msg)
					}
				})
			},
			// 查询工单
			queryOrder() {
				uni.removeStorage({
					key: 'orderInfo'
				})
				uni.navigateTo({
					url: '../conmon/sgOrderQuery/sgOrderQuery?LineName=' + this.LineName
				})
			},
			// 获取工序
			getWO() {
				// this.lastCurrentBadQty = null
				// this.lastCurrentTotalQty = null
				// this.planQty = null
				// this.currentBadQty = null
				// this.currentTotalQty = null
				this.indexStorage = 0
				this.request({
					url: '/Process/GetWo',
					method: 'GET',
					data: {
						intPageSize: 9999,
						woid: this.orderId,
						page: 1
					},
					success: res => {
						const temp = res.response.data
						this.storageList = [{
							id: 0,
							ProcessName: '请先选择工单'
						}]
						for (let i = 0; i < temp.length; i++) {
							if (!Boolean(temp[i].IsBatch)) {
								this.storageList.push({
									id: temp[i].Id,
									ProcessName: temp[i].ProcessName
								})
							}
						}
						if (this.storageList.length < 2) {
							this.storageList = [{
								id: 1,
								ProcessName: '无可报工工序'
							}]
						} else if (this.storageList.length == 2) {
							this.storageList.shift()
							this.lastQueryCurrentBadQty(this.storageList[0].id)
							this.queryCurrentBadQty(this.storageList[0].id)
							this.processId = this.storageList[0].id
						} else {
							this.storageList.shift()
							this.storageList.unshift({
								id: 1,
								ProcessName: '请选择工序'
							})
						}
					},
					error: res => {
						uni.showToast({
							title: res.msg,
							icon: 'error'
						});
						this.msg = res.msg
						this.cancel()
					}
				})
			},
			validateProduct() {
				this.request({
					url: '/SNStationInfo/GetSn',
					method: 'GET',
					data: {
						materiaId: this.MaterialId,
						sn: this.SNCode,
						wo: this.orderNo
					},
					success: res => {
						this.focus = true;
						this.msg = '扫描物料存在于工单';
					},
					error: res => {
						this.focus = true;
						uni.showToast({
							title: '当前物料不存在于该工单',
							icon: 'error'
						});
						this.msg = '当前物料不存在于该工单';
						this.SNCode = '';
					}
				})
			},
			// 提交真实数量
			submitActualQty() {
				const that = this;
				if (this.orderNo == '') {
					uni.showToast({
						title: '请选择工单！',
						icon: 'loading'
					})
				} else if (this.storageList[this.indexStorage].ProcessName == '请先选择工单' || this.storageList[this
						.indexStorage].ProcessName == '请选择工序' || this.storageList[this.indexStorage].ProcessName ==
					'无可报工工序') {
					uni.showToast({
						title: '未获取到具体工序，无法报工！',
						icon: 'loading'
					})
				} else if (this.processId == null) {
					uni.showToast({
						title: '未获取到具体工序，无法报工！！',
						icon: 'loading'
					})
				} else if (this.SNCode == '') {
					uni.showToast({
						title: '请扫描产品码！',
						icon: 'loading'
					})
				} else if (this.userName == '') {
					uni.showToast({
						title: '未获取到当前登录人员！',
						icon: 'loading'
					})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要提交吗？', function(e) {
						console.log("Close confirm: " + e.index);
						//0==确认，否则取消  
						if (e.index == 0) {
							that.req()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要进行报工吗？？',
						success: function(res) {
							if (res.confirm) {
								that.req();
							} else if (res.cancel) {
								// console.log('用户点击取消');
							}
						}
					})
					// #endif
				}
			},
			// 提交请求
			req() {
				this.focus = false
				this.request({
					url: '/SNStationInfo/SinglePassPDA',
					method: 'POST',
					data: {
						WO: this.orderNo,
						ProcessId: this.processId,
						IsGood: true,
						IsHold: null,
						MateriaNumber: this.MaterialCode,
						MaterialId: this.MaterialId,
						LineId: this.LineId,
						SN: this.SNCode,
						personId: this.userId
					},
					success: res => {
						if (res.response.success) {
							// this.workers.push({
							// 	SNCode: this.SNCode,
							// 	processId: this.processId,
							// 	processName: this.processName,
							// 	DepartmentId: this.DepartmentId,
							// 	MaterialId: this.MaterialId,
							// 	MaterialCode: this.MaterialCode,
							// 	MaterialName: this.MaterialName,
							// 	Specification: this.Specification,
							// 	Status: '合格',
							// 	item: '',
							// 	historyId: res.response.response.historyId,
							// 	date: getTime()
							// })
							this.$toast(res.response.msg)
							setTimeout(()=>{
								this.msg = res.msg;
								this.SNCode = ''
								this.focus = true
								this.lastQueryCurrentBadQty(this.processId)
								this.queryCurrentBadQty(this.processId)
							}, 2000)
						} else {
							this.$error(res.response.msg)
							this.SNCode = ''
							this.focus = true
							this.msg = res.msg;
						}
					},
					error: res => {
						this.$error(res.msg)
						this.msg = res.msg;
						this.SNCode = ''
						this.focus = true
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.SNCode = ''
				this.orderNo = ''
				this.orderName = ''
				this.orderId = ''
				// 工序选择使用
				this.storageList = [{
					id: null,
					ProcessName: '请先选择工单'
				}];
				this.indexStorage = 0
				this.woId = null
				// 物料
				this.MaterialId = null
				this.MaterialCode = ''
				this.MaterialName = ''
				this.Specification = ''
				// 
				this.orgId = null
				this.user = ''
				this.msg = ''
			},
			/* picker */
			bindPickerChangeStorage: function(e, name) {
				let id = e.detail.value;
				// console.log('picker发送选择改变，携带值为', this.storageList[id].id);
				this.indexStorage = id;
				this.processId = this.storageList[id].id
				this.processName = this.storageList[id].ProcessName
				this.lastCurrentBadQty = null
				this.lastCurrentTotalQty = null
				// this.planQty = null
				this.currentBadQty = null
				this.currentTotalQty = null
				this.lastQueryCurrentBadQty(this.storageList[id].id)
				this.queryCurrentBadQty(this.storageList[id].id)
				// this.queryCurrentTotalQty(this.storageList[id].id)
			}
		}
	}
</script>

<style>

</style>
