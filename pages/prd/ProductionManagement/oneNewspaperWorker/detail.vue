<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-datetime-picker v-model="range" type="daterange" @change="change" @maskClick="maskClick" />
			<view style="display: flex;	align-items: center;">
				<view class="xj_button" style="width: 50%; color: green; font-size: 50upx;">良品数</view>
				<view class="xj_button" style="width: 50%; color: red; font-size: 50upx;">不良品数</view>
			</view>
			<view style="display: flex;	align-items: center;">
				<view class="xj_button" style="width: 50%; color: green; font-size: 40upx;">{{totalGood}}</view>
				<view class="xj_button" style="width: 50%; color: red; font-size: 40upx;">{{totalBad}}</view>
			</view>
			<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in info" :key="index">
				<view class="cu-form-group solid-bottom panel-full" @longtap="deleteItem(index)">
					<view class="content">
						<view class="text-black cu-form-data-10">产线：{{item.LineName}} </view>
						<view class="text-black cu-form-data-10">工单号：{{item.WO}} </view>
						<view class="text-black cu-form-data-10">工序：{{item.ProcessName}} </view>
						<view class="text-black cu-form-data-10">报工时间：{{item.PieceworkDate}}</view>
						<view class="text-black cu-form-data-10">合格数量：{{item.GoodQty}}</view>
						<view class="text-black cu-form-data-10">不合格数量：{{item.BadQty}}</view>
						<view class="text-black cu-form-data-10">产品码：{{item.SN}} </view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		dateFormat
	} from '../../../../common/js/util.js'
	export default {
		data() {
			return {
				userId: null,
				processId: null,
				wo: '',
				range: [],
				info: []
			}
		},
		onLoad(e) {
			this.wo = e.wo
			this.userId = e.userId;
			this.processName = e.processName;
			this.initDate();
			this.query();
		},
		computed: {
			totalGood(){
				let num = 0
				for (let i = 0; i < this.info.length; i++) {
					num = num + this.info[i].GoodQty
				}
				return num
			},
			totalBad(){
				let num = 0
				for (let i = 0; i < this.info.length; i++) {
					num = num + this.info[i].BadQty
				}
				return num
			},
		},
		methods: {
			initDate() {
				let begin = this.getDate({
					format: true
				});
				let end = this.getDate({
					format: true
				})
				this.range = [begin, end];
			},
			maskClick(e) {
				this.query();
			},
			change(e) {
				this.range = e
				this.query()
			},
			query() {
				this.request({
					url: '/PieceworkMain/GetPieceworkMains',
					method: 'GET',
					data: {
						wo: this.wo,
						processName: this.processName,
						pieceworkUserId: this.userId || 0,
						pieceworkDate: this.range[0] + ',' + this.range[1],
						inPageSize: 9999,
						inPageIndex: 1
					},
					success: res => {
						this.info = res.response.data;
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					day = day - 30;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
		}
	}
</script>

<style>

</style>
