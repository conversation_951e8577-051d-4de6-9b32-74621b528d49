<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar :focus="true" v-model="searchValue" @confirm="query"></uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content" style="width: 85%;">
								<view class="cu-form-data-14 uni-ellipsis">工单号: {{item.Wo}}</view>
								<view class="cu-form-data-14 uni-ellipsis">物料编码: {{item.MaterialCode}}</view>
								<view class="cu-form-data-14 uni-preWrap">物料名称: {{item.MaterialName}}</view>
								<view class="cu-form-data-14 uni-preWrap">规格: {{item.Specification}}</view>
								<view class="cu-form-data-14 uni-ellipsis">计划开工时间: {{item.PlanStartDate}}</view>
								<view class="cu-form-data-14 uni-ellipsis">计划完工时间: {{item.PlanEndDate}}</view>
								<view class="cu-form-data-14 uni-ellipsis">计划数量: {{item.PlanQty}}</view>
								<view class="cu-form-data-14 uni-ellipsis" v-show="item.SourceWo">来源工单: {{item.SourceWo}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Wo === currentOrderNo" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		data() {
			return {
				woStart: null,
				currentOrderNo: '',
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				obj: [],
				LineName:null,
				finish: '',
				// 搜索条件
				searchValue:'',
				startDate:'',
				// 数据量
				pageSize: 20
			}
		},
		onLoad(e) {
			this.woStart = e.woStart;
			this.startDate = e.startDate;
			this.LineName = e.LineName
			this.finish = e.finish
			this.query();
		},
		onShow() {
			this.query();
		},
		onReachBottom() {
			this.pageSize += 20
			this.query()
		},
		methods: {
			query(){
				this.request({
					url: '/WOMain/GetMaterialAndWOMainList',
					method: 'GET',
					data: {
						key: this.searchValue,
						LineName: this.LineName || null,
						status: 1
					},
					success: res => {
						this.obj = res.response
					},
					error: res =>{
						this.$error(res.msg)
					}
				})
			},
			radioChange(evt) {
				// console.log("123" + evt);
				var i = parseInt(evt.detail.value);
				var orderNo = this.obj[i].Wo;
				var orderName = this.obj[i].WOName;
				var id = this.obj[i].WOId;
				var MaterialCode = this.obj[i].MaterialCode;
				var MaterialName = this.obj[i].MaterialName;
				var Specification = this.obj[i].Specification;
				var PlanEndDate= this.obj[i].PlanEndDate;
				var MaterialId = this.obj[i].MaterialId;
				var ShipId = this.obj[i].ShipId;
				var PlanQty =  this.obj[i].PlanQty;
				uni.setStorage({
					key: "orderInfo",
					data: {
						"orderNo": orderNo,
						"orderName": orderName,
						"Id": id,
						"Specification": Specification,
						"MaterialId": MaterialId,
						"MaterialCode": MaterialCode,
						"MaterialName": MaterialName,
						"PlanEndDate": PlanEndDate,
						"ShipId": ShipId,
						"PlanQty": PlanQty
					}
				})
				// VUEX
				// debugger
				this.$store.commit('wo/setWO',{
					wo: orderNo,
					woName: orderName,
					Id: id,
					Specification: '',
					MaterialId: MaterialId,
					MaterialCode: MaterialCode,
					MaterialName: MaterialName,
					ShipId: ShipId
				})
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
