<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
				<view class="cu-form-group padding panel-full">
					<view style="width: 100%" @click="showDialog(index)">
						<view class="text-black uni-ellipsis">编码：{{ item.MaterialCode }}</view>
						<view class="text-black uni-ellipsis">名称：{{ item.MaterialName }}</view>
						<view class="text-black uni-ellipsis">规格：{{ item.Specification }}</view>
						<view class="text-black uni-ellipsis">数量：{{ item.Qty }}</view>
						<view class="text-black uni-ellipsis">问题点：{{ item.ProblemPoint }}</view>
					</view>
				</view>
			</view>
			<view class="cu-form-group uni-fixed-bottom">
				<uni-tag text="作废" type="warning" @click="submitAllCancel" />
				<uni-tag text="审批通过" type="success" @click="submitAllApproval" />
			</view>
			<uni-dialog :show="isShow">
				<view class="cu-form-group">
					<view class="title" style="width: max-content">不良分类</view>
					<uni-data-select style="background-color: #ffffff" v-model="value" :localdata="range" @change="change"></uni-data-select>
				</view>
				<view class="cu-form-group">
					<textarea v-model="badReason" placeholder="输入问不良原因" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">数量</view>
					<input v-model="qty" type="number" />
				</view>
				<view class="cu-form-group" v-if="MaterialReturnType == 1">
					<view class="title" style="width: max-content">工号</view>
					<input v-model="UserCode" placeholder="扫描工号获取" type="text" @confirm="getUserInfo()" />
					<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="scan" color="#8f8f94" size="25" />
				</view>
				<view class="cu-form-group" v-if="MaterialReturnType == 1">
					<view class="title" style="width: max-content">责任人</view>
					<input v-model="UserName" placeholder="扫描工号后获取" type="text" disabled="true" />
					<view></view>
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">物料项目</view>
					<uni-data-select
						style="background-color: #ffffff"
						v-model="ItemProject"
						:localdata="[
							{ value: '01', text: '原辅料' },
							{ value: '02', text: '铸件金加工类' }
						]"
					></uni-data-select>
					<view></view>
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">重要度</view>
					<uni-data-select
						style="background-color: #ffffff"
						v-model="Important"
						:localdata="[
							{ value: 'A', text: 'A' },
							{ value: 'B', text: 'B' },
							{ value: 'C', text: 'C' }
						]"
					></uni-data-select>
					<view></view>
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">日期</view>
					<picker mode="date" :value="FaultDate" @change="(e) => (FaultDate = e.detail.value)">
						<view class="uni-input">{{ FaultDate }}</view>
					</picker>
					<view></view>
				</view>

				<view class="cu-form-group" v-if="MaterialReturnType == 1 || MaterialReturnType == 2">
					<textarea v-model="question" placeholder="输入问题点" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">供应商</view>
					<input v-model="sup.search" placeholder="请选择" type="text" @confirm="sup.fetchSuggestions" />
					<picker mode="selector" :range="sup.option" range-key="value" @change="sup.selectItem" filterable>
						<view class="picker">
							选择
						</view>
					</picker>
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">嵌件费</view>
					<input v-model="insertFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">搪瓷费</view>
					<input v-model="enamelFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">铜套费</view>
					<input v-model="copperSleeveFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">电泳费</view>
					<input v-model="electrophoresisFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">点工费</view>
					<input v-model="pointWorkFee" type="number" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">赔偿比例</view>
					<input v-model="ratio" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">赔偿金额</view>
					<input v-model="amount" type="number" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">是否升级SQE</view>
					<uni-data-select
						style="background-color: #ffffff"
						v-model="isSqe"
						:localdata="[
							{ value: '0', text: '否' },
							{ value: '1', text: '是' }
						]"
					></uni-data-select>
					<view></view>
				</view>

				<view v-if="isSqe == 1">
					<view class="cu-form-group">
						<view class="title" style="width: max-content">升级原因</view>
						<textarea v-model="sqeReason" />
					</view>
					<view class="cu-form-group">
						<view class="title" style="width: max-content">SQE处理优先级</view>
						<input v-model="sqePriority" />
					</view>
					<view class="cu-form-group">
						<view class="title" style="width: max-content">SQE</view>
						<input v-model="user.search" placeholder="请选择" type="text" @confirm="user.fetchSuggestions" />
						<picker mode="selector" :range="user.option" range-key="value" @change="user.selectItem" filterable>
							<view class="picker">
								选择
								<!-- 当前选择：{{ this.sqeName }} -->
							</view>
						</picker>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">备注</view>
					<textarea v-model="remarks" placeholder="请输入备注" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">奖励金额</view>
					<input v-model="rewardAmount" type="number" />
				</view>

				<view class="cu-form-group">
					<uni-tag text="取消" type="warning" @click="cancel()" />
					<uni-tag text="确认" type="success" @click="submit()" />
				</view>
			</uni-dialog>
		</view>
	</view>
</template>

<script>
import uniDialog from '@/components/uni-dialog.vue';
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
import { deepCopyPromise } from '@/js/productUtil.js';
export default {
	components: {
		uniDialog,
		uniTag,
		UniIcons
	},
	data() {
		return {
			// 用户选择远程下拉框
			user: {
				option: [],
				search: '',
				// 远程方法
				fetchSuggestions: () => {
					this.request({
						url: '/SysUser/GetUserPageInfo?page=1&intPageSize=1000&key=' + this.user.search,
						method: 'GET',
						data: {
							page: 1,
							intPageSize: 1000,
							key: this.user.searche
						},
						success: (res) => {
							let tempArr = [];
							for (let i = 0; i < res.response.data.length; i++) {
								tempArr.push({ value: res.response.data[i].UserName, text: res.response.data[i].UserName });
							}
							this.user.option = tempArr;
						},
						error: (res) => {
							this.$error(res.msg);
						}
					});
				},
				selectItem: (item) => {
					this.user.search = this.user.option[item.detail.value].value;
					this.sqeName = this.user.search;
				}
			},
			// 供应商选择远程下拉框
			sup: {
				option: [],
				search: '',
				// 远程方法
				fetchSuggestions: () => {
					this.request({
						url: '/Supplier/Get?page=1&intPageSiz=1000&name=' + this.sup.search,
						method: 'GET',
						data: {
							page: 1,
							intPageSize: 1000,
							key: this.sup.search
						},
						success: (res) => {
							let tempArr = [];
							for (let i = 0; i < res.response.data.length; i++) {
								tempArr.push({ value: res.response.data[i].SupplierName, text: res.response.data[i].Id });
							}
							this.sup.option = tempArr;
						},
						error: (res) => {
							this.$error(res.msg);
						}
					});
				},
				selectItem: (item) => {
					this.sup.search = this.sup.option[item.detail.value].value;
					this.SupplierName = this.sup.option[item.detail.value].value;
					this.Supplier = this.sup.option[item.detail.value].text;
				}
			},
			/* 扩展组件 下拉框 */
			range: [],
			value: '',
			badReason: '', // 故障原因
			// 数量
			qty: 0,
			// 问题点
			question: '',
			// 用户
			UserId: null,
			UserCode: '',
			UserName: '',
			ItemProject: '', //物料项目
			Important: '', //重要度
			FaultDate: '', //故障日期
			insertFee: 0, // 嵌件费
			enamelFee: 0, // 搪瓷费
			copperSleeveFee: 0, // 铜套费
			electrophoresisFee: 0, // 电泳费
			pointWorkFee: 0, // 点工费
			ratio: 0, // 赔偿比例*
			amount: 0, // 赔偿金额
			isSqe: 0, // 是否Sqe
			sqeReason: '', // 升级原因
			sqePriority: '', // SQE处理优先级
			sqeName: '', // 负责SQE
			SqeId: '', // 负责SQE
			remarks: '', // 备注说明
			rewardAmount: 0, // 奖励金额
			Supplier: null, // 供应商
			SupplierName: '', // 供应商名称
			// 是否显示
			is: null,
			isShow: false,
			// 类型
			checkInfo: [],
			//
			obj: [],
			MaterialReturnType: null,
			Id: null,
			detailId: null
		};
	},
	onLoad(e) {
		this.Id = e.ProdReturnMainId;
		this.MaterialReturnType = e.MaterialReturnType;
		this.req(e.ProdReturnMainId);
		this.user.fetchSuggestions();
		this.sup.fetchSuggestions();
		this.getBadGroup();
	},
	onBackPress(options) {
		uni.navigateTo({
			url: '../../product'
		});
	},
	methods: {
		change(e) {},
		// 获取所有的不良分类
		getBadGroup() {
			this.request({
				url: '/BadGroup/GetEnableBadGroupAll',
				method: 'GET',
				success: (res) => {
					for (let i = 0; i < res.response.length; i++) {
						let temp = {
							text: res.response[i].Name,
							value: res.response[i].Id
						};
						this.range.push(temp);
					}
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 提交全部_审批
		submitAllApproval() {
			this.request({
				url: '/ProdReturn/ApprovalProdReturnOrder',
				method: 'POST',
				data: {
					Id: this.Id
				},
				success: (res) => {
					this.$error(res.msg, () => {
						uni.navigateBack({
							animationDuration: 1000
						});
					});
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 提交全部_作废
		submitAllCancel() {
			this.request({
				url: '/ProdReturn/CancelProdReturnOrder',
				method: 'POST',
				data: {
					Id: this.Id
				},
				success: (res) => {
					this.$error(res.msg, () => {
						uni.navigateBack({
							animationDuration: 1000
						});
					});
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 单个物料修改
		submit() {
			this.request({
				url: '/ProdReturn/ModifyProdReturnOrderDetail',
				method: 'PUT',
				data: [
					{
						Id: this.detailId,
						BadTypeId: this.value,
						PersonLiableCode: this.UserCode,
						PersonLiableName: this.UserName,
						Qty: this.qty,
						ProblemPoint: this.question,
						ItemProject: this.ItemProject,
						Important: this.Important,
						BadReason: this.badReason,
						InsertFee: this.insertFee, // 嵌件费
						EnamelFee: this.enamelFee, // 搪瓷费
						CopperSleeveFee: this.copperSleeveFee, // 铜套费
						ElectrophoresisFee: this.electrophoresisFee, // 电泳费
						PointWorkFee: this.pointWorkFee, // 点工费
						Ratio: this.ratio, // 赔偿比例*
						Amount: this.amount, // 赔偿金额
						IsSqe: this.isSqe, // 是否Sqe
						SqeReason: this.sqeReason, // 升级原因
						SqePriority: this.sqePriority, // SQE处理优先级
						SqeName: this.sqeName, // 负责SQE
						Remarks: this.remarks, // 备注说明
						Supplier: this.Supplier, // 供应商
						RewardAmount: this.rewardAmount, // 奖励金额
						FaultDate: this.FaultDate == '' ? '' : new Date(this.FaultDate) // 日期
					}
				],
				success: (res) => {
					this.$error(res.msg, this.req(this.Id));
					this.cancel();
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 获取登录人员信息
		getUserInfo() {
			this.request({
				url: '/EquipRepairMain/GetSysUser',
				method: 'GET',
				data: {
					acount: this.UserCode
				},
				success: (res) => {
					if (res.response == null) {
						this.UserId = '';
						this.UserName = '';
						this.UserCode = '';
						return;
					}
					this.UserId = res.response.Id;
					this.UserCode = res.response.UserAccount;
					this.UserName = res.response.UserName;
				},
				error: (res) => {
					this.$error('扫描出的条码格式错误！');
				}
			});
		},
		// 查询备料清单数据请求
		req(id) {
			this.request({
				url: '/ProdReturn/GetProdProdReturnDetailInfo',
				method: 'POST',
				data: {
					ProdReturnMainId: id
				},
				success: (res) => {
					this.obj = res.response;
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 清除弹窗数据
		clearDialogData() {
			// 数量
			this.qty = 0;
			// 问题点
			this.question = '';
			// 用户
			this.UserId = null;
			this.UserName = '';
			this.UserCode = '';
			this.value = '';
			this.ItemProject = ''; //物料项目
			this.Important = ''; //重要度
			this.FaultDate = ''; //故障日期
			this.insertFee = 0; // 嵌件费
			this.enamelFee = 0; // 搪瓷费
			this.copperSleeveFee = 0; // 铜套费
			this.electrophoresisFee = 0; // 电泳费
			this.pointWorkFee = 0; // 点工费
			this.ratio = 0; // 赔偿比例*
			this.amount = 0; // 赔偿金额
			this.isSqe = 0; // 是否Sqe
			this.sqeReason = ''; // 升级原因
			this.sqePriority = ''; // SQE处理优先级
			this.sqeName = ''; // 负责SQE
			this.remarks = ''; // 备注说明
			this.rewardAmount = 0; // 奖励金额
		},
		// 显示弹窗
		cancel() {
			this.isShow = false;
			this.clearDialogData();
		},
		// 初始化弹窗数据
		showDialog(index) {
			this.question = this.obj[index].ProblemPoint;
			this.UserCode = this.obj[index].PersonLiableCode;
			this.UserName = this.obj[index].PersonLiableName;
			this.qty = this.obj[index].Qty;
			this.value = this.obj[index].BadTypeId;
			this.isShow = true;
			this.detailId = this.obj[index].Id;
			this.ItemProject = this.obj[index].ItemProject; // 物料项目
			this.Important = this.obj[index].Important; // 重要度
			this.FaultDate = this.obj[index].FaultDate; // 故障日期
			this.insertFee = this.obj[index].InsertFee; // 嵌件费
			this.enamelFee = this.obj[index].EnamelFee; // 搪瓷费
			this.copperSleeveFee = this.obj[index].CopperSleeveFee; // 铜套费
			this.electrophoresisFee = this.obj[index].ElectrophoresisFee; // 电泳费
			this.pointWorkFee = this.obj[index].PointWorkFee; // 点工费
			this.ratio = this.obj[index].Ratio; // 赔偿比例*
			this.amount = this.obj[index].Amount; // 赔偿金额
			this.isSqe = this.obj[index].IsSqe; // 是否Sqe
			this.sqeReason = this.obj[index].SqeReason; // 升级原因
			this.sqePriority = this.obj[index].SqePriority; // SQE处理优先级
			this.sqeName = this.obj[index].SqeName; // 负责SQE
			this.remarks = this.obj[index].Remarks; // 备注说明R
			this.rewardAmount = this.obj[index].RewardAmount; // 奖励金额
			this.Supplier = this.obj[index].Supplier; // 供应商
			this.SupplierName = this.obj[index].SupplierName; // 供应商名称
		}
	}
};
</script>
<style>
.dropdown {
	position: absolute;
	width: 100%;
	max-height: 200px;
	overflow-y: auto;
	z-index: 1;
	background-color: #fff;
}
.dropdown-item {
	padding: 10px;
	border-bottom: 1px solid #ccc;
	text-align: left;
}
</style>
