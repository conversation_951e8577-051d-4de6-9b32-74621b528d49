<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title">部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title">产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="materialDetail()" v-show="wo">
				<view class="iconfont xj-mingxi xj_form_icon" />
				<view class="title">物料清单</view>
				<input disabled="true" placeholder="请选择物料" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="querywarehouseList">
				<view class="iconfont xj-cangku xj_form_icon" />
				<view class="title">退料仓库</view>
				<input v-model="storageLocation" disabled="true" placeholder="请手动选择仓库" />
				<uni-icons @click="querywarehouseList" class=" uni-panel-icon uni-icon alin_x_center" type="arrowright"
					color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title">退料单号</view>
				<input v-model="returnNo" placeholder="需要手输部分单号" @confirm="getReturnData"/>
			</view>
			<view class="cu-form-group">
				<view style="width: 15%;">类型</view>
				<uni-data-checkbox @change="change" mode="button" v-model="radio" :localdata="returnType">
				</uni-data-checkbox>
			</view>
			<view class="cu-form-group" v-show="obj.length != 0">
				<text class="text-xj cuIcon-titles">退料单列表</text>
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full padding-xs" @click="getDetailInfo(item.Id, item.MaterialReturnType)">
					<view style="width: 80%;">
						<view class = "text-black ds uni-ellipsis" >
							退料单号：{{item.ProdReturnOrder}}
						</view>
						<view class = "text-black uni-ellipsis" >退料仓库：{{item.ReturnWarehouseName}}</view>
						<view class = "text-black uni-ellipsis" >退料时间：{{item.CreateTime}}</view>
						<view class = "text-black uni-ellipsis" >退料人：{{item.CreateUserName}}</view>
					</view>
					<view class = "text-black  uni-ellipsis" style="20%" >
						<uni-tag text="工废" :inverted="true" type="warning" v-if="item.MaterialReturnType == 1" />
						<uni-tag text="料废" :inverted="true" type="primary" v-if="item.MaterialReturnType == 2" />
						<uni-tag text="合格" :inverted="true" type="success" v-if="item.MaterialReturnType == 3" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import vTabs from '@/components/v-tabs/v-tabs.vue'
	export default {
		components: {
			uniTag,
			UniIcons,
			vTabs
		},
		data() {
			return {
				returnNo:'',
				// 退料类型
				radio: null,
				returnType: [{
					text: '工废',
					value: 1
				}, {
					text: '料废',
					value: 2
				}, {
					text: '合格',
					value: 3
				}],
				obj: []
			}
		},
		computed: {
			// 工单信息
			woId() {
				return this.$store.state.wo.wo.woId;
			},
			wo() {
				return this.$store.state.wo.wo.wo;
			},
			woName() {
				return this.$store.state.wo.wo.woName;
			},
			// 产线
			LineCode() {
				return this.$store.state.line.line.lineCode;
			},
			LineName() {
				return this.$store.state.line.line.lineName;
			},
			LineId() {
				return this.$store.state.line.line.lineId;
			},
			// 车间、部门
			DepartmentId() {
				return this.$store.state.department.department.departmentId;
			},
			DepartmentCode() {
				return this.$store.state.department.department.departmentCode;
			},
			DepartmentName() {
				return this.$store.state.department.department.departmentName;
			},
			// 仓库
			storageId() {
				return this.$store.state.storage.storage.storageId;
			},
			storageCode() {
				return this.$store.state.storage.storage.storageCode;
			},
			storageLocation() {
				return this.$store.state.storage.storage.storageName;
			},
		},
		onLoad(e) {
			this.clearStore();
			this.getReturnData();
		},
		onShow() {
			this.getReturnData();
		},
		methods: {
			getDetailInfo(id, MaterialReturnType){
				uni.navigateTo({
					url:'materialDetail?ProdReturnMainId=' + id + '&MaterialReturnType=' + MaterialReturnType
				})
			},
			// 查询退料数据
			getReturnData(){
				this.request({
					url: '/ProdReturn/GetProdReturnMainInfo',
					method: 'POST',
					data: {
						WorkShopId: this.DepartmentId || '',
						ProdLineId: this.LineId || '',
						ReturnWarehouseId: this.storageId || '',
						MaterialReturnType: this.radio || '',
						ProdReturnOrder: this.returnNo
					},
					success: res => {
						this.obj = res.response
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 清空数据
			cancel() {
				const that = this;
				uni.showModal({
					title: '提示',
					content: '确定清空已选条件吗？',
					success: function(res) {
						if (res.confirm) {
							that.clearStore();
						} else if (res.cancel) {}
					}
				})
			},
			// 清 store
			clearStore() {
				this.$store.commit('department/empty');
				this.$store.commit('line/empty');
				this.$store.commit('storage/empty');
				this.$store.commit('wo/empty');
				this.$store.commit('returnMaterial/empty');
				this.$store.commit('radios/emptyRadioRerurnType');
			},
			// 
			change(e) {
				const that = this;
				this.getReturnData();
			},
			// 跳转仓库列表
			querywarehouseList() {
				uni.navigateTo({
					url: '../../common/warehouseList/warehouseList?warehouseCode=' + this.storageCode
				})
			},
			// 查询部门
			queryDepartment() {
				uni.navigateTo({
					url: '../../common/department/department'
				})
			},
			// 查询产线
			getLine() {
				uni.navigateTo({
					url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			}
		}
	}
</script>

<style>

</style>
