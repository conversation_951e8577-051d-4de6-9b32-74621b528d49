<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryU9()">
				<view class="iconfont xj-icon xj_form_icon" />
				<view class="title">U9单类型</view>
				<input v-model="u9Type" disabled="true" placeholder="请手动选择单据类型" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-zuzhi xj_form_icon" />
				<view class="title">受益组织</view>
				<!-- <input v-model="StorageName" disabled="true" placeholder="请手动选择组织"/>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" /> -->
				<uni-data-select style="background-color: #FFFFFF;" v-model="BenefitOrgId" :localdata="range">
				</uni-data-select>
			</view>
			<view class="cu-form-group" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title">受益部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryStorage()">
				<view class="iconfont xj-cangku xj_form_icon" />
				<view class="title">受益仓库</view>
				<input v-model="StorageName" disabled="true" placeholder="请手动选择收益仓库" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-xiangmu xj_form_icon" />
				<view class="title">受益项目</view>
				<input placeholder="请手动输入收益项目编号" v-model="SYOrderCode" @confirm="queryOrder()" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title">纸质单号</view>
				<input placeholder="请手动输入纸质单号" v-model="OrderCode" @confirm="queryOrder()" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-beizhu xj_form_icon" />
				<view class="title">备注</view>
				<input v-model="Remark" @confirm="queryOrder()" placeholder="选填" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="queryCarrierInfo()" placeholder="请手动扫描载具码" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in carrierMatinfo"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full ">
					<view class="content">
						<strong>
							<view class="text-black cu-form-data-10">料号：{{item.MaterialCode}} </view>
							<view class="text-black cu-form-data-10">名称：{{item.MaterialName}}</view>
							<view class="text-black cu-form-data-10">规格：{{item.Specification}}</view>
							<view class="text-black cu-form-data-10">仓库：{{item.StorageName}} </view>
						</strong>
						<view class="uni-list-cell " hover-class="uni-list-cell-hover"
							v-for="(subItem,subIndex) in item.Details" :key="subIndex">
							<view class="content">
								<view class="text-black cu-form-data-10">载具码：{{subItem.CarrierCode}}</view>
								<view class="text-black cu-form-data-10">发出数量：{{subItem.Qty}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group">
				<view class="xj_button" style="width: 100%; background-color: #009598;" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import {
		isRepeat,
		dataDivision
	} from '@/js/productUtil.js'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				/* 扩展组件 下拉框 */
				range: [],
				value: '',
				OrgId: '',
				carrierNo: '',
				OrderCode: '',

				SYOrderCode: '',
				/* U9 */
				u9Id: '',
				u9Type: '',
				/* department */
				BenefitOrgId: 0,
				DepartmentId: 0,
				DepartmentCode: '',
				DepartmentName: '',

				StorageName: '',

				Remark: '',
				/* 杂收详情 */
				carrierMatinfo: []
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		onLoad() {
			this.clearCath()
			this.getOrgData()
		},
		onShow() {
			uni.getStorage({
				key: 'u9Info',
				success: o => {
					this.u9Id = o.data.id,
						this.u9Type = o.data.label
				}
			});
			uni.getStorage({
				key: 'departmentInfo',
				success: o => {
					// this.BenefitOrgId = o.data.OrgId,
					this.DepartmentId = o.data.Id,
						this.DepartmentCode = o.data.DepartmentCode,
						this.DepartmentName = o.data.DepartmentName
				}
			});
			uni.getStorage({
				key: 'storageInfo',
				success: o => {
					this.StorageName = o.data.storageName
				}
			});
			this.OrgId = uni.getStorageSync('orgId')
		},
		methods: {
			getOrgData() {
				this.request({
					url: '/Org/GetOrgAllByTree',
					method: 'GET',
					success: res => {
						for (let i = 0; i < res.response.data.length; i++) {
							let temp = {
								text: res.response.data[i].label,
								value: res.response.data[i].id,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			clearCath() {
				uni.removeStorage({
					key: 'storageInfo'
				})
				uni.removeStorage({
					key: 'u9Info'
				})
				uni.removeStorage({
					key: 'departmentInfo'
				})
			},
			// 查询 U9 单据类型
			queryU9() {
				uni.removeStorage({
					key: 'u9Info',
					success() {
						console.log("U9 缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../../common/u9/u9?type=' + 'zf'
				})
			},
			// 查询收益部门
			queryDepartment() {
				uni.removeStorage({
					key: 'departmentInfo',
					success() {
						console.log("部门 缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../../common/department/department?isProduct=' + '2'
				})
			},
			// 根据当前登录组织ID查询收益仓库
			queryStorage() {
				uni.removeStorage({
					key: 'storageInfo',
					success() {
						console.log("仓库缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../../common/warehouseList/warehouseList?OrgId=' + this.OrgId
				})
			},
			// 查询载具信息
			queryCarrierInfo() {
				if (this.carrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				const app = this;
				app.syncRequest({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: app.carrierNo,
					},
					success: async res => {
						if (res.response === null) {
							uni.showToast({
								title: '未查询到数据',
								icon: 'error',
								duration: 1000
							})
							app.carrierNo = '';
							app.focus = true;
							return;
						}
						app.actualQty = res.response.Qty;
						let temp = {
							// 展示字段
							CarrierCode: res.response.CarrierCode,
							MaterialCode: res.response.MaterialCode,
							MaterialName: res.response.MaterialName,
							Specification: res.response.Specification,
							StorageName: res.response.StorageName,
							// 纸质单号
							OtherIssueCode: app.OrderCode,
							// 收益组织 ID
							BenefitOrgId: app.BenefitOrgId,
							// U9 单据类型
							OrderType: app.u9Id,
							// 部门 ID
							DepartmentId: app.DepartmentId,
							// 仓库 ID
							StorageId: res.response.StorageId,
							// 收益项目
							Item: app.SYOrderCode,
							// 备注
							Remark: app.Remark,
							// 杂发 1 杂收 2
							Type: 1,
							/* Details */
							// 登录组织 ID
							CarrierId: res.response.CarrierId,
							WOId: res.response.WOId,
							MaterialId: res.response.MaterialId,
							LotNo: res.response.LotNo,
							SupplierId: res.response.SupplierId,
							Qty: res.response.Qty,
							Reason: app.Remark,
							OrgId: app.OrgId,
							UnitId: res.response.UnitID
						};
						// debugger
						if (isRepeat(res.response.CarrierId, app.carrierMatinfo)) {
							// 处理数据格式
							const newCarrierMatinfo = await dataDivision(app.carrierMatinfo, temp, 1);
							app.carrierMatinfo = newCarrierMatinfo;
							app.carrierNo = '';
							app.focus = true;
						} else {
							uni.showToast({
								title: '重复扫码！',
								icon: 'error'
							})
							app.carrierNo = '';
							app.focus = true;
						}
					},
					error: res => {
						app.$error(res.msg, () => {
							app.carrierNo = ''
							app.focus = true
						})
					}
				})
			},
			// 退！
			submit() {
				let that = this;
				if (this.u9Type == '') {
					uni.showToast({
						title: '请选择 U9 单据类型！',
						icon: 'error'
					});
					return;
				} else if (this.DepartmentId == 0) {
					uni.showToast({
						title: '请选择收益部门！',
						icon: 'error'
					});
					return;
				// } else if (this.SYOrderCode == '') {
				// 	uni.showToast({
				// 		title: '请手动填写收益项目编号！',
				// 		icon: 'error'
				// 	});
				// 	return;
				// } else if (this.OrderCode == '') {
				// 	uni.showToast({
				// 		title: '请手动填写纸质单号！',
				// 		icon: 'error'
				// 	});
				// 	return;
				} else if (this.carrierMatinfo.length == 0) {
					uni.showToast({
						title: '请至少扫码一个载具！',
						icon: 'error'
					});
					return;
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认提交吗？', function(e) {
						//0==确认，否则取消  
						if (e.index == 0) {
							that.reqZF();
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
						// "verticalAlign":"bottom"
					});
					// #endif

					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要接收？',
						success: function(res) {
							if (res.confirm) {
								console.log('用户点击确定');
								// 执行逻辑
								that.reqZF();
							} else if (res.cancel) {
								console.log('用户点击取消');
								// 执行逻辑
							}
						}
					})
					// #endif 
				}
			},
			/* 请求 */
			reqZF() {
				this.request({
					url: '/OtherIssueMain/Post',
					method: 'POST',
					data: this.carrierMatinfo,
					success: res => {
						this.$audio.SemiFinishMiscellaneousCollection.success()
						this.$error(res.msg, () => {
							this.cancel()
						})
					},
					error: res => {
						this.$audio.SemiFinishMiscellaneousCollection.error()
						this.$error(res.msg, () => {
							this.msg = res.msg
							this.cancel()
						})
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = ''
				this.actualQty = ''
				this.carrierMatinfo = []
				this.OrgId = ''
				this.OrderCode = ''
				this.SYOrderCode = ''
				/* U9 */
				this.u9Id = ''
				this.u9Type = ''
				/* department */
				this.BenefitOrgId = 0
				this.DepartmentId = 0
				this.DepartmentCode = ''
				this.DepartmentName = ''
				this.StorageName = ''
				this.Remark = ''
			},
			// swiper method
			sswitch(index) {
				this.active = index;
			},
			showStyle(index) {
				if (this.active == index) {
					return "green";
				}
			}
		}
	}
</script>

<style>

</style>
