<!-- 生产发料 -->
<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-riqi_o xj_form_icon" />
				<view class="title">日期选择</view>
				<picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange">
					<view style="color: red;">{{date}}</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title">工单号</view>
				<input v-model="orderNo"    disabled="true" placeholder="请选择工单号"/>
				<uni-icons @click="queryOrder()" class=" uni-panel-icon uni-icon alin_x_center" type="search"
					color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="sendMaterial()" placeholder="请手动扫描载具码" :focus="focus"/>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="action margin-top" v-show="msg !== ''">
				<text class="cuIcon-title text-xj "></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<!-- 自定义 swiper -->
			<view class="margin-top">
				<uni-segmented-control :current="current" :values="items" :style-type="styleType"
					:active-color="activeColor" @clickItem="onClickItem" />
				<view v-if="current === 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover"
						v-for="(item,index) in invoiceDetails" :key="index">
						<view class="cu-form-group solid-bottom panel-full ">
							<view class="content">
								<view>料号：{{item.MaterialCode}}</view>
								<view>名称：{{item.MaterialName}} </view>
								<view>规格：{{item.Specification}} </view>
								<view>需求数量：{{item.PlanQty}} </view>
								<view>已发数量：{{item.ActualQty}} </view>
								<!-- <view>发货状态：{{item.ActualQty >= item.PlanQty ? '已完成':'未完成'}} </view> -->
							</view>
						</view>
					</view>
				</view>
				<view v-if="current === 1">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover"
						v-for="(item,index) in vehicleDetails" :key="index">
						<view class="cu-form-group solid-bottom panel-full " @click="detailInfo()">
							<view class="content">
								<view>载具码：{{item.CarrierCode}}</view>
								<view>规格：{{item.Specification}} </view>
								<view>料号：{{item.MaterialCode}} </view>
								<view>名称：{{item.MaterialName}} </view>
								<view>数量：{{item.ActualQty}} </view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				focus: false,
				carrierNo: '',
				orderNo: '',
				orderName: '',
				orderId: '',
				/* 发料单详情 */
				invoiceDetails: [],
				/* 载具详情 */
				vehicleDetails: [],
				date: currentDate,
				// 滑动模块
				items: ['备料详情', '发料详情'],
				current: 0,
				activeColor: '#009598',
				styleType: 'button'
			}
		},
		watch: {
		    orderNo: {
		        handler (newOrder, oldOrder) {
		            this.autoLoad();
		        },
		    }
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		onLoad() {
			uni.removeStorage({
				key: 'orderInfo'
			})
			this.cancel();
		},
		onShow() {
			uni.getStorage({
				key: 'orderInfo',
				success: o => {
					this.orderNo = o.data.orderNo;
					this.orderName = o.data.orderName;
					this.orderId = o.data.Id;
				}
			})
		},
		computed: {
			/* date picker */
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			// 自动加载数据
			 autoLoad() {
				 debugger;
				let that = this;
				if (that.orderNo !== '') {
					if(that.current == 0){
						that.queryShipOrderDetail(that.orderNo)
					} else {
						that.queryProductionIssueDetail(that.orderNo)
					}
				}
			},
			// 查询工单
			queryOrder() {
				uni.removeStorage({
					key: 'orderInfo',
					success() {
						console.log("工单缓存清理成功！");
					}
				})
				uni.navigateTo({
					
					url: '../../common/orderQuery/orderQuery?orderNo=' + this.orderNo + '&startDate=' + this.date + '&finish=1'
				})
			},
			/* 查询备料单信息 */
			queryShipOrderDetail(orderNo) {
				return new Promise((resolve, reject) => {
					this.request({
						url: '/ShipOrderDetail/Get',
						method: 'GET',
						data: {
							key: orderNo,
							intPageSize: 9999,
							page: 1
						},
						success: res => {
							this.invoiceDetails = res.response.data;
							resolve('success')
						},
						error: res => {
							this.$error(res.msg)
							this.msg = res.msg;
							reject('fail');
						}
					})
				})
			},
			/* 查询备料单发货载具信息 */
			queryProductionIssueDetail(orderNo) {
				return new Promise((resolve, reject) => {
					this.request({
						url: '/ProductionIssueDetail/Gets',
						method: 'GET',
						data: {
							wo: orderNo,
							intPageSize: 9999,
							page: 1
						},
						success: res => {
							this.vehicleDetails = res.response.data;
							resolve('success')
						},
						error: res => {
							this.$error(res.msg)
							this.msg = res.msg;
							reject('fail');
						}
					})
				})
			},
			/* 单个载具发料 */
			sendMaterial() {
				let that = this;
				that.focus = false
				if (this.orderNo == '') {
					uni.showToast({
						title: '请先选择工单！',
						icon: 'error'
					})
				} else {
					that.request({
						url: '/ProductionIssueDetail/Post',
						method: 'POST',
						data: {
							Wo: that.orderId,
							CarrierCode: that.carrierNo
						},
						success: res => {
							that.$audio.ProductionAndDelivery.success();
							if(that.current == 0){
								that.queryShipOrderDetail(that.orderNo)
							} else {
								that.queryProductionIssueDetail(that.orderNo)
							}
							that.$error(res.msg, () => {
								that.carrierNo = ''
								that.focus = true
							})
						},
						error: res => {
							that.$audio.ProductionAndDelivery.error();
							that.$error(res.msg)
							that.msg = res.msg;
							that.carrierNo = '';
						}
					})
				}
			},
			/* 查询物料载具详情 */
			detailInfo() {
				console.log("详情展示");
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = '';
				this.orderNo = '';
				this.msg = '';
				this.invoiceDetails = [];
				this.vehicleDetails = [];
			},
			onClickItem(e) {
				const app = this
				if (app.current !== e.currentIndex) {
					app.current = e.currentIndex
					if(e.currentIndex == 0){
						app.queryShipOrderDetail(app.orderNo)
					} else {
						app.queryProductionIssueDetail(app.orderNo)
					}
				}
			},
			/* date picker */
			bindDateChange: function(e) {
				this.date = e.detail.value
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			// swiper method
			sswitch(index) {
				this.active = index;
			},
			showStyle(index) {
				if (this.active == index) {
					return "green";
				}
			},
			onNavigationBarButtonTap(e) {
				if(!this.orderNo)
				{
					this.$error("请先选择工单！")
					return;
				}
				if(!this.carrierNo)
				{
					this.$error("请扫载具码！")
					return;
				}
			if (e.index == 0) {
				let pageUrl = '../../../index/miniAppPrint?wo=' + this.orderNo + '&carrierNo=' + this.carrierNo;
				uni.navigateTo({
					url: pageUrl
				});
			}
		},
		}
	}
</script>

<style>
	.ullist {
		display: flex;
		justify-content: space-around;
		font-size: 30rpx;
		color: $uni-text-color;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		background-color: #fff;
		width: 100%;
	}

	.active {
		color: $uni-color-primary;
		border-bottom: 4rpx solid $uni-color-primary;
	}
</style>
