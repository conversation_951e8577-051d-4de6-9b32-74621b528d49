<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="querywarehouseList">
				<view class="iconfont xj-cangku xj_form_icon" />
				<view class="title">仓库</view>
				<input v-model="storageLocation" @confirm="querywarehouseList" disabled="true" placeholder="请手动选择仓库" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-kuwei xj_form_icon" />
				<view class="title">库位</view>
				<input v-model="LocationName" placeholder="请手动扫描库位码" @confirm="queryLocationByCode" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="queryCarrierInfo()" :focus="focus" placeholder="请手动扫描载具码" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="action margin-top" v-show="msg != ''">
				<text class="cuIcon-title text-orange "></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in carrierMatinfo"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full " @longtap="deleteItem(index)">
					<view class="content">
						<view class="text-black ">载具码：{{item.CarrierCode}} </view>
						<view class="text-black ">工单号：{{item.WO}} </view>
						<view class="text-black ">料号：{{item.MaterialCode}} </view>
						<view class="text-black ">名称：{{item.MaterialName}}</view>
						<view class="text-black ">规格：{{item.Specification}}</view>
						<view class="text-black ">数量：{{item.Qty}}</view>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group">
				<view class="xj_button" style="width: 100%; background-color: #009598;" @click="submit">入库</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				storageId: null,
				storageCode: '',
				storageLocation: '',
				// 库位
				LocationId: null,
				LocationCode: '',
				LocationName: '',
				carrierNo: '',
				carrierMatinfo: [],
				focus: false,
				msg: ''
			}
		},
		onLoad() {
			uni.removeStorage({
				key: 'storageInfo'
			})
		},
		onShow() {
			uni.getStorage({
				key: 'storageInfo',
				success: o => {
					this.storageId = o.data.id;
					this.storageCode = o.data.storageCode;
					this.storageLocation = o.data.storageName;
					let that = this;
					// if (this.storageCode != '') {
					// 	that.queryStorageList();
					// }
				}
			})
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		methods: {
			queryLocationByCode() {
				this.request({
					url: '/Location/Get',
					method: 'GET',
					data: {
						intPageSize: 999,
						page: 1,
						key: this.LocationName
					},
					success: res => {
						if (res.response.data.length === 0) {
							uni.showToast({
								title: '未查找到该库位',
								icon: 'error'
							})
							this.msg = '未查找到该库位'
							return;
						}
						let temp = res.response.data[0];
						this.LocationId = temp.Id;
						this.LocationCode = temp.LocationCode;
						this.LocationName = temp.LocationName;
					},
					error: res => {
						this.$error(res.msg)
						this.msg = res.msg;
					}
				})
			},
			// 删除
			deleteItem(index) {
				let that = this;
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要删除所选项吗？', function(e) {
					//0==确认，否则取消  
					if (e.index == 0) {
						that.carrierMatinfo.splice(index, 1);
						// console.log(that.matList);
					} else {

					}
				}, {
					"title": '提示',
					"buttons": btns,
				});
				// #endif
			},
			// 跳转仓库列表
			querywarehouseList() {
				uni.navigateTo({
					url: '../../common/warehouseList/warehouseList?warehouseCode=' + this.storageCode
				})
			},
			// 查询载具信息
			queryCarrierInfo() {
				if (this.storageLocation == '' || this.storageLocation == '' || typeof this.storageLocation ==
					'undefined') {
					this.$toast('请先选择仓库')
					return
				}
				if (this.LocationName == '' || this.LocationName == '' || typeof this.LocationName == 'undefined') {
					this.$toast('请先选择库位')
					return
				}
				if (this.carrierNo == '') {
					this.$toast('请先输入载具码')
					return
				}
				this.syncRequest({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo,
					},
					success: async res => {
						if (res.response === null) {
							uni.showToast({
								title: '未查询到数据',
								icon: 'error',
								duration: 1000
							})
							this.carrierNo = '';
							this.focus = true;
							return;
						}
						this.focus = true;
						let temp = {
							CarrierId: res.response.CarrierId,
							CarrierCode: res.response.CarrierCode,
							WO: res.response.WO,
							Status: res.response.Status,
							MaterialCode: res.response.MaterialCode,
							MaterialName: res.response.MaterialName,
							Specification: res.response.Specification,
							Qty: res.response.Qty,
							Storage: this.storageId,
							MaterialId: res.response.MaterialId,
							LocationId: this.LocationId
						};
						if (this.isRepeat(res.response.CarrierCode, this.carrierMatinfo)) {
							this.carrierMatinfo.push(temp);
							this.carrierNo = '';
							this.focus = true;
							this.msg = res.msg;
						} else {
							uni.showToast({
								title: '重复扫码！',
								icon: 'error'
							})
						}
					},
					error: res => {
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 提交真实数量
			submit() {
				let that = this;
				if (this.carrierMatinfo.length == 0) {
					uni.showToast({
						title: '请至少扫码一个载具！',
						icon: 'error'
					})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要入库这些载具的物料吗？', function(e) {
						if (e.index == 0) {
							that.reqRK();
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要入库这些载具的物料吗？',
						success: function(res) {
					 	if (res.confirm) {
								that.reqRK();
							} else if (res.cancel) {
								// 执行逻辑
							}
						}
					})
					// #endif
				}
			},
			/* 入库请求 */
			reqRK() {
				this.request({
					url: '/StockInMain/AddStorage',
					method: 'POST',
					data: this.carrierMatinfo,
					success: res => {
						this.$audio.SemiFinishedProductsStorage.success();
						uni.showToast({
							title: res.msg,
							icon: 'success'
						});
						this.cancel();
					},
					error: res => {
						this.$audio.SemiFinishedProductsStorage.error();
						this.$error(res.msg)
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 
			isRepeat(carrierCode) {
				for (let i = 0; i < this.carrierMatinfo.length; i++) {
					if (carrierCode == this.carrierMatinfo[i].CarrierCode) {
						return false;
					} else {
						return true;
					}
				}
				return true
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = '';
				this.index = 0;
				this.carrierMatinfo = []
			}
		}
	}
</script>

<style>

</style>
