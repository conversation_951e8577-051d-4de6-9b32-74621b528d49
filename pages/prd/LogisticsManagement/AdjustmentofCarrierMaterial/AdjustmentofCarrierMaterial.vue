<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon"/>
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="queryCarrierInfo()" placeholder="请手动扫描载具码" :focus="focusOne" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-icon_caigoushuliang xj_form_icon"/>
				<view class="title"><text style="color: red;">*</text>实际数量</view>
				<input type="number" v-model="actualQty" placeholder="请手动填写载具实际数量" :focus="focusTwo" />
			</view>
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">				
					<text class="cuIcon-title text-xj"></text> 载具码{{carrierNo}}
				</view>
			</view>
			<view class="cu-form-group">
				<view>
					<view class="uni-ellipsis">车间：{{carrierMatinfo.DepartmentName}}</view>
					<view class="uni-ellipsis">工单号：{{carrierMatinfo.WO}}</view>		
					<view class="uni-ellipsis">料号：{{carrierMatinfo.MaterialCode}}</view>
					<view class="uni-ellipsis">规格：{{carrierMatinfo.Specification}}</view>
					<view class="uni-ellipsis">名称：{{carrierMatinfo.MaterialName}}</view>
					<view class="uni-ellipsis">来源：{{carrierMatinfo.Source}}</view>
					<view class="uni-ellipsis">数量：{{carrierMatinfo.Qty}}</view>								
				</view>
			</view>
			<!-- <view class="action margin-top">
				<text class="cuIcon-title text-orange "></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view> -->
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">取消</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submitActualQty">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				carrierNo: '',
				actualQty: '',
				carrierMatinfo: {},
				msg: '',
				// 焦点控制
				focusOne: true,
				focusTwo: false
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		methods: {
			// 查询载具信息
			queryCarrierInfo() {
				if (this.carrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo,
					},
					success: res => {
						this.carrierMatinfo = res.response
					},
					error: res => {
						this.$error(res.msg)
						this.cancel();
					}
				})
				this.changeBlur();
			},
			// 提交真实数量
			submitActualQty() {
				let that = this;
				if (this.actualQty == '') {
					uni.showToast({
						title: '请输入需要更改的实际数量！',
						icon: 'error'
					})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					var msg = '确认要更改这个载具物料的数量吗？';
					if(that.carrierMatinfo.DepartmentName != null)
					{
						msg = '该载具已发往' + that.carrierMatinfo.DepartmentName + '，是否调整？';
					}
					plus.nativeUI.confirm(msg, function(e) {
						//0==确认，否则取消  
						if (e.index == 0) {
							that.req()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					var msg = '确认要更改这个载具物料的数量吗？';
					if(that.carrierMatinfo.DepartmentName != null)
					{
						msg = '该载具已发往' + that.carrierMatinfo.DepartmentName + '，是否调整？';
					}
					uni.showModal({
						title: '提示',
						content: msg,
						success: function(res) {
					  if (res.confirm) {
								that.req()
							} else if (res.cancel) {}
						}
					})
					// #endif
				}
			},
			// 请求
			req() {
				this.request({
					url: '/Carrier/CarrierChangeRecordInfo',
					method: 'POST',
					data: {
						CarrierId:this.carrierMatinfo.CarrierId,
						CarrierCode: this.carrierNo,
						Number: Number(this.actualQty),
						MaterialId:this.carrierMatinfo.MaterialId,						
						MaterialCode:this.carrierMatinfo.MaterialCode,
						MaterialName:this.carrierMatinfo.MaterialName,
						Specification:this.carrierMatinfo.Specification
					},
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'success'
						});
						this.$audio.AdjustmentofCarrierMaterial.success();
						this.msg = '成功！';
						this.queryCarrierInfo();
						this.reset();
						this.changeBlur();
					},
					error: res => {
						this.$audio.AdjustmentofCarrierMaterial.error();
						this.msg = res.data;
						this.cancel();
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = '';
				this.actualQty = '';
				this.carrierMatinfo = {};
			},
			// 焦点处理
			changeBlur() {
				let that = this;
				if (this.carrierNo == '') {
					that.focusOne = true;
				} else if (this.actualQty == '') {
					that.focusTwo = true;
				}
			},
			// 
			reset() {
				this.carrierNo = '';
				this.actualQty = '';
			}
		}
	}
</script>

<style>

</style>
