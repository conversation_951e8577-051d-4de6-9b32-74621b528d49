<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view style="background-color: #fdf5e6; font-size: 13px; color: #de8c17; text-align: center">所选仓库：{{ storageLocation }}</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
				<view class="cu-form-group padding panel-full" v-show="item.Qty > 0">
					<view style="width: 90%" @click="showDialog(index)">
						<view class="text-black uni-ellipsis cu-form-data-10">编码：{{ item.MaterialCode }}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">名称：{{ item.MaterialName }}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">规格：{{ item.Specification }}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">数量：{{ item.Qty }}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">默认仓库：{{ item.StoreKeeperName }}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">仓库员：{{ item.DefaultStorageName }}</view>
					</view>
					<view style="width: 10%">
						<checkbox-group @change="checkboxChange($event, index)">
							<label>
								<checkbox :value="String(item.isReturn)" :checked="item.isReturn" />
							</label>
						</checkbox-group>
					</view>
				</view>
			</view>
			<view class="cu-form-group text-right uni-fixed-bottom">
				<uni-tag text="反选" type="warning" @click="checkAll" />
				<uni-tag text="完成" type="success" @click="submitAll" />
			</view>
			<uni-dialog :show="isShow">
				<view class="cu-form-group">
					<view class="title">数量</view>
					<input v-model="qty" type="number" />
				</view>
				<view class="cu-form-group" v-show="is == 1">
					<view class="title">工号</view>
					<input v-model="UserCode" placeholder="扫描工号获取" type="text" @confirm="getUserInfo()" />
					<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="scan" color="#8f8f94" size="25" />
				</view>
				<view class="cu-form-group" v-show="is == 1">
					<view class="title">责任人</view>
					<input v-model="UserName" placeholder="扫描工号后获取" type="text" disabled="true" />
					<view></view>
				</view>
				<view class="cu-form-group" v-show="is == 1 || is == 2">
					<textarea v-model="question" placeholder="输入问题点" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">物料项目</view>
					<uni-data-select
						style="background-color: #ffffff"
						v-model="ItemProject"
						:localdata="[
							{ value: '01', text: '原辅料' },
							{ value: '02', text: '铸件金加工类' }
						]"
					></uni-data-select>
					<view></view>
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">重要度</view>
					<uni-data-select
						style="background-color: #ffffff"
						v-model="Important"
						:localdata="[
							{ value: 'A', text: 'A' },
							{ value: 'B', text: 'B' },
							{ value: 'C', text: 'C' }
						]"
					></uni-data-select>
					<view></view>
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">日期</view>
					<picker mode="date" :value="FaultDate" @change="(e) => (FaultDate = e.detail.value)">
						<view class="uni-input">{{ FaultDate }}</view>
					</picker>
					<view></view>
				</view>

				<view class="cu-form-group" v-if="MaterialReturnType == 1 || MaterialReturnType == 2">
					<textarea v-model="question" placeholder="输入问题点" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">供应商</view>
					<input v-model="sup.search" placeholder="请选择" type="text" @confirm="sup.fetchSuggestions" />
					<picker mode="selector" :range="sup.option" range-key="value" @change="sup.selectItem" filterable>
						<view class="picker">
							选择
							<!-- 当前选择：{{ this.sqeName }} -->
						</view>
					</picker>
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">嵌件费</view>
					<input v-model="insertFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">搪瓷费</view>
					<input v-model="enamelFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">铜套费</view>
					<input v-model="copperSleeveFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">电泳费</view>
					<input v-model="electrophoresisFee" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">点工费</view>
					<input v-model="pointWorkFee" type="number" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">赔偿比例</view>
					<input v-model="ratio" type="number" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">赔偿金额</view>
					<input v-model="amount" type="number" />
				</view>

				<view class="cu-form-group">
					<view class="title" style="width: max-content">是否升级SQE</view>
					<uni-data-select
						style="background-color: #ffffff"
						v-model="isSqe"
						:localdata="[
							{ value: '0', text: '否' },
							{ value: '1', text: '是' }
						]"
					></uni-data-select>
					<view></view>
				</view>

				<view v-if="isSqe == 1">
					<view class="cu-form-group">
						<view class="title" style="width: max-content">升级原因</view>
						<textarea v-model="sqeReason" />
					</view>
					<view class="cu-form-group">
						<view class="title" style="width: max-content">SQE处理优先级</view>
						<input v-model="sqePriority" />
					</view>
					<view class="cu-form-group">
						<view class="title" style="width: max-content">SQE</view>
						<input v-model="user.search" placeholder="请选择" type="text" @confirm="user.fetchSuggestions" />
						<picker mode="selector" :range="user.option" range-key="value" @change="user.selectItem" filterable>
							<view class="picker">
								选择
								<!-- 当前选择：{{ this.sqeName }} -->
							</view>
						</picker>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">备注</view>
					<textarea v-model="remarks" placeholder="请输入备注" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">奖励金额</view>
					<input v-model="rewardAmount" type="number" />
				</view>

				<view class="cu-form-group">
					<uni-tag text="取消" type="warning" @click="cancel()" />
					<uni-tag text="确认" type="success" @click="submit()" />
				</view>
			</uni-dialog>
		</view>
	</view>
</template>

<script>
import uniDialog from '@/components/uni-dialog.vue';
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
import { deepCopyPromise } from '@/js/productUtil.js';
export default {
	components: {
		uniDialog,
		uniTag,
		UniIcons
	},
	data() {
		return {
			index: null,
			// 数量
			qty: 0,
			// 问题点
			question: '',

			badReason: '', // 故障原因
			// 用户
			UserId: null,
			UserCode: '',
			UserName: '',
			ItemProject: '', //物料项目
			Important: '', //重要度
			FaultDate: '', //故障日期
			insertFee: 0, // 嵌件费
			enamelFee: 0, // 搪瓷费
			copperSleeveFee: 0, // 铜套费
			electrophoresisFee: 0, // 电泳费
			pointWorkFee: 0, // 点工费
			ratio: 0, // 赔偿比例*
			amount: 0, // 赔偿金额
			isSqe: 0, // 是否Sqe
			sqeReason: '', // 升级原因
			sqePriority: '', // SQE处理优先级
			sqeName: '', // 负责SQE
			SqeId: '', // 负责SQE
			remarks: '', // 备注说明
			rewardAmount: 0, // 奖励金额
			Supplier: null, // 供应商
			SupplierName: '', // 供应商名称

			// 用户
			UserId: null,
			UserCode: '',
			UserName: '',
			// 是否显示
			is: null,
			isShow: false,
			// 类型
			checkInfo: [],
			//
			obj: [],
			// 用户选择远程下拉框
			user: {
				option: [],
				search: '',
				// 远程方法
				fetchSuggestions: () => {
					this.request({
						url: '/SysUser/GetUserPageInfo?page=1&intPageSize=1000&key=' + this.user.search,
						method: 'GET',
						data: {
							page: 1,
							intPageSize: 1000,
							key: this.user.searche
						},
						success: (res) => {
							let tempArr = [];
							for (let i = 0; i < res.response.data.length; i++) {
								tempArr.push({ value: res.response.data[i].UserName, text: res.response.data[i].UserName });
							}
							this.user.option = tempArr;
						},
						error: (res) => {
							this.$error(res.msg);
						}
					});
				},
				selectItem: (item) => {
					this.user.search = this.user.option[item.detail.value].value;
					this.sqeName = this.user.search;
				}
			},
			// 供应商选择远程下拉框
			sup: {
				option: [],
				search: '',
				// 远程方法
				fetchSuggestions: () => {
					this.request({
						url: '/Supplier/Get?page=1&intPageSiz=1000&name=' + this.sup.search,
						method: 'GET',
						data: {
							page: 1,
							intPageSize: 1000,
							key: this.sup.search
						},
						success: (res) => {
							let tempArr = [];
							for (let i = 0; i < res.response.data.length; i++) {
								tempArr.push({ value: res.response.data[i].SupplierName, text: res.response.data[i].Id });
							}
							this.sup.option = tempArr;
						},
						error: (res) => {
							this.$error(res.msg);
						}
					});
				},
				selectItem: (item) => {
					this.sup.search = this.sup.option[item.detail.value].value;
					this.SupplierName = this.sup.option[item.detail.value].value;
					this.Supplier = this.sup.option[item.detail.value].text;
				}
			}
		};
	},
	onLoad(e) {
		const id = e.id;
		this.is = e.isShow;
		this.req(id);
		this.user.fetchSuggestions();
		this.sup.fetchSuggestions();
		this.getBadGroup();
	},
	onBackPress(options) {
		uni.navigateTo({
			url: '../../product'
		});
	},
	computed: {
		storageLocation() {
			return this.$store.state.storage.storage.storageName;
		}
	},
	methods: {
		jump() {
			return new Promise((resolve) => {
				let temp = [];
				const that = this;
				for (let i = 0; i < that.obj.length; i++) {
					if (that.obj[i].isReturn) {
						if (typeof that.obj[i].detail != 'undefined') {
							if (that.obj[i].Qty > 0) {
								temp.push(that.obj[i]);
							}
						}
					}
				}
				resolve(temp);
			});
		},
		// 提交全部
		submitAll() {
			this.jump().then((res) => {
				if (res.length == 0) {
					uni.navigateBack({
						animationDuration: 300
					});
					return;
				} else {
					uni.setStorageSync('returnWOMat', encodeURIComponent(JSON.stringify(res)));
				}

				uni.navigateBack({
					animationDuration: 300
				});
			});
		},
		//全选
		checkAll() {
			const that = this;
			deepCopyPromise(that.obj).then((res) => {
				for (let i = 0; i < res.length; i++) {
					that.obj[i].isReturn = !that.obj[i].isReturn;
					if (Number(that.obj[i].Qty) <= 0) {
						continue;
					} else {
						if (!res[i].isReturn) {
							that.obj[i].WorkOrderId = this.$store.state.wo.wo.woId;
							that.obj[i].WorkOrderCode = this.$store.state.wo.wo.wo;
							that.obj[i].detail = [
								{
									qty: that.obj[i].Qty
								}
							];
						} else {
							that.obj[i].detail = null;
						}
					}
				}
			});
		},
		// 单个物料弹窗信息确定
		submit() {
			const that = this;
			if (that.is == 'true') {
				if (this.question === '' && this.UserName === '') {
					uni.showToast({
						title: '请输入工号和问题点',
						icon: 'loading'
					});
					that.isShow = !that.isShow;
					return;
				}
			}
			that.obj.forEach((item, indexx) => {
				if (!item.isReturn) {
					return;
				} else {
					if (item.Qty < that.qty) {
						uni.showToast({
							title: '数量不能大于剩余数量，目前剩余数量：' + item.Qty,
							icon: 'none'
						});
						item.isReturn = false;
						return;
					}
					item.WorkOrderId = this.$store.state.wo.wo.woId;
					item.WorkOrderCode = this.$store.state.wo.wo.wo;
					if (item.detail == null) {
						item.detail = [
							{
								qty: that.qty,
								question: that.question,
								workerCode: that.UserCode,
								UserName: that.UserName,
								Id: that.detailId,
								BadTypeId: that.value,
								PersonLiableCode: that.UserCode,
								PersonLiableName: that.UserName,
								Qty: that.qty,
								ProblemPoint: that.question,
								ItemProject: that.ItemProject,
								Important: that.Important,
								BadReason: that.badReason,
								InsertFee: that.insertFee, // 嵌件费
								EnamelFee: that.enamelFee, // 搪瓷费
								CopperSleeveFee: that.copperSleeveFee, // 铜套费
								ElectrophoresisFee: that.electrophoresisFee, // 电泳费
								PointWorkFee: that.pointWorkFee, // 点工费
								Ratio: that.ratio, // 赔偿比例*
								Amount: that.amount, // 赔偿金额
								IsSqe: that.isSqe, // 是否Sqe
								SqeReason: that.sqeReason, // 升级原因
								SqePriority: that.sqePriority, // SQE处理优先级
								SqeName: that.sqeName, // 负责SQE
								Remarks: that.remarks, // 备注说明
								Supplier: that.Supplier, // 供应商
								RewardAmount: that.rewardAmount, // 奖励金额
								FaultDate: that.FaultDate == '' ? '' : new Date(that.FaultDate) // 日期
							}
						];
					} else {
						if (that.index == indexx) {
							item.detail.push({
								qty: that.qty,
								question: that.question,
								workerCode: that.UserCode,
								UserName: that.UserName,
								Id: that.detailId,
								BadTypeId: that.value,
								PersonLiableCode: that.UserCode,
								PersonLiableName: that.UserName,
								Qty: that.qty,
								ProblemPoint: that.question,
								ItemProject: that.ItemProject,
								Important: that.Important,
								BadReason: that.badReason,
								InsertFee: that.insertFee, // 嵌件费
								EnamelFee: that.enamelFee, // 搪瓷费
								CopperSleeveFee: that.copperSleeveFee, // 铜套费
								ElectrophoresisFee: that.electrophoresisFee, // 电泳费
								PointWorkFee: that.pointWorkFee, // 点工费
								Ratio: that.ratio, // 赔偿比例*
								Amount: that.amount, // 赔偿金额
								IsSqe: that.isSqe, // 是否Sqe
								SqeReason: that.sqeReason, // 升级原因
								SqePriority: that.sqePriority, // SQE处理优先级
								SqeName: that.sqeName, // 负责SQE
								Remarks: that.remarks, // 备注说明
								Supplier: that.Supplier, // 供应商
								RewardAmount: that.rewardAmount, // 奖励金额
								FaultDate: that.FaultDate == '' ? '' : new Date(that.FaultDate) // 日期
							});
						}
					}
				}
			});
			// 弹窗关闭时，清除弹窗数据
			that.clearDialogData();
			that.isShow = !that.isShow;
		},
		// 获取登录人员信息
		getUserInfo() {
			this.request({
				url: '/EquipRepairMain/GetSysUser',
				method: 'GET',
				data: {
					acount: this.UserCode
				},
				success: (res) => {
					if (res.response == null) {
						this.$error('获取人员信息失败', () => {
							this.UserId = '';
							this.UserName = '';
							this.UserCode = '';
						});
						return;
					} else {
						this.UserId = res.response.Id;
						this.UserCode = res.response.UserAccount;
						this.UserName = res.response.UserName;
					}
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 查询备料清单数据请求
		req(id) {
			this.request({
				url: '/ProdReturn/GetProdStockList',
				method: 'POST',
				data: {
					WorkOrderId: id
				},
				success: (res) => {
					console.log(res);
					new Promise((resolve, reject) => {
						resolve(res.response);
					}).then((matsS) => {
						const mats = this.$store.state.returnMaterial.returnMaterial;
						if (Object.keys(mats).length == 0) {
							this.obj = matsS;
							return;
						} else {
							for (let i = 0; i < mats.materials.length; i++) {
								for (let j = 0; j < matsS.length; j++) {
									if (mats.materials[i].MaterialCode == matsS[j].MaterialCode) {
										matsS[j].Qty = Number(matsS[j].Qty) - Number(mats.materials[i].detail[0].qty);
										this.obj = matsS;
									}
								}
							}
						}
					});
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		clearDialogData() {
			// 数量
			this.qty = 0;
			// 问题点
			this.question = '';
			// 用户
			this.UserId = null;
			this.UserName = '';
			this.UserCode = '';
			this.value = '';
			this.ItemProject = ''; //物料项目
			this.Important = ''; //重要度
			this.FaultDate = ''; //故障日期
			this.insertFee = 0; // 嵌件费
			this.enamelFee = 0; // 搪瓷费
			this.copperSleeveFee = 0; // 铜套费
			this.electrophoresisFee = 0; // 电泳费
			this.pointWorkFee = 0; // 点工费
			this.ratio = 0; // 赔偿比例*
			this.amount = 0; // 赔偿金额
			this.isSqe = 0; // 是否Sqe
			this.sqeReason = ''; // 升级原因
			this.sqePriority = ''; // SQE处理优先级
			this.sqeName = ''; // 负责SQE
			this.remarks = ''; // 备注说明
			this.rewardAmount = 0; // 奖励金额
		},
		cancel() {
			this.isShow = !this.isShow;
			this.obj[this.index].isReturn = false;
		},
		// 显示弹窗
		showDialog(index) {
			if (!this.obj[index].isReturn) {
				uni.showToast({
					title: '选中的物料才可添加问题点',
					icon: 'none'
				});
				return;
			}
			this.index = index;

			this.qty = this.obj[index].Qty;
			this.question = this.obj[index].ProblemPoint;
			this.UserCode = this.obj[index].PersonLiableCode;
			this.UserName = this.obj[index].PersonLiableName;
			this.value = this.obj[index].BadTypeId;
			this.detailId = this.obj[index].Id;
			this.ItemProject = this.obj[index].ItemProject; // 物料项目
			this.Important = this.obj[index].Important; // 重要度
			this.FaultDate = this.obj[index].FaultDate; // 故障日期
			this.insertFee = this.obj[index].InsertFee; // 嵌件费
			this.enamelFee = this.obj[index].EnamelFee; // 搪瓷费
			this.copperSleeveFee = this.obj[index].CopperSleeveFee; // 铜套费
			this.electrophoresisFee = this.obj[index].ElectrophoresisFee; // 电泳费
			this.pointWorkFee = this.obj[index].PointWorkFee; // 点工费
			this.ratio = this.obj[index].Ratio; // 赔偿比例*
			this.amount = this.obj[index].Amount; // 赔偿金额
			this.isSqe = this.obj[index].IsSqe; // 是否Sqe
			this.sqeReason = this.obj[index].SqeReason; // 升级原因
			this.sqePriority = this.obj[index].SqePriority; // SQE处理优先级
			this.sqeName = this.obj[index].SqeName; // 负责SQE
			this.remarks = this.obj[index].Remarks; // 备注说明R
			this.rewardAmount = this.obj[index].RewardAmount; // 奖励金额
			this.Supplier = this.obj[index].Supplier; // 供应商
			this.SupplierName = this.obj[index].SupplierName; // 供应商名称

			this.isShow = !this.isShow;
		},
		// checkbox 发生变化
		checkboxChange: function (e, index) {
			const that = this;
			let values = e.detail.value;
			let res = Boolean(values[0]);
			if (res) {
				// that.obj[index].WorkOrderId = this.$store.state.wo.wo.woId;
				// that.obj[index].WorkOrderCode = this.$store.state.wo.wo.wo;
				that.obj[index].isReturn = true;
				that.showDialog(index);
			} else {
				that.obj[index].isReturn = false;
			}
		}
	}
};
</script>
