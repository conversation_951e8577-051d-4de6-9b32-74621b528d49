<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" :focus="!focus" @confirm="queryCarrierInfo()" placeholder="请手动扫描载具码" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-konghuowei xj_form_icon" />
				<view class="title">货位码</view>
				<input v-model="locationCode" :focus="focus" @confirm="submit()" placeholder="请手动扫描货码" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="action margin-top" v-show="msg != ''">
				<text class="cuIcon-title text-xj "></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<view class="cu-bar bg-white solid-bottom margin-top" v-show="focus">
				<view class="action">
					<text class="cuIcon-titles text-xj"></text> 载具详情
				</view>
			</view>
			<view class="cu-form-group" v-show="focus">
				<view class="title">载具码：</view>
				<view class="ds_center">{{carrierNo}}</view>
			</view>
			<view class="cu-form-group" v-show="focus">
				<view class="title">物料编码：</view>
				<view class="ds_center">{{MaterialCode}}</view>
			</view>
			<view class="cu-form-group" v-show="focus">
				<view class="title">物料名称：</view>
				<view class="ds_center">{{MaterialName}}</view>
			</view>
			<view class="cu-form-group" v-show="focus">
				<view class="title">规格：</view>
				<view class="ds_center">{{Specification}}</view>
			</view>
			<view class="cu-form-group" v-show="focus">
				<view class="title">是否在库：</view>
				<view class="ds_center">{{IsInStorage? '在库':'不在库'}}</view>
			</view>
			<view class="cu-form-group" v-show="IsInStorage">
				<view class="title">所属仓库：</view>
				<view class="ds_center">{{StorageName}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				// 载具码
				carrierNo: '',
				// 货位码
				locationCode: null,
				// 载具信息
				MaterialCode: null,
				MaterialName: null,
				Specification: null,
				IsInStorage: null,
				StorageId: null,
				StorageName: null,
				// 是否显示使用
				focus: false,
				// 错误信息提示
				msg: ''
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		onLoad() {

		},
		onShow() {

		},
		methods: {
			// 查询载具信息
			queryCarrierInfo() {
				this.focus = false
				if (this.carrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo,
					},
					success: res => {
						this.MaterialCode = res.response.MaterialCode;
						this.MaterialName = res.response.MaterialName;
						this.Specification = res.response.Specification;
						this.IsInStorage = res.response.IsInStorage;
						this.StorageName = res.response.StorageName;
						this.StorageId = res.response.StorageId;
						this.focus = true;
					},
					error: res => {
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 提交上架
			submit() {
				if (this.IsInStorage === 'false') {
					uni.showToast({
						title: '载具不在仓库，无法上架'
					})
					return;
				}
				let that = this;
				if (this.carrierNo == '') {
					uni.showToast({
						title: '请扫描载具码！',
						icon: 'error'
					});
					return;
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要上架？', function(e) {
						if (e.index == 0) {
							that.reqReturnWarehouse();
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要上架？',
						success: function(res) {
							if (res.confirm) {
								console.log('用户点击确定');
								// 执行逻辑
								that.reqReturnWarehouse();
							} else if (res.cancel) {
								console.log('用户点击取消');
								// 执行逻辑
							}
						}
					})
					// #endif
				}
			},
			/* 上架请求 */
			reqReturnWarehouse() {
				this.request({
					url: '/Storage/GetBindingStorage',
					method: 'GET',
					data: {
						locationCode: this.locationCode,
						sId: this.StorageId,
						carrierCode: this.carrierNo
					},
					success: res => {
						this.$audio.materialShelf.success();
						uni.showToast({
							title: res.msg,
							icon: 'success'
						});
						this.msg = res.msg;
						this.cancel();
					},
					error: res => {
						this.$audio.materialShelf.error();
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定'
						});
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = '';
				this.locationCode = null;
				this.MaterialCode = null;
				this.MaterialName = null;
				this.Specification = null;
				this.IsInStorage = null;
				this.StorageId = null;
				this.StorageName = null;
				this.focus = false;
			}
		}
	}
</script>

<style>

</style>
