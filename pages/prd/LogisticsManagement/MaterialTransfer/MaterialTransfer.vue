<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-riqi_o xj_form_icon" />
				<view class="title">日期选择</view>
				<picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange">
					<view style="color: red;">{{date}}</view>
				</picker>
			</view>
			<view class="cu-form-group" @click="getOrderInfo()">
				<view class="iconfont xj-zhuanru xj_form_icon" />
				<view class="title">转入工单号</view>
				<input v-model="inOrderNo" @confirm="getOrderInfo()" disabled="true" placeholder="请手动选择转入工单"/>
				<uni-icons  class="uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="queryCarrierInfo()" :focus="focus" placeholder="请手动扫描载具码"/>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-zhuanchu xj_form_icon" />
				<view class="title">转出工单号</view>
				<uni-data-select style="background-color: #FFFFFF;" v-model="outOrderNo" :localdata="range"  />
			</view>
			<view class="cu-bar bg-white solid-bottom" v-if="carrierMatinfo.length != 0">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 转移信息列表
				</view>
			</view>
			<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in carrierMatinfoShow"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full ">
					<view class="content">
						<strong>
							<view class="text-black cu-form-data-10">载具码：{{item.CarrierCode}}</view>
						</strong>
						<view class="text-black cu-form-data-10">物料编码：{{item.MaterialCode}} </view>
						<view class="text-black cu-form-data-10">物料名称：{{item.MaterialName}} </view>
						<view class="text-black cu-form-data-10">物料规格：{{item.Specification}} </view>
						<view class="text-black cu-form-data-10">转移数量：{{item.Qty}}</view>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group">
				<view class="xj_button" style="width: 100%; background-color: #009598;" @click="submit">物料转移</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				carrierNo: '',
				outOrderNo: '',
				inOrderNo: '',
				WOId:'',
				carrierMatinfo: [],
				carrierMatinfoShow: [],
				/* 时间 */
				date: currentDate,
				/* 错误信息 */
				msg: '',
				// 备料单 ID
				ShipId:	'',
				focus: false,
				range: []
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		onLoad() {
			uni.removeStorage({
				key: 'orderInfo',
				success: function (res) {
					console.log('success');
				}
			});
		},
		onShow() {
			uni.getStorage({
				key: 'orderInfo',
				success: o => {
					this.inOrderNo = o.data.orderNo;
					this.ShipId = o.data.ShipId;
				}
			})
		},
		computed: {
			/* date picker */
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			// 获取工单信息
			getOrderInfo() {
				uni.removeStorage({
					key: 'orderInfo',
					success() {
						console.log("工单缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../../common/orderQuery/orderQuery?orderNo=' + this.inOrderNo + '&startDate=' + this.date + '&woStart=' + 0
				})
			},
			// 查询载具信息
			queryCarrierInfo() {
				this.focus = false
				this.request({
					url: '/Carrier/GetProductionIssueCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo,
					},
					success: res => {
						for (let i = 0; i < res.response.length; i++) {
							let temp = {
								text: res.response[i].WO,
								value: res.response[i].WO,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						this.$error(res.msg, () => {
							this.cancel()
						})
						
					}
				})
			},
			/* 判断是否重复 */
			isRepeat(carrierCode, wo) {
				for (let i = 0; i < this.carrierMatinfo.length; i++) {
					if (carrierCode == this.carrierMatinfo[i].CarrierCode) {
						return false;
					} else if(this.carrierMatinfo[i].WO == wo){
						return false;
					} else {
						return true;
					}
				}
				return true
			},
			// 提交真实数量
			submit() {
				let that = this;
				if (this.inOrderNo == '') {
					that.$toast('请选择转入工单')
				} else if (this.outOrderNo == '') {
					that.$toast('未获取到转出工单，无法提交！')
				} else if (this.carrierMatinfo == {}) {
					that.$toast('请扫码需要转移的载具！')
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要转移这些载具的物料吗？', function(e) {
						console.log("Close confirm: " + e.index);
						if(e.index == 0){
							that.reqTransfer();
						} else {
							
						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要转移这些载具的物料吗？',
						success: function(res) {
							if (res.confirm) {
								that.reqTransfer();
							} else if (res.cancel) {
							}
						}
					})
					// #endif
				}
			},
			/* 请求 */
			reqTransfer(){
				this.request({
					url: '/CarrierWOTransferDetail/Post?formWo=' + this.outOrderNo + '&toWo=' + this.inOrderNo +
						'&carrCode=' + this.carrierNo,
					method: 'POST',
					success: res => {
						this.$audio.MaterialTransfer.success();
						this.$error(res.msg)
						this.cancel();
					},
					error: res => {
						this.$audio.MaterialTransfer.error();
						this.$error(res.msg)
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.inOrderNo = '';
				this.outOrderNo = '';
				this.carrierMatinfo = {};
				this.carrierMatinfoShow = {};
			},
			/* date picker */
			bindDateChange: function(e) {
				this.date = e.detail.value
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			/* picker */
			bindPickerChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			},
		}
	}
</script>

<style>

</style>
