<!--超领料发料-->
<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title">工单号</view>
				<input v-model="WO" disabled="true" placeholder="请选择工单号" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-xiangmu xj_form_icon" />
				<view class="title">超领料单号</view>
				<input v-model="RedoMaterialCode" disabled="true" placeholder="请选择工单号" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="sendMaterial()" placeholder="请手动扫描载具码" :focus="focus" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<!-- 自定义 swiper -->
			<view class="margin-top">
				<uni-segmented-control :current="current" :values="items" :style-type="styleType"
					:active-color="activeColor" @clickItem="onClickItem" />
				<view v-if="current === 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover"
						v-for="(item,index) in invoiceDetails" :key="index">
						<view class="cu-form-group solid-bottom panel-full"
							:class="showStyle(item.ActualQty, item.PlanQty)">
							<view class="content">
								<view class="text-black uni-ellipsis">叫料类型：{{item.RedoType == 0 ? '配比' : '杂发'}}</view>
								<view class="text-bold uni-ellipsis" v-if="item.DemandTime != null">
									需求时间：{{item.DemandTime}}</view>
								<view class="text-black uni-ellipsis">料号：{{item.MaterialCode}}</view>
								<view class="text-black uni-ellipsis">名称：{{item.MaterialName}}</view>
								<view class="text-black uni-ellipsis">规格：{{item.Specification}} </view>
								<view class="text-black uni-ellipsis">需求数量：{{item.PlanQty}} </view>
								<view class="text-black uni-ellipsis">已发数量：{{item.ActualQty}} </view>
								<view class="text-black uni-ellipsis">
									发货状态：{{item.RedoDetailStatus == 0 ? '待补料': item.RedoDetailStatus == 1 ? '补料中' : '补料完成'}}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="current === 1">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover"
						v-for="(item,index) in vehicleDetails" :key="index">
						<view class="cu-form-group solid-bottom panel-full " @click="detailInfo()">
							<view class="content">
								<view class="text-black cu-form-data-10">载具码：{{item.CarrierCode}}</view>
								<view class="text-black cu-form-data-10">规格：{{item.Specification}} </view>
								<view class="text-black cu-form-data-10">料号：{{item.MaterialCode}} </view>
								<view class="text-black cu-form-data-10">名称：{{item.MaterialName}} </view>
								<view class="text-black cu-form-data-10">数量：{{item.ActualQty}} </view>
								<view class="text-black cu-form-data-10">时间：{{item.CreateTime}} </view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue'
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				focus: false,
				carrierNo: '',
				/* 发料单详情 */
				invoiceDetails: [],
				/* 载具详情 */
				vehicleDetails: [],
				date: currentDate,
				// 滑动模块
				items: ['叫料详情', '发料详情'],
				current: 0,
				colorIndex: 0,
				activeColor: '#009598',
				styleType: 'button'
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		onLoad() {
			this.cancel()
		},
		onShow() {},
		computed: {
			Id() {
				return this.$store.state.superWO.wo.Id
			},
			WO() {
				return this.$store.state.superWO.wo.WO
			},
			WOStatus() {
				return this.$store.state.superWO.wo.WOStatus
			},
			RedoMaterialCode() {
				return this.$store.state.superWO.wo.RedoMaterialCode
			},
			Status() {
				return this.$store.state.superWO.wo.Status
			},
			RedoType() {
				return this.$store.state.superWO.wo.RedoType
			},
			/* date picker */
			startDate() {
				return this.getDate('start')
			},
			endDate() {
				return this.getDate('end')
			}
		},
		watch: {
			Id() {
				this.queryShipOrderDetail()
			},
			carrierNo() {
				// this.queryShipOrderDetail()
				// this.queryProductionIssueDetail()
			}
		},
		methods: {
			// 查询工单
			queryOrder() {
				uni.navigateTo({
					url: './WO'
				})
			},
			// 查询备料单信息 
			queryShipOrderDetail() {
				this.invoiceDetails = []
				return new Promise((resolve, reject) => {
					this.request({
						url: '/RedoMaterialMain/QueryWaitMainDetail',
						method: 'GET',
						data: {
							Id: this.Id
						},
						success: res => {
							this.invoiceDetails = res.response
							resolve('success')
						},
						error: res => {
							this.$error(res.msg)
							this.msg = res.msg
							reject('fail')
						}
					})
				})
			},
			// 查询备料单发货载具信息 
			queryProductionIssueDetail() {
				this.invoiceDetails = []
				return new Promise((resolve, reject) => {
					this.request({
						url: '/ProductionIssueDetail/QueryProductionIssueDetailByType',
						method: 'GET',
						data: {
							intPageSize: 9999,
							page: 1,
							redoMainId: this.Id,
							type: 3
						},
						success: res => {
							this.vehicleDetails = res.response.data
							resolve('success')
						},
						error: res => {
							this.$error(res.msg)
							this.msg = res.msg
							reject('fail')
						}
					})
				})
			},
			// 获取载具信息
			queryCarrierInfo() {
				return new Promise((resolve, reject) => {
					this.request({
						url: '/Carrier/GetCarrier',
						method: 'GET',
						data: {
							carrierCode: this.carrierNo,
						},
						success: res => {
							if (res.response === null) {
								reject('未查询到数据')
							} else if (res.response.IsInStorage === false) {
								reject('当前载具不在仓库')
							} else if (res.response.Qty === 0) {
								reject('当前载具无物料')
							} else {
								resolve(res.response)
							}
						},
						error: res => {
							reject(res.msg)
						}
					})
				})
			},
			// 获取载具物料对应的 mainId,detailId
			getInfo(id) {
				debugger;
				const app = this
				let ids = []
				for (let [index, item] of app.invoiceDetails.entries()) {
					if (item.MaterialId == id) {
						ids.push({
							RedoMainId: item.RedoMainId,
							RedoDetailId: item.RedoDetailId
						})
					}
				}
				if (ids.length != 0) {
					return Promise.resolve(ids)
				} else {
					return Promise.reject('该载具所有物料与所需物料不符，无法发料')
				}
			},
			// 单个载具发料 
			sendMaterial() {
				let app = this
				app.focus = false
				if (app.WO == '') {
					uni.showToast({
						title: '请先选择工单！',
						icon: 'error'
					})
				} else {
					app.queryCarrierInfo().then(res => {
						app.getInfo(res.MaterialId).then(res => {
							app.reqSuper(res[0].RedoMainId, res[0].RedoDetailId)
						}, error => {
							app.$error(error, () => {
								app.carrierNo = ''
								app.focus = true
							})
						})
					}, error => {
						app.$error(error, () => {
							app.carrierNo = ''
							app.focus = true
						})
					})
				}
			},
			reqSuper(RedoMainId, RedoDetailId) {
				const app = this
				app.request({
					url: '/RedoMaterialMain/AddRedoIssueDetail',
					method: 'POST',
					data: {
						Wo: app.WO,
						CarrierCode: app.carrierNo,
						RedoMainId: RedoMainId,
						RedoDetailId: RedoDetailId
					},
					success: res => {
						app.$audio.ProductionAndDelivery.success()
						app.$success(res.msg)
						app.queryProductionIssueDetail()
						app.queryShipOrderDetail()
						//app.carrierNo = ''
					},
					error: res => {
						app.$audio.ProductionAndDelivery.error()
						app.$error(res.msg, () => {
							app.msg = res.msg
							app.carrierNo = ''
						})
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = ''
				this.orderNo = ''
				this.msg = ''
				this.invoiceDetails = []
				this.vehicleDetails = []
			},
			onClickItem(e) {
				console.log(e);
				if (e.currentIndex == 1) {
					this.queryProductionIssueDetail()
				} else {
					this.queryShipOrderDetail()
				}
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex
				}
			},
			/* date picker */
			bindDateChange: function(e) {
				this.date = e.detail.value
			},
			getDate(type) {
				const date = new Date()
				let year = date.getFullYear()
				let month = date.getMonth() + 1
				let day = date.getDate()

				if (type === 'start') {
					year = year - 60
				} else if (type === 'end') {
					year = year + 2
				}
				month = month > 9 ? month : '0' + month
				day = day > 9 ? day : '0' + day
				return `${year}-${month}-${day}`
			},
			// swiper method
			sswitch(index) {
				this.active = index
			},
			showStyle(AQ, PQ) {
				if (AQ == null) {
					return 'blue'
				}
				if (AQ >= PQ) {
					return "green"
				} else {
					return "yellow"
				}
			},
			onNavigationBarButtonTap(e) {
				if (!this.WO) {
					this.$error("请先选择工单！")
					return;
				}
				if (!this.carrierNo) {
					this.$error("请扫载具码！")
					return;
				}
				if (e.index == 0) {
					let pageUrl = '../../../index/miniAppPrint?wo=' + this.orderNo + '&carrierNo=' + this.carrierNo;
					uni.navigateTo({
						url: pageUrl
					});
				}
			},
		}
	}
</script>
