<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar :focus="true" v-model="searchValue" @confirm="query"></uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="uni-ellipsis">工单号: {{item.WO}}</view>
								<view class="uni-ellipsis">超领料单号: {{item.RedoMaterialCode}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Wo === currentOrderNo" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	export default {
		components: {
			dsNone
		},
		data() {
			return {
				woStart: null,
				currentOrderNo: '',
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				obj: [],
				LineName: null,
				// 搜索条件
				searchValue: '',
				startDate: ''
			}
		},
		onLoad(e) {
			this.query();
		},
		onShow() {
			this.query();
		},
		methods: {
			query() {
				this.request({
					url: '/RedoMaterialMain/QueryWaitMain',
					method: 'GET',
					data:{
						key: this.searchValue
					},
					success: res => {
						this.obj = res.response;
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var WO = this.obj[i].WO;
				var WOStatus = this.obj[i].WOStatus;
				var Id = this.obj[i].Id;
				var RedoMaterialCode = this.obj[i].RedoMaterialCode;
				var Status = this.obj[i].Status;
				var RedoType = this.obj[i].RedoType;
				// debugger
				this.$store.commit('superWO/setWO', {
					Id: Id,
					WO: WO,
					WOStatus: WOStatus,
					RedoMaterialCode: RedoMaterialCode,
					Status: Status, // 0 待补料；1 补料中；2 补料完成
					RedoType: RedoType	// 0 配比单 1 杂发单
				})
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
