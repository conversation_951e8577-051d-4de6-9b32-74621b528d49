<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<view class="iconfont xj-type xj_form_icon" />
				<view class="title">调拨类型</view>
				<uni-data-select style="background-color: #FFFFFF;" v-model="value" :localdata="range" @change="change" />
			</view>
			<view class="cu-form-group" @click="querywarehouseList">
				<view class="iconfont xj-zhuanru xj_form_icon" />
				<view class="title">转入仓库</view>
				<input v-model="storageLocation" disabled="true" placeholder="请选择转入仓库" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="search"
					color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">载具码</view>
				<input v-model="carrierNo" @confirm="queryCarrierInfo()" :focus="focus" placeholder="请手动扫描载具码" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-zhuanchu xj_form_icon" />
				<view class="title">转出仓库</view>
				<input v-model="outStorageLocation" disabled="true" placeholder="扫描载具码后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-beizhu xj_form_icon" />
				<view class="title">备注</view>
				<input v-model="Remark" placeholder="选填" />
			</view>
			<view class="action margin-top" v-show="msg">
				<text class="cuIcon-title text-xj "></text> 消息提示：
				<text style="color:#E3162E;">{{msg}}</text>
			</view>
			<view class="uni-list-cell " hover-class="uni-list-cell-hover"
				v-for="(item,index) in carrierMatinfo.Details" :key="index">
				<view class="cu-form-group solid-bottom panel-full ">
					<view class="content">
						<view class="uni-ellipsis">载具码：{{item.CarrierCode}} </view>
						<view class="uni-ellipsis">工单号：{{item.WO}} </view>
						<view class="uni-ellipsis">料号：{{item.MaterialCode}} </view>
						<view class="uni-ellipsis">名称：{{item.MaterialName}}</view>
						<view class="uni-ellipsis">规格：{{item.Specification}}</view>
						<view class="uni-ellipsis">数量：{{item.AllotQty}}</view>
						<view class="uni-ellipsis">备注：{{item.Remark}}</view>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">取消</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				/* 扩展组件 下拉框 */
				range: [],
				value: '',
				storageId: null,
				storageCode: '',
				storageLocation: '',
				carrierNo: '',
				carrierMatinfo: {
					WOId: '',
					OutStorgeId: '',
					InStorgeId: '',
					Remark: '',
					OrgId: '',
					Details: []
				},
				focus: false,
				msg: '',
				outStorageLocation: '',
				Remark: '',
				OrgId: ''
			}
		},
		onLoad() {
			uni.removeStorage({
				key: 'storageInfo',
			});
			this.reqGETDR()
		},
		onShow() {
			const app = this
			app.focus = false
			app.loadLocationStorage().then(() => {
				app.OrgId = uni.getStorageSync('orgId')
				// 展示主页面时，如果转入仓库已选，就自动定位焦点
				if(app.storageLocation){
					app.focus = true
				}
			},error => {
				app.$error('仓库数据加载失败，请重新选择转入仓库')
			})
		},
		methods: {
			change(e) {
				// console.log("e:", e);
				// uni.setStorageSync('orgId', e)
			},
			// 加载仓库本地缓存
			loadLocationStorage(){
				return new Promise((resolve, reject) => {
					uni.getStorage({
						key: 'storageInfo',
						success: o => {
							this.storageId = o.data.id;
							this.storageCode = o.data.storageCode;
							this.storageLocation = o.data.storageName;
							// let that = this;
							// if (this.storageCode != '') {
							// 	that.queryStorageList();
							// }
							resolve()
						},
						error: res => {
							reject()
						}
					})
				})
			},
			// 跳转仓库列表
			querywarehouseList() {
				if (this.carrierMatinfo.Details.length !== 0) {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('重新选择仓库，将清空已经扫码的载具，确认选择吗？', function(e) {
						//0==确认，否则取消  
						if (e.index == 0) {
							console.log(this.value)
							// 跨组织调拨
							if(this.value == 'DR01'){
								uni.navigateTo({
									url: '../../common/warehouseList/getAllWarehouse?warehouseCode=' + this
										.storageCode
								})
							}
							else{
								uni.navigateTo({
									url: '../../common/warehouseList/warehouseList?warehouseCode=' + this
										.storageCode
								})
							}
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '重新选择仓库，将清空已经扫码的载具，确认选择吗？',
						success: function(res) {
							if (res.confirm) {
								console.log(this.value)
								// 跨组织调拨
								if(this.value == 'DR01'){
									uni.navigateTo({
										url: '../../common/warehouseList/getAllWarehouse?warehouseCode=' + this
											.storageCode
									})
								}
								else{
									uni.navigateTo({
										url: '../../common/warehouseList/warehouseList?warehouseCode=' + this
											.storageCode
									})
								}
							} else if (res.cancel) {
								return;
							}
						}
					})
					// #endif
				} else {
					console.log(this.value)
					// 跨组织调拨
					if(this.value == 'DR01'){
						uni.navigateTo({
							url: '../../common/warehouseList/getAllWarehouse?warehouseCode=' + this
								.storageCode
						})
					}
					else{
						uni.navigateTo({
							url: '../../common/warehouseList/warehouseList?warehouseCode=' + this
								.storageCode
						})
					}
				}
			},
			// 查询载具信息
			queryCarrierInfo() {
				this.focus = false
				if(this.value == ''){
					this.$toast('请先选择调拨类型')
					return
				}
				if(this.carrierNo == ''){
					this.$toast('未获取到载具码')
					return
				}
				this.syncRequest({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo,
					},
					success: async res => {
						if (res.response === null) {
							uni.showToast({
								title: '未查询到数据',
								icon: 'error',
								duration: 1000
							})
							this.carrierNo = '';
							this.focus = true;
							return;
						}
						if (!this.isRepeat(res.response.CarrierCode, this.carrierMatinfo.Details)) {
							uni.showToast({
								title: '重复扫码！',
								icon: 'error'
							})
							this.carrierNo = '';
							this.focus = true;
							return;
						}
						this.focus = true;
						this.outStorageLocation = res.response.StorageName;
						let that = this;
						if (this.carrierMatinfo.Details.length == 0) {
							that.carrierMatinfo = {
								WOId: res.response.WOId,
								OutStorgeId: res.response.StorageId,
								InStorgeId: that.storageId,
								Remark: that.Remark,
								OrgId: that.OrgId,
								AllotNo: that.value,
								Details: [{
									CarrierId: res.response.CarrierId,
									CarrierCode: res.response.CarrierCode,
									MaterialId: res.response.MaterialId,
									WO: res.response.WO,
									MaterialCode: res.response.MaterialCode,
									MaterialName: res.response.MaterialName,
									Specification: res.response.Specification,
									AllotQty: res.response.Qty,
									Remark: that.Remark,
									OrgId: that.OrgId
								}]
							}
						} else {
							let temp = {
								CarrierId: res.response.CarrierId,
								CarrierCode: res.response.CarrierCode,
								MaterialId: res.response.MaterialId,
								WO: res.response.WO,
								MaterialCode: res.response.MaterialCode,
								MaterialName: res.response.MaterialName,
								Specification: res.response.Specification,
								AllotQty: res.response.Qty,
								OrgId: that.OrgId,
								Remark: that.Remark								
							};
							this.carrierMatinfo.Details.push(temp);
						}
						this.carrierNo = '';
						this.focus = true;
						this.msg = res.msg;
					},
					error: res => {
						this.$error(res.msg)
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 提交真实数量
			submit() {
				let that = this;
				if (this.carrierMatinfo.Details.length == 0) {
					that.$toast('请至少扫码一个载具！')
				} else if (this.storageLocation == '') {
					that.$toast('请选择转入仓库！')
				} else {
					uni.showModal({
						title: '提示',
						content: '确认要入库这些载具的物料吗？',
						success: function(res) {
							if (res.confirm) {
								// 执行逻辑
								that.reqRK();
							} else if (res.cancel) {
								// 执行逻辑
							}
						}
					})
				}
			},
			/* 入库请求 */
			reqRK() {
				this.request({
					url: '/AllotMain/Post',
					method: 'POST',
					data: this.carrierMatinfo,
					success: res => {
						this.$audio.AllocationOfSemiFinishedProducts.success();
						uni.showToast({
							title: res.msg,
							icon: 'success'
						});
						this.cancel();
					},
					error: res => {
						this.$audio.AllocationOfSemiFinishedProducts.error();
						this.$error(res.msg)
						this.msg = res.msg;
						// this.cancel();
					}
				})
			},
			/* 调拨类型 */
			reqGETDR() {
				this.request({
					url: '/AllotMain/GetDR',
					method: 'GET',
					success: res => {
						for (let i = 0; i < res.response.length; i++) {
							let temp = {
								text: res.response[i].ItemName,
								value: res.response[i].ItemValue,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 
			isRepeat(carrierCode) {
				if (this.carrierMatinfo.Details.length == 0) {
					return true
				}
				for (let i = 0; i < this.carrierMatinfo.Details.length; i++) {
					if (carrierCode == this.carrierMatinfo.Details[i].CarrierCode) {
						return false;
					} else {
						return true;
					}
				}
				return true
			},
			// 取消，初始化页面
			cancel() {
				this.carrierMatinfo = {
					WOId: '',
					OutStorgeId: '',
					InStorgeId: '',
					Remark: '',
					OrgId: '',
					Details: []
				}
				this.carrierNo = ''
				this.index = 0
				this.msg = ''
			}
		}
	}
</script>

<style>

</style>
