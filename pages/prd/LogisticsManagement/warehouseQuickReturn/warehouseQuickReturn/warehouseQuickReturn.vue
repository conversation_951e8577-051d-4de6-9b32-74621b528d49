<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="querywarehouseList">
				<view class="iconfont xj-cangku xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>退料仓库</view>
				<input v-model="storageLocation" disabled="true" placeholder="请手动选择仓库" />
				<uni-icons @click="querywarehouseList" class=" uni-panel-icon uni-icon alin_x_center" type="arrowright"
					color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>工单号</view>
				<input v-model="wo" disabled="true" placeholder="请选择工单号" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<!-- <view class="iconfont xj-type xj_form_icon" /> -->
				<view style="width: 15%;"><text style="color: #E3162E;">*</text>类型</view>
				<uni-data-checkbox @change="change" mode="button" v-model="radio" :localdata="returnType" />
			</view>
			<view class="cu-form-group" @click="materialDetail()" v-show="wo">
				<view class="iconfont xj-mingxi xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>物料清单</view>
				<input disabled="true" placeholder="请选择物料" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="uni-list-cell uni-bg-white" hover-class="uni-list-cell-hover">
				<view class="padding panel-full">
					<view class="text-black uni-ellipsis" v-show="returnInfo.MaterialReturnType == 1">退料类型：工废
					</view>
					<view class="text-black uni-ellipsis " v-show="returnInfo.MaterialReturnType == 2">退料类型：料废</view>
					<view class="text-black uni-ellipsis " v-show="returnInfo.MaterialReturnType == 3">退料类型：合格退料</view>
					<view v-for="(subItem,index) in returnInfo.materials" :key="index">
						<view class="text-black uni-ellipsis ">工单：{{subItem.WorkOrderCode}}</view>
						<view class="text-black uni-ellipsis">规格：{{subItem.Specification}}</view>
						<uni-collapse ref="collapse">
							<uni-collapse-item :title="'物料名称：' + subItem.MaterialName">
								<view v-for="(subSubItem,index) in subItem.detail" :key="index">
									<view style="display: flex; justify-content: space-between;">
										<view class="text-black uni-ellipsis" v-show="subSubItem.workerCode != '' && subSubItem.workerCode != null">
											工号：{{subSubItem.workerCode}}</view>
										<view class="text-black uni-ellipsis" v-show="subSubItem.UserName != '' && subSubItem.UserName != null">
											名字：{{subSubItem.UserName}}
										</view>
										<view class="text-black uni-ellipsis" v-show="subSubItem.qty != '' && subSubItem.qty != null">
											数量：{{subSubItem.qty}}
										</view>
									</view>
									<view class="text-black uni-ellipsis" v-show="subSubItem.question != '' && subSubItem.question != null">
										问题点：{{subSubItem.question}}
									</view>
								</view>
							</uni-collapse-item>
						</uni-collapse>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">全部清空</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submit">确认提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import vTabs from '@/components/v-tabs/v-tabs.vue'
	export default {
		components: {
			uniTag,
			UniIcons,
			vTabs
		},
		data() {
			return {
				// 退料类型
				radio: 0,
				returnType: [{
					text: '工废',
					value: 1
				}, {
					text: '料废',
					value: 2
				}, {
					text: '合格',
					value: 3
				}],
				// 已选工单列表：每次选择工单后，拿来验证是否继续选择物料
				WOS: []
			}
		},
		computed: {
			returnInfo() {
				return this.$store.state.returnMaterial.returnMaterial;
			},
			// 工单信息
			woId() {
				return this.$store.state.wo.wo.woId;
			},
			wo() {
				return this.$store.state.wo.wo.wo;
			},
			woName() {
				return this.$store.state.wo.wo.woName;
			},
			// 产线
			LineCode() {
				return this.$store.state.line.line.lineCode;
			},
			LineName() {
				return this.$store.state.line.line.lineName;
			},
			LineId() {
				return this.$store.state.line.line.lineId;
			},
			// 车间、部门
			DepartmentId() {
				return this.$store.state.department.department.departmentId;
			},
			DepartmentCode() {
				return this.$store.state.department.department.departmentCode;
			},
			DepartmentName() {
				return this.$store.state.department.department.departmentName;
			},
			// 仓库
			storageId() {
				return this.$store.state.storage.storage.storageId;
			},
			storageCode() {
				return this.$store.state.storage.storage.storageCode;
			},
			storageLocation() {
				return this.$store.state.storage.storage.storageName;
			},
		},
		onLoad(e) {
			this.$clearStore(this)
		},
		onShow() {
			// 检查是否需要增加新的退料数据
			if (uni.getStorageSync('returnWOMat') == '') {
				return;
			} else {
				this.$store.dispatch('returnMaterial/promiseSetReturnMaterial', {
					WorkShopId: this.DepartmentId,
					ProdLineId: this.LineId,
					ReturnWarehouseId: this.storageId,
					MaterialReturnType: this.$store.state.radios.radioRerurnType,
					materials: JSON.parse(decodeURIComponent(uni.getStorageSync('returnWOMat')))
				}).then(() => {
					uni.removeStorageSync('returnWOMat')
				})
				this.radio = this.$store.state.radios.radioRerurnType
				console.log(this.returnInfo);
			}
		},
		methods: {
			// 清空数据
			cancel() {
				const that = this;
				uni.showModal({
					title: '提示',
					content: '确定清空已选退料信息吗？',
					success: function(res) {
						if (res.confirm) {
							uni.removeStorageSync('returnWOMat');
							that.clearStore();
							that.WOS = [];
						} else if (res.cancel) {}
					}
				})
			},
			// 清除界面选中值
			clearStore() {
				this.$store.commit('department/empty');
				this.$store.commit('line/empty');
				this.$store.commit('storage/empty');
				this.$store.commit('wo/empty');
				this.$store.commit('returnMaterial/empty');
				this.$store.commit('radios/emptyRadioRerurnType');
			},
			// 监听退料类型
			change(e) {
				const that = this;
				let returnInfo = this.$store.state.returnMaterial.returnMaterial;
				if (typeof returnInfo.materials != 'undefined') {
					uni.showModal({
						title: '提示',
						content: '更换退料类型，将会清空已选物料。确认继续吗？',
						success: function(res) {
							if (res.confirm) {
								that.$store.commit('returnMaterial/empty');
							} else if (res.cancel) {
								that.radio = that.$store.state.radios.radioRerurnType;
							}
						}
					})
				}
			},
			submit() {
				const returnMaterialInfo = this.$store.state.returnMaterial.returnMaterial;
				const that = this;
				if (that.DepartmentId == null) {
					uni.showToast({
						title: '未获取到部门信息',
						icon: 'none'
					})
				} else if (that.LineId == null) {
					uni.showToast({
						title: '未获取到产线信息',
						icon: 'none'
					})
				} else if (returnMaterialInfo.length == 0) {
					uni.showToast({
						title: '请至少选择一个物料',
						icon: 'none'
					})
				} else if (that.storageId == null) {
					uni.showToast({
						title: '请选择退料仓库',
						icon: 'none'
					})
				} else {
					that.request({
						url: '/ProdReturn/WarehouseQuickReturn',
						method: 'POST',
						data: returnMaterialInfo,
						success: res => {
							that.$audio.materialReturn.success();
							that.$error('请求成功！' + res.response)
							uni.removeStorageSync('returnWOMat');
							that.$store.commit('returnMaterial/empty');
							that.WOS = [];
						},
						error: res => {
							that.$audio.materialReturn.error();
							that.$error(res.msg)
						}
					})
				}
			},
			// 验证是否存在重复工单
			isRepeatWO(wo) {
				return new Promise(resolve => {
					const that = this;
					if (that.WOS.includes(wo) && typeof that.returnInfo == 'undefined') {
						uni.showModal({
							title: '提示',
							content: '选择了重复工单，继续将重置这个工单的退料信息',
							success: function(res) {
								if (res.confirm) {
									that.$store.dispatch('returnMaterial/removeElement', {
										WO: wo
									})
									resolve('success')
								} else if (res.cancel) {
									return;
								}
							}
						})
					} else {
						resolve('success')
					}
				})
			},
			// 物料清单选择
			materialDetail() {
				const that = this;
				const wo = this.$store.state.wo.wo.wo;
				if (that.DepartmentId == null) {
					that.$toast('请先选择部门')
				} else if (that.LineId == null) {
					that.$toast('请先选择产线')
				} else if (that.storageId == null) {
					that.$toast('请先选择仓库')
				} else if (this.radio === 0) {
					uni.showToast({
						title: '请先选择退料类型',
						icon: 'none'
					})
				} else if (1 === this.radio) {
					that.isRepeatWO(wo).then(res => {
						that.WOS.push(wo);
						that.$store.commit('radios/radioRerurnType', {
							radio: that.radio
						})
						uni.navigateTo({
							url: './warehouseQuickReturnEdit?id=' + this.$store.state.wo.wo.Id + '&isShow=' + true
						})
					})
				} else {
					that.isRepeatWO(wo).then(res => {
						that.WOS.push(wo);
						that.$store.commit('radios/radioRerurnType', {
							radio: that.radio
						})
						uni.navigateTo({
							url: './warehouseQuickReturnEdit?id=' + this.$store.state.wo.wo.Id + '&isShow=' + false
						})
					})
				}
			},
			// 跳转仓库列表
			querywarehouseList() {
				uni.navigateTo({
					url: '../../common/warehouseList/warehouseList?warehouseCode=' + this.storageCode
				})
			},
			// 查询工单
			queryOrder() {
				uni.navigateTo({
					url: '../../common/orderQuery/orderQuery?LineName=' + this.LineName + '&woStart=' + 0
				})
			},
			// 查询部门
			queryDepartment() {
				uni.navigateTo({
					url: '../../common/department/department'
				})
			},
			// 查询产线
			getLine() {
				uni.navigateTo({
					url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			}
		}
	}
</script>

<style>

</style>
