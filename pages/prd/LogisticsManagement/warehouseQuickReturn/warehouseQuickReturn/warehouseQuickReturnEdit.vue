<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view style="background-color: #FDF5E6; font-size: 13px; color: #de8c17; text-align: center;">
				所选仓库：{{storageLocation}}
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
				<view class="cu-form-group padding panel-full" v-show="item.Qty > 0">
					<view style="width: 90%; " @click="showDialog(index)">
						<view class="text-black uni-ellipsis cu-form-data-10">编码：{{item.MaterialCode}}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">名称：{{item.MaterialName}} </view>
						<view class="text-black uni-ellipsis cu-form-data-10">规格：{{item.Specification}}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">数量：{{item.Qty}}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">默认仓库：{{item.StoreKeeperName}}</view>
						<view class="text-black uni-ellipsis cu-form-data-10">仓库员：{{item.DefaultStorageName}}</view>
					</view>
					<view style="width: 10%; ">
						<checkbox-group @change="checkboxChange($event, index)">
							<label>
								<checkbox :value="String(item.isReturn)" :checked="item.isReturn" />
							</label>
						</checkbox-group>
					</view>
				</view>
			</view>
			<view class="cu-form-group text-right uni-fixed-bottom">
				<uni-tag text="反选" type="warning" @click="checkAll" />
				<uni-tag text="完成" type="success" @click="submitAll" />
			</view>
			<uni-dialog :show="isShow">
				<view class="cu-form-group">
					<view class="title">数量</view>
					<input v-model="qty" type="number" />
				</view>
				<view class="cu-form-group" v-show="is == 'true'">
					<view class="title">工号</view>
					<input v-model="UserCode" placeholder="扫描工号获取" type="text" @confirm="getUserInfo()" />
					<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
				</view>
				<view class="cu-form-group" v-show="is == 'true'">
					<view class="title">责任人</view>
					<input v-model="UserName" placeholder="扫描工号后获取" type="text" disabled="true" />
					<view></view>
				</view>
				<view class="cu-form-group" v-show="is == 'true'">
					<textarea v-model="question" placeholder="输入问题点" />
				</view>
				<view class="cu-form-group">
					<uni-tag text="取消" type="warning" @click="cancel()" />
					<uni-tag text="确认" type="success" @click="submit()" />
				</view>
			</uni-dialog>
		</view>
	</view>
</template>

<script>
	import uniDialog from '@/components/uni-dialog.vue'
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import {
		deepCopyPromise
	} from '@/js/productUtil.js'
	export default {
		components: {
			uniDialog,
			uniTag,
			UniIcons
		},
		data() {
			return {
				index: null,
				// 数量
				qty: 0,
				// 问题点
				question: '',
				// 用户
				UserId: null,
				UserCode: '',
				UserName: '',
				// 是否显示
				is: null,
				isShow: false,
				// 类型
				checkInfo: [],
				// 
				obj: []
			}
		},
		onLoad(e) {
			const id = e.id;
			this.is = e.isShow;
			this.req(id);
		},
		onBackPress(options) {
			uni.navigateTo({
				url: '../../product'
			})
		},
		computed:{
			storageLocation() {
				return this.$store.state.storage.storage.storageName;
			}
		},
		methods: {
			jump() {
				return new Promise(resolve => {
					let temp = [];
					const that = this;
					for (let i = 0; i < that.obj.length; i++) {
						if (that.obj[i].isReturn) {
							if (typeof(that.obj[i].detail) != 'undefined') {
								if (that.obj[i].Qty > 0) {
									temp.push(that.obj[i])
								}
							}
						}
					}
					resolve(temp);
				})
			},
			// 提交全部
			submitAll() {
				this.jump().then(res => {
					if (res.length == 0) {
						uni.navigateBack({
							animationDuration: 300
						})
						return
					} else {
						uni.setStorageSync('returnWOMat', encodeURIComponent(JSON.stringify(res)))
					}

					uni.navigateBack({
						animationDuration: 300
					})
				})
			},
			//全选
			checkAll() {
				const that = this;
				deepCopyPromise(that.obj).then(res => {
					for (let i = 0; i < res.length; i++) {
						that.obj[i].isReturn = !that.obj[i].isReturn;
						if (Number(that.obj[i].Qty) <= 0) {
							continue
						} else {
							if (!res[i].isReturn) {
								that.obj[i].WorkOrderId = this.$store.state.wo.wo.woId;
								that.obj[i].WorkOrderCode = this.$store.state.wo.wo.wo;
								that.obj[i].detail = [{
									qty: that.obj[i].Qty
								}]
							} else {
								that.obj[i].detail = null
							}
						}
					}
				});
			},
			// 单个物料弹窗信息确定
			submit() {
				const that = this;
				if (that.is == 'true') {
					if (this.question === '' && this.UserName === '') {
						uni.showToast({
							title: '请输入工号和问题点',
							icon: 'loading'
						})
						that.isShow = !that.isShow
						return
					}
				}
				that.obj.forEach((item, indexx) => {
					if (!item.isReturn) {
						return
					} else {
						if (item.Qty < that.qty) {
							uni.showToast({
								title: '数量不能大于剩余数量，目前剩余数量：' + item.Qty,
								icon: 'none'
							})
							item.isReturn = false
							return
						}
						item.WorkOrderId = this.$store.state.wo.wo.woId
						item.WorkOrderCode = this.$store.state.wo.wo.wo
						if (item.detail == null) {
							item.detail = [{
								qty: that.qty,
								question: that.question,
								workerCode: that.UserCode,
								UserName: that.UserName
							}]
						} else {
							if (that.index == indexx) {
								item.detail.push({
									qty: that.qty,
									question: that.question,
									workerCode: that.UserCode,
									UserName: that.UserName
								})
							}
						}
					}
				})
				// 弹窗关闭时，清除弹窗数据
				that.clearDialogData()
				that.isShow = !that.isShow
			},
			// 获取登录人员信息
			getUserInfo() {
				this.request({
					url: '/EquipRepairMain/GetSysUser',
					method: 'GET',
					data: {
						acount: this.UserCode
					},
					success: res => {
						if (res.response == null) {
							this.$error('获取人员信息失败', () => {
								this.UserId = ''
								this.UserName = ''
								this.UserCode = ''
							})
							return
						} else {
							this.UserId = res.response.Id
							this.UserCode = res.response.UserAccount
							this.UserName = res.response.UserName
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询备料清单数据请求
			req(id) {
				this.request({
					url: '/ProdReturn/GetProdStockList',
					method: 'POST',
					data: {
						WorkOrderId: id
					},
					success: res => {
						new Promise((resolve, reject) => {
							resolve(res.response)
						}).then(matsS => {
							const mats = this.$store.state.returnMaterial.returnMaterial
							if (Object.keys(mats).length == 0) {
								this.obj = matsS
								return
							} else {
								for (let i = 0; i < mats.materials.length; i++) {
									for (let j = 0; j < matsS.length; j++) {
										if (mats.materials[i].MaterialCode == matsS[j].MaterialCode) {
											matsS[j].Qty = Number(matsS[j].Qty) - Number(mats
												.materials[i].detail[0].qty)
											this.obj = matsS
										}
									}
								}
							}
						})
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			clearDialogData() {
				// 数量
				this.qty = 0
				this.index = null
				// 问题点
				this.question = '';
				// 用户
				this.UserId = null;
				this.UserName = '';
				this.UserCode = '';
			},
			cancel() {
				this.isShow = !this.isShow;
				this.obj[this.index].isReturn = false;
			},
			// 显示弹窗
			showDialog(index) {
				if (!this.obj[index].isReturn) {
					uni.showToast({
						title: '选中的物料才可添加问题点',
						icon: 'none'
					})
					return;
				}
				this.index = index
				this.qty = this.obj[index].Qty;
				this.isShow = !this.isShow;
			},
			// checkbox 发生变化
			checkboxChange: function(e, index) {
				const that = this;
				let values = e.detail.value;
				let res = Boolean(values[0]);
				if (res) {
					// that.obj[index].WorkOrderId = this.$store.state.wo.wo.woId;
					// that.obj[index].WorkOrderCode = this.$store.state.wo.wo.wo;
					that.obj[index].isReturn = true;
					that.showDialog(index)
				} else {
					that.obj[index].isReturn = false;
				}
			}
		}
	}
</script>
