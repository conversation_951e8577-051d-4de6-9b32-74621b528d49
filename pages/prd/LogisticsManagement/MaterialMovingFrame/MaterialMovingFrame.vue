<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-zhuanchu xj_form_icon"/>
				<view class="title">转出载具码</view>
				<input v-model="outCarrierNo" @confirm="queryOutCarrierInfo()" placeholder="请手动扫描载具码"
					:focus="focusOne" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-zhuanru xj_form_icon"/>
				<view class="title">转入载具码</view>
				<input v-model="inCarrierNo" @confirm="queryInCarrierInfo()" placeholder="请手动扫描载具码" :focus="focusTwo" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-icon_caigoushuliang xj_form_icon"/>
				<view class="title"><text style="color: red">*</text>实际数量</view>
				<input type="number" v-model="actualQty" placeholder="请手动输入移框数量" :focus="focusThree" />
			</view>
			<view class="action margin-top" v-show="msg != ''">
				<text class="cuIcon-title text-xj"></text> 消息提示：
				<text style="color:#E3162E">{{msg}}</text>
			</view>
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">
					<text class="cuIcon-title text-xj "></text> 转出载具码{{outCarrierNo}}
				</view>
			</view>
			<view class="cu-form-group">
				<view v-show="outFlag">
					<view class="uni-ellipsis">料号：{{outCarrierMatinfo.matNo}}</view>
					<view class="uni-ellipsis">规格：{{outCarrierMatinfo.specifications}}</view>
					<view class="uni-ellipsis">名称：{{outCarrierMatinfo.matName}}</view>
					<view class="uni-ellipsis">来源：{{outCarrierMatinfo.source}}</view>
					<view class="uni-ellipsis">数量：{{outCarrierMatinfo.qty}}</view>
					<view class="uni-ellipsis">仓库：{{outCarrierMatinfo.storageName}}</view>
					
				</view>
				<view v-show="!outFlag" class="text-red">请先扫码</view>
			</view>
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-xj"></text> 转入载具码：{{inCarrierNo}}
				</view>
			</view>
			<view class="cu-form-group">
				<view v-show="inFlag">
					<view class="uni-ellipsis">料号：{{inCarrierMatinfo.matNo}}</view>
					<view class="uni-ellipsis">规格：{{inCarrierMatinfo.specifications}}</view>
					<view class="uni-ellipsis">名称：{{inCarrierMatinfo.matName}}</view>
					<view class="uni-ellipsis">来源：{{inCarrierMatinfo.source}}</view>
					<view class="uni-ellipsis">数量：{{inCarrierMatinfo.qty}}</view>
					<view class="uni-ellipsis">仓库：{{inCarrierMatinfo.storageName}}</view>
				</view>
				<view v-show="!inFlag" class="text-red">请先扫码</view>
			</view>
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">取消</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submitActualQty">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue'
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				// 焦点控制
				focusOne: true,
				focusTwo: false,
				focusThree: false,
				outCarrierNo: '',
				inCarrierNo: '',
				actualQty: '',
				outCarrierMatinfo: {
					matNo: '',
					specifications: '',
					matName: '',
					source: '',
					qty: '',
					storageName:''
				},
				inCarrierMatinfo: {
					matNo: '',
					specifications: '',
					matName: '',
					source: '',
					qty: '',
					storageName:''
				},
				outFlag: false,
				inFlag: false,
				msg: ''
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		methods: {
			// 焦点处理
			changeBlur() {
				let that = this
				if (this.outCarrierNo == '') {
					that.focusOne = true
				} else if (this.inCarrierNo == '') {
					that.focusTwo = true
				} else if (this.actualQty == '') {
					that.focusThree = true
				}
			},
			// 查询转出载具信息
			queryOutCarrierInfo() {
				if (this.outCarrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.outCarrierNo,
					},
					success: res => {
						this.outCarrierMatinfo.matNo = res.response.MaterialCode
						this.outCarrierMatinfo.specifications = res.response.Specification
						this.outCarrierMatinfo.matName = res.response.MaterialName
						this.outCarrierMatinfo.source = res.response.Source
						this.outCarrierMatinfo.qty = res.response.Qty
						this.outCarrierMatinfo.storageName = res.response.StorageName
						
						this.outFlag = true
						this.changeBlur()
					},
					error: res => {
						this.$error(res.msg)
						this.msg = res.msg
						this.outCarrierNo = ''
					}
				})
			},
			// 查询转入载具信息
			queryInCarrierInfo() {
				if (this.inCarrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.inCarrierNo,
					},
					success: res => {
						this.inCarrierMatinfo.matNo = res.response.MaterialCode
						this.inCarrierMatinfo.specifications = res.response.Specification
						this.inCarrierMatinfo.matName = res.response.MaterialName
						this.inCarrierMatinfo.source = res.response.Source
						this.inCarrierMatinfo.qty = res.response.Qty
						this.inCarrierMatinfo.storageName = res.response.StorageName
						
						this.inFlag = true
						this.changeBlur()
					},
					error: res => {
						this.$error(res.msg)
						this.msg = res.msg
						this.inCarrierNo = ''
					}
				})
			},
			// 提交真实数量
			submitActualQty() {
				let that = this
				if (this.actualQty == '') {
					uni.showToast({
						title: '请输入需要转移的实际数量！',
						icon: 'error'
					})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"]
					plus.nativeUI.confirm('确认要转移' + that.outCarrierNo + '载具物料到' + that.inCarrierNo + '载具吗？', function(e) {
						console.log("Close confirm: " + e.index)
						//0==确认，否则取消  
						if (e.index == 0) {
							that.mf()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					})
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要转移' + that.outCarrierNo + '载具物料到' + that.inCarrierNo + '载具吗？',
						success: function(res) {
							if (res.confirm) {
								console.log('用户点击确定')
								that.mf()
							} else if (res.cancel) {
								console.log('用户点击取消')
								// 执行逻辑
							}
						}
					})
					// #endif
				}
			},
			mf() {
				this.request({
					url: '/Carrier/CarrierTransferRecordInfo',
					method: 'POST',
					data: {
						OriginalCarrierId: this.outCarrierNo,
						TargetCarrierId: this.inCarrierNo,
						Qty: this.actualQty,
						MaterialCode:this.outCarrierMatinfo.matNo,
						MaterialName:this.inCarrierMatinfo.matName,
						Specification:this.inCarrierMatinfo.specifications
					},
					success: res => {
						this.$audio.MaterialMovingFrame.success()
						this.changeBlur()
						this.$success(res.msg)
						this.msg = '成功！'
						this.cancel()
					},
					error: res => {
						this.$audio.MaterialMovingFrame.error()
						this.$error(res.msg, () => this.changeBlur())
						this.cancel()
						this.msg = res.msg
					}
				})
				// this.queryOutCarrierInfo()
				// this.queryInCarrierInfo()
			},
			// 焦点处理
			changeBlur() {
				let app = this
				if (app.outCarrierNo == '') {
					app.focusOne = true
				} else if (app.inCarrierNo == '') {
					app.focusTwo = true
				} else if (app.actualQty == '') {
					app.focusThree = true
				}
			},
			// 取消，初始化页面
			cancel() {
				this.outCarrierNo = ''
				this.inCarrierNo = ''
				this.actualQty = ''

				this.outFlag = false
				this.inFlag = false

				this.msg = ''

				this.focusOne = true
				this.focusTwo = false
				this.focusThree = false
			}
		}
	}
</script>

<style>

</style>
