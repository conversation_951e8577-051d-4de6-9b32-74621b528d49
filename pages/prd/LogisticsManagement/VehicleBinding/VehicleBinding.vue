<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon"/>
				<view class="title">载具码</view>
				<input v-model="carrierNo" placeholder="请手动扫描载具码" :focus="focusOne" @confirm="isExistCarrier" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-lianjie xj_form_icon"/>
				<view class="title">绑定类型</view>
				<picker @change="bindPickerChange" :value="index" :range="bindType">
					<view class="#">{{bindType[index]}}</view>
				</picker>
			</view>
			<view class="cu-form-group" @click="queryMaterial()">
				<view class="iconfont xj-bianmubianma xj_form_icon"/>
				<view class="title">物料编码</view>
				<input v-model="matNo" disabled="true" placeholder="请手动选择物料" :focus="focusTwo" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-mingcheng xj_form_icon"/>
				<view class="title">物料名称</view>
				<input v-model="matDesc" disabled="true" placeholder="选择物料后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-guigeguanli xj_form_icon"/>
				<view class="title">物料规格</view>
				<input v-model="materialSpecification" disabled="true" placeholder="选择物料后自动加载" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-icon_caigoushuliang xj_form_icon"/>
				<view class="title">数量</view>
				<input v-model="qty" placeholder="请手动输入绑定数量" type="number" min=1 step=1 :focus="focusThree" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-danyilaiyuan xj_form_icon"/>
				<view class="title">来源</view>
				<input v-model="source" placeholder="请手动输入来源" :focus="focusFour" />
			</view>
			<view class="cu-form-group" @click="querywarehouseList">
				<view class="iconfont xj-cangku xj_form_icon"/>
				<view class="title">仓库</view>
				<input v-model="storageLocation" disabled="true" placeholder="请手动选择仓库" :focus="focusFive" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-kuwei xj_form_icon"/>
				<view class="title">库位</view>
				<input v-model="LocationName" placeholder="请手动扫描库位码" :focus="focusSix" @confirm="queryLocationByCode" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">全部清空</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submitActualQty">载具绑定</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue'
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				// 焦点控制
				focusOne: true,
				focusTwo: false,
				focusThree: false,
				focusFour: false,
				focusFive: false,
				focusSix: false,
				// 载具码
				carrierNo: '',
				bindType: ['期初绑定', '无条码绑定'],
				index: 0,
				// 仓库
				storageList: [{
					StorageId: 0,
					LocationName: '请先选择仓库'
				}],
				indexStorage: null,
				// 库位
				LocationId: null,
				// StorageCode: '',
				// StorageName: '',
				LocationCode: '',
				LocationName: '',
				// 物料
				matId: null,
				matNo: '',
				matDesc: '',
				materialSpecification: '',
				qty: '',
				storageId: null,
				warehouse: '',
				storageLocation: '',
				source: '',
				msg: '',
				/* 扩展组件 下拉框 */
				range: [],
				value: ''
			}
		},
		onLoad() {
			this.clearCath()
		},
		onShow() {
			uni.getStorage({
				key: 'materialInfo',
				success: o => {
					this.matId = o.data.id
					this.matNo = o.data.matNo
					this.matDesc = o.data.matDesc
					this.materialSpecification = o.data.specifications
				}
			})
			uni.getStorage({
				key: 'storageInfo',
				success: o => {
					this.storageId = o.data.id
					this.warehouse = o.data.storageCode
					this.storageLocation = o.data.storageName
					let that = this
					if (this.warehouse != '') {
						that.queryStorageList()
					}
				}
			})
			this.changeBlur()
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		methods: {
			clearCath() {
				uni.removeStorage({
					key: 'storageInfo'
				})
				uni.removeStorage({
					key: 'materialInfo'
				})
			},
			// 跳转物料列表
			queryMaterial() {
				uni.navigateTo({
					url: '../../common/materialList/materialList?matNo=' + this.matNo
				})
			},
			// 跳转仓库列表
			querywarehouseList() {
				uni.navigateTo({
					url: '../../common/warehouseList/warehouseList?warehouseCode=' + this.warehouse
				})
			},
			// 判断载具是否存在
			isExistCarrier() {
				if (this.carrierNo == '') {
					this.$toast('未获取到载具码')
					return
				}
				this.request({
					url: '/Carrier/GetCarrier',
					method: 'GET',
					data: {
						carrierCode: this.carrierNo
					},
					success: res => {
						if (res.response === null) {
							uni.showToast({
								title: '该载具不存在',
								icon: 'error'
							})
							this.msg = '该载具不存在'
							this.carrierNo = ''
							this.focusOne = true
						} else {
							this.changeBlur()
						}
					}
				})
			},
			// 查询库位码
			queryLocationByCode() {
				if (this.LocationName == '') {
					this.$toast('请先扫描库位码')
					return
				}
				this.request({
					url: '/Location/Get',
					method: 'GET',
					data: {
						intPageSize: 999,
						page: 1,
						key: this.LocationName
					},
					success: res => {
						if (res.response.data.length === 0) {
							uni.showToast({
								title: '未查找到该库位',
								icon: 'error'
							})
							this.msg = '未查找到该库位'
							return
						}
						let temp = res.response.data[0]
						this.LocationId = temp.Id
						this.LocationCode = temp.LocationCode
						this.LocationName = temp.LocationName
					},
					error: res => {
						this.$error(res.msg)
						this.msg = res.msg
					}
				})
			},
			// 查询库位信息
			queryStorageList() {
				let that = this
				if (this.warehouse == '') {
					uni.showToast({
						title: '请先选择库位',
						icon: 'loading'
					})
				} else {
					that.request({
						url: '/Location/GetByStorage',
						method: 'GET',
						data: {
							StorageId: that.storageId
						},
						success: res => {
							this.range = []
							for (let i = 0; i < res.response.length; i++) {
								let temp = {
									text: res.response[i].LocationName,
									value: res.response[i].StorageId,
								}
								this.range.push(temp)
							}
						}
					})
				}
			},
			// 提交真实数量
			submitActualQty() {
				const that = this
				if (this.carrierNo == '') {
					uni.showToast({
						title: '请输入载具码！',
						icon: 'loading'
					})
				} else if (this.qty == '') {
					uni.showToast({
						title: '请输入需要更改的实际数量！',
						icon: 'loading'
					})
				} else if (this.matNo == '') {
					uni.showToast({
						title: '请输入需要需要绑定的物料编码！',
						icon: 'loading'
					})
				} else if (this.storageId == '') {
					uni.showToast({
						title: '请重新选择仓库！',
						icon: 'loading'
					})
				} else if (this.LocationCode == '') {
					uni.showToast({
						title: '请选择库位！',
						icon: 'loading'
					})
					// } else if (this.storageLocation == '') {
					// 	uni.showToast({
					// 		title: '请选择库位！',
					// 		icon: 'loading'
					// 	})
					// } else if (this.storageList[0] == '未获取到该仓库的仓位' || this.storageList[0] == '请先选择仓库') {
					// 	uni.showToast({
					// 		title: '仓库库位不存在无法绑定！',
					// 		icon: 'loading'
					// 	})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"]
					plus.nativeUI.confirm('确认要提交吗？', function(e) {
						console.log("Close confirm: " + e.index)
						//0==确认，否则取消  
						if (e.index == 0) {
							that.req()
						}
					}, {
						"title": '提示',
						"buttons": btns,
					})
					// #endif

					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要进行绑定吗？？',
						success: function(res) {
							if (res.confirm) {
								that.req()
							} else if (res.cancel) {
								// console.log('用户点击取消')
							}
						}
					})
					// #endif
				}
			},
			// 请求
			req() {
				this.request({
					url: '/Carrier/BindCarrier',
					method: 'PUT',
					data: {
						Source: this.source,
						CarrierCode: this.carrierNo,
						MaterialId: this.matId,
						MaterialCode:this.matNo,
						MaterialName:this.matDesc,
						Specification:this.materialSpecification,												
						Qty: this.qty,
						StorageId: this.storageId,
						LocationId: this.LocationId, //this.storageList[this.indexStorage],
						BindType: this.bindType[this.index] === '期初绑定' ? 1 : 0
					},
					success: res => {
						this.$audio.VehicleBinding.success()
						this.$error(res.msg)
						this.cancel()
					},
					error: res => {
						this.$audio.VehicleBinding.error()
						this.$error(res.msg)
						this.msg = res.msg
						// that.cancel()
					}
				})
			},
			// 焦点处理
			changeBlur() {
				let that = this
				if (this.carrierNo == '') {
					that.focusOne = true
				} else if (this.matNo == '') {
					that.focusTwo = true
				} else if (this.qty == '') {
					that.focusThree = true
				} else if (this.source == '') {
					that.focusFour = true
				} else if (this.storageLocation == '') {
					that.focusFive = true
				}
			},
			// 取消，初始化页面
			cancel() {
				this.carrierNo = ''
				this.index = 0
				this.matId = null
				this.matNo = ''
				this.matDesc = ''
				this.materialSpecification = ''
				this.qty = ''
				this.warehouse = ''
				this.storageLocation = ''
				this.indexStorage = null
				/* picker */
				this.storageList = [{
					StorageId: 0,
					LocationName: '请先选择仓库'
				}]
				this.storageId = null
				this.value = ''
				this.range = []
				this.source = ''
				this.clearCach()
				this.changeBlur()

				this.LocationId = null
				this.LocationCode = ''
				this.LocationName = ''
			},
			// 清理缓存
			clearCach() {
				uni.removeStorage({
					key: 'materialInfo',
					success() {

					}
				})
				uni.removeStorage({
					key: 'storageInfo',
					success() {

					}
				})
			},
			/* picker */
			bindPickerChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			},
			/* picker */
			bindPickerChangeStorage: function(e) {
				let id = e.detail.value
				this.indexStorage = id
				this.storageId = this.storageList[id].StorageId
			},
			/* select */
			change() {
				// TDOO
			}
		}
	}
</script>

<style>
</style>
