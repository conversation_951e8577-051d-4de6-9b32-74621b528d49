<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryDepartment()">
				<view class="iconfont xj-bumen xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="getLine()">
				<view class="iconfont xj-chanxian xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title"><text style="color: #E3162E;">*</text>工单号</view>
				<input v-model="wo" disabled="true" placeholder="请选择工单号" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="uni-list-cell uni-bg-white" hover-class="uni-list-cell-hover">
				<view class="padding panel-full">
					<view v-for="(subItem,index) in returnInfo.materials" :key="index">
						<view class="text-black uni-ellipsis ">工单：{{subItem.WorkOrderCode}}</view>
						<view class="text-black uni-ellipsis">规格：{{subItem.Specification}}</view>
					</view>
				</view>
			</view>
			<view style="height: 200upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">全部清空</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submit">一键上料</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import vTabs from '@/components/v-tabs/v-tabs.vue'
	export default {
		components: {
			uniTag,
			UniIcons,
			vTabs
		},
		data() {
			return {
				// 已选工单列表：每次选择工单后，拿来验证是否继续选择物料
				WOS: [],
			}
		},
		computed: {
			returnInfo() {
				return this.$store.state.returnMaterial.returnMaterial;
			},
			// 工单信息
			woId() {
				return this.$store.state.wo.wo.woId;
			},
			wo() {
				return this.$store.state.wo.wo.wo;
			},
			woName() {
				return this.$store.state.wo.wo.woName;
			},
			// 产线
			LineCode() {
				return this.$store.state.line.line.lineCode;
			},
			LineName() {
				return this.$store.state.line.line.lineName;
			},
			LineId() {
				return this.$store.state.line.line.lineId;
			},
			// 车间、部门
			DepartmentId() {
				return this.$store.state.department.department.departmentId;
			},
			DepartmentCode() {
				return this.$store.state.department.department.departmentCode;
			},
			DepartmentName() {
				return this.$store.state.department.department.departmentName;
			},
		},
		methods: {
			// 清空数据
			cancel() {
				const that = this;
				uni.showModal({
					title: '提示',
					content: '确定清空已选一键上料信息吗？',
					success: function(res) {
						if (res.confirm) {
							uni.removeStorageSync('returnWOMat');
							that.clearStore();
							that.WOS = [];
						} else if (res.cancel) {}
					}
				})
			},
			// 清除界面选中值
			clearStore() {
				this.$store.commit('department/empty');
				this.$store.commit('line/empty');
				this.$store.commit('storage/empty');
				this.$store.commit('wo/empty');
				this.$store.commit('returnMaterial/empty');
				this.$store.commit('radios/emptyRadioRerurnType');
			},
			submit() {
				const returnMaterialInfo = this.$store.state.returnMaterial.returnMaterial;
				const that = this;
				const request = {
					DepartmentId: that.DepartmentId,
					Wo:that.wo,
					LineId:that.LineId,
				}
					that.request({
						url: '/UPMaterial/OneClickFeeding',
						method: 'POST',
						data: request,
						success: res => {
							that.$error('请求成功！')
							that.WOS = [];
						},
						error: res => {
							that.$error(res.msg)
						}
					})
			},
			// 查询工单
			queryOrder() {
				uni.navigateTo({
					url: '../../common/orderQuery/orderQuery1?LineName=' + this.LineName + '&woStart=' + 0
				})
			},
			// 查询部门
			queryDepartment() {
				uni.navigateTo({
					url: '../../common/department/getDepartment'
				})
			},
			// 查询产线
			getLine() {
				uni.navigateTo({
					url: '../../common/productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			}
		}
	}
</script>

<style>

</style>