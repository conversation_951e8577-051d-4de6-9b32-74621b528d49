<template>
	<view>
		<!-- <uni-notice-bar show-icon="true" scrollable="true" single="true" v-show="OrgName" :text="OrgName" speed=50></uni-notice-bar> -->
		<view style="background-color: #c7e4e4; font-size: 13px; color: #009598; text-align: center">当前组织：{{ OrgName }}</view>
		<view v-for="(item, index) in candidates" :key="index">
			<view style="background-color: #ffffff; font-size: 13px">
				<strong>
					<text class="cuIcon-titles text-xj"></text>
					{{ item.label }}
				</strong>
			</view>
			<uni-grid :options="item.children" @gridClick="gridClick" column-num="4"></uni-grid>
		</view>
	</view>
</template>

<script>
import uniSwiperDot from '@/components/uni-ui/uni-swiper-dot/uni-swiper-dot.vue';
import uniNoticeBar from '@/components/uni-ui/uni-notice-bar/uni-notice-bar.vue';
import uniGrid from '@/components/uni-ui/uni-grid/uni-grid.vue';
import cmdNavBar from '@/components/uni-ui/cmd-nav-bar/cmd-nav-bar.vue';

export default {
	components: {
		uniSwiperDot,
		uniNoticeBar,
		uniGrid,
		cmdNavBar
	},
	data() {
		return {
			candidates: [],
			options: [],
			OrgName: ''
		};
	},
	onLoad() {
		this.initPage();
	},
	onShow() {
		//web版注释  app版放开 自动更新
		//this.getLatest()
	},
	methods: {
		// 取得最新版本及其所有信息
		getLatest() {
			uni.showLoading({
				title: '检查 PDA 版本...',
				mask: true
			});
			this.request({
				url: '/Pda/GetSysParameter',
				method: 'GET',
				success: (res) => {
					const latest = res.response.ParameterValue;
					if (this.$current.id < latest) {
						uni.navigateTo({
							url: '../update/index'
						});
					}
					uni.hideLoading();
				}
			});
		},
		async initPage() {
			this.OrgName = await this.getOrgData();
			// console.log('orgName：' + this.OrgName);
			this.getPDACurrentUserMenu();
		},
		getOrgData() {
			let that = this;
			let orgId = uni.getStorageSync('orgId');
			return new Promise((resolve, reject) => {
				this.request({
					url: '/Org/GetOrgAllByTree',
					method: 'GET',
					success: (res) => {
						for (let i = 0; i < res.response.data.length; i++) {
							if (orgId === res.response.data[i].id) {
								resolve(res.response.data[i].label);
							}
						}
					},
					error: (res) => {
						this.msg = res.msg;
						this.cancel();
						reject(res.msg);
					}
				});
			});
		},
		// 获取符合操作人员权限的全部站点
		getPDACurrentUserMenu() {
			this.candidates = [];
			this.request({
				url: '/SysMenu/GetPDACurrentUserMenu',
				method: 'GET',
				success: (res) => {
					this.candidates = res.response.data;
					console.log(this.candidates);
				},
				error: (res) => {}
			});
		},
		// 选择站点，触发事件
		choiceStation(e) {
			uni.setStorageSync('currentStation', e);
			this.initPages();
		},
		// 初始化页面菜单
		initPages() {
			/* var that = this
				this.request({
					url: "/common/getStationPages",
					success: res => {
						console.log(res)
						that.options = that.sortBykey(res.data, "seq")
					},
					error: res => {
						that.options = []
					}
				}) */
			this.options = [
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '载具绑定',
					url: '/pages/prd/LogisticsManagement/VehicleBinding/VehicleBinding',
					seq: 1
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '载具物料调整',
					url: '/pages/prd/LogisticsManagement/AdjustmentofCarrierMaterial/AdjustmentofCarrierMaterial',
					seq: 2
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '物料移框',
					url: '/pages/prd/LogisticsManagement/MaterialMovingFrame/MaterialMovingFrame',
					seq: 3
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '生产发料',
					url: '/pages/prd/LogisticsManagement/ProductionAndDelivery/ProductionAndDelivery',
					seq: 4
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '生产退料',
					url: '/pages/prd/LogisticsManagement/materialReturn/materialReturn',
					seq: 5
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '半成品杂发',
					url: '/pages/prd/LogisticsManagement/Semi-FinishMiscellaneousSend/Semi-FinishMiscellaneousSend',
					seq: 6
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '半成品杂收',
					url: '/pages/prd/LogisticsManagement/Semi-FinishMiscellaneousCollection/Semi-FinishMiscellaneousCollection',
					seq: 15
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '半成品入库',
					url: '/pages/prd/LogisticsManagement/Semi-finishedProductsStorage/Semi-finishedProductsStorage',
					seq: 7
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '物料转移',
					url: '/pages/prd/LogisticsManagement/MaterialTransfer/MaterialTransfer',
					seq: 8
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '半成品调拨',
					url: '/pages/prd/LogisticsManagement/AllocationOfSemi-finishedProducts/AllocationOfSemi-finishedProducts',
					seq: 9
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '设备报修',
					url: '/pages/eam/equipmentRepair/equipmentRepair',
					seq: 10
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '单件报工',
					url: '/pages/prd/ProductionManagement/oneNewspaperWorker/oneNewspaperWorker',
					seq: 11
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '批次报工',
					url: '/pages/prd/ProductionManagement/batchNewspaperWorker/batchNewspaperWorker',
					seq: 12
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '设备保养',
					url: '/pages/eam/EquipmentMaintenance/EquipmentMaintenance',
					seq: 13
				},
				{
					productLine: '#',
					image: '/static/images/charts/column-stack.png',
					stationWorkcenter: null,
					stationWorkPlace: null,
					text: '设备点检',
					url: '/pages/eam/EquipmentSpotInspection/EquipmentSpotInspection',
					seq: 14
				}
			];
		},

		sortBykey(array, key) {
			return array.sort(function (a, b) {
				var x = a[key]; //如果要从大到小,把x,y互换就好
				var y = b[key];
				return x < y ? -1 : x > y ? 1 : 0;
			});
		},
		gridClick(e) {
			// console.log(e.url)
			// uni.setStorageSync("functionMsg", e)
			// uni.navigateTo({
			// 	url: e.url
			// })
		}
	}
};
</script>

<style>
.swiper-box {
	width: 750upx;
	height: 400upx;
}

.swiper-item {
	width: 750upx;
	height: 400upx;
}
</style>
