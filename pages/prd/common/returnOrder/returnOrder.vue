<template>
	<view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style>

</style>
<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue"></uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
						<view class="cu-form-group solid-bottom panel-full">

							<view class="content" style="width:85%">
								<!-- <view class="text-black ds uni-ellipsis">test-id：{{ item.Id }}</view> -->
								<view class="text-black ds uni-ellipsis">退料单号：{{ item.ProdReturnOrder }}</view>
								<view class="text-black uni-ellipsis">物料名称：{{ item.MaterialName }}(数量：{{ item.Qty }})</view>
								<view class="text-black uni-ellipsis">物料规格：{{ item.Specification }}</view>
								<view class="text-black uni-ellipsis">退料仓库：{{ item.ReturnWarehouseName }}</view>
								<view class="text-black uni-ellipsis">退料时间：{{ item.CreateTime }}</view>
								<view class="text-black uni-ellipsis">退料人：{{ item.CreateUserName }}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Id === Id" :disabled="returnOrder.findIndex((t) => t.Id == item.Id) != -1" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none';
export default {
	components: { dsNone },
	onLoad(e) {
		this.departmentId = e.departmentId;
		this.warehouseCode = e.warehouseCode;
		this.query();
	},

	data() {
		return {
			departmentId: null,
			warehouseCode: null,
			// lineId: null,
			searchValue: '',
			Id: '',
			warehouseCode: null,
			obj: []
		};
	},
	computed: {
		// 退料单号列表
		returnOrder() {
			return this.$store.state.returnOrder.returnOrder;
		},
		LineId() {
			return this.$store.state.line.line.lineId;
		}
	},
	methods: {
		query() {
			this.request({
				url: '/ProdReturn/GetApprovalProdReturnOrder',
				method: 'GET',
				data: {
					search: this.searchValue,
					org: typeof this.departmentId == 'undefined' ? '' : this.departmentId,
					line: typeof this.LineId == 'undefined' ? '' : this.LineId,
					ware: typeof this.warehouseCode == 'undefined' ? '' : this.warehouseCode
				},
				success: (res) => {
					this.obj = res.response;
				},
				error: (res) => {
					uni.showModal({
						title: '提示',
						content: res.msg,
						confirmColor: '#ee6666', //确定字体颜色
						showCancel: false, //没有取消按钮的弹框
						buttonText: '确定'
					});
				}
			});
		},
		radioChange(evt) {
			var i = parseInt(evt.detail.value);
			var id = this.obj[i].Id;
			var prodReturnOrder = this.obj[i].ProdReturnOrder;
			var returnWarehouseName = this.obj[i].ReturnWarehouseName;
			var createTime = this.obj[i].CreateTime;
			var createUserName = this.obj[i].CreateUserName;
			var materialName = this.obj[i].MaterialName;
			var specification = this.obj[i].Specification;
			var problemPoint = this.obj[i].ProblemPoint;
			var returnWarehouseId = this.obj[i].ReturnWarehouseId;
			var qty = this.obj[i].Qty;
			// uni.setStorage({
			// 	key: 'returnOrder',
			// 	data: {
			// 		orderId: orderId
			// 	}
			// });
			// vuex
			var isAdd = this.$store.commit('returnOrder/pushReturnOrder', {
				Id: id,
				ProdReturnOrder: prodReturnOrder,
				ReturnWarehouseName: returnWarehouseName,
				CreateTime: createTime,
				CreateUserName: createUserName,
				MaterialName: materialName,
				Specification: specification,
				ProblemPoint: problemPoint,
				ReturnWarehouseId: returnWarehouseId,
				Qty: qty
			});
			uni.navigateBack({
				animationDuration: 1000
			});
		}
	}
};
</script>
