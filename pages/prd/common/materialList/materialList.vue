<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue">
			</uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-5">物料编码: {{item.MaterialCode}}</view>
								<view class="cu-form-data-5">物料名称: {{item.MaterialName}}</view>
								<view class="cu-form-data-5">物料规格: {{item.Specification}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.MaterialCode === matNo" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		onLoad(e) {
			// this.matNo = e.matNo;
			this.query();
		},
		data() {
			return {
				searchValue: '',
				matNo: '',
				matDesc: '',
				obj: []
			}
		},
		methods: {
			query() {
				this.request({
					url: '/Material/Get',
					method: 'GET',
					data: {
						intPageSize: 20,
						page: 1,
						materCode: this.searchValue
					},
					success: res => {
						this.obj = res.response.data;
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var id = this.obj[i].Id;
				var matNo = this.obj[i].MaterialCode;
				var matDesc = this.obj[i].MaterialName;
				var specifications = this.obj[i].Specification;

				// 1 以本地缓存的方式存储
				uni.setStorage({
					key: "materialInfo",
					data: {
						"matDesc": matDesc,
						"matNo": matNo,
						"specifications": specifications,
						"id": id
					}
				})
				// 2 VUEX的形式存储
				this.$store.commit('material/setMaterial', {
					materialId: id,
					materialCode: matNo,
					materialName: matDesc,
					specification: specifications
				})
				
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
