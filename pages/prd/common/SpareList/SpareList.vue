
<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar  class="bg-white dashed-top" :focus="true" v-model="searchValue"
				@confirm="query" />
			<!-- 多选模式 -->
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
				<view class="cu-form-group padding panel-full">
					<view style="width: 90%;">
						<view class="uni-ellipsis cu-form-data-10">备品编码: {{item.ChoiceCode}}</view>
						<view class="uni-ellipsis cu-form-data-10">备品名称: {{item.ChoiceName}}</view>
					</view>
					<view style="width: 10%;">
						<checkbox-group @change="checkboxChange($event, index)">
							<label>
								<checkbox :value="String(item.isChecked)" :checked="item.isChecked" />
							</label>
						</checkbox-group>
					</view>
				</view>
			</view>
			<view class="uni-fixed-bottom">
				<uni-tag text="备品选择完成" style="width: 100%;" type="primary" @click="submit" />
			</view>
			
		</view>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue'
	export default {
		components: {
			dsNone,
			uniTag
		},
		data() {
			return {
				radio: null,
				keys: [],
				currentOrderNo: '',
				obj: [],
				// 搜索条件
				searchValue: ''
			}
		},
		onLoad(e) {
			// this.$clearStore(this)
			this.obj = []
		},
		onShow() {
			this.query()
			
		},
		watch: {
			wo() {
				this.query()
			}
		},
		computed: {
			
		
		},
		methods: {
			change(e) {
				this.searchValue = e.detail.value
				this.query()
			},
			// 清除 store
			clearStore() {

			},
			
			// 查询工单备料详情
			query() {
				this.request({
					url: '/EquipChoice/GetEquipChoice',
					method: 'GET',
					data: {
						key: this.searchValue,
					},
					success: res => {
						if (res.response.length == 0) {
							this.obj = []
							this.$toast('未查询到数据')
							return
						}
						this.obj = res.response;
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			/* 
				@Desc：多选模式 
				@Author：DingShuai 
				@UpdateDate：20220905 
			*/
			/* 整理多选数据并返回上一级界面 */
			submit(){
				const res = this.getCheckedDate()
				this.$store.commit('spareChecks/setSpareChecks', res)
				uni.navigateBack({
					animationDuration: 1000
				})
			},
			/* 获取到所有已选中物料 */
			getCheckedDate(){
				const temp = this.obj
				let arr = []
				for(let i = 0; i < temp.length; i++){
					if(temp[i].isChecked){
						arr.push(temp[i])
					}
				}
				return arr
			},
			checkboxChange: function(e, index) {
				const values = e.detail.value
				const res = Boolean(values[0])
				this.obj[index].isChecked = res
			},
			/* ------------------------------------------------------------------------------------ */
			/* 单选模式 */
		}
	}
</script>


