<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">工单号: {{item.Wo}}</view>
								<view class="cu-form-data-15">工单描述: {{item.WOName}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Wo === currentOrderNo" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		onLoad(e) {
			this.request({
				url: '/PieceworkMain/GetPieceworkMain',
				method: 'GET',
				data: {
					inPageSize: 20,
					inPageIndex: 1
				},
				success: res => {
					// console.log(res);
					this.obj = res.response.data;
				},
					error: res =>{
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
			})
		},
		data() {
			return {
				currentOrderNo: '',
				obj: []
			}
		},
		methods: {
			radioChange(evt) {
				console.log("123"+evt);
				var i = parseInt(evt.detail.value);
				var orderNo = this.obj[i].Wo;
				var orderName = this.obj[i].WOName;
				var id = this.obj[i].Id;
				uni.setStorage({
					key: "pieceworkOrderInfo",
					data: {
						"orderNo": orderNo,
						"orderName": orderName,
						"Id": id
					}
				})
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
