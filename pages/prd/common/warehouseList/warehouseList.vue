<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue"></uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-10">仓库编码: {{ item.StorageCode }}</view>
								<view class="cu-form-data-10">仓库名称: {{ item.StorageName }}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.StorageCode === storageCode" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none';
export default {
	components: { dsNone },
	onLoad(e) {
		this.warehouseCode = e.warehouseCode;
		// /api/Storage/GetByCode根据仓库code获取仓库列表
		this.query();
	},
	data() {
		return {
			searchValue: '',
			storageCode: '',
			obj: []
		};
	},
	methods: {
		query() {
			this.request({
				url: '/Storage/GetStorages',
				method: 'GET',
				data: {
					code: this.searchValue,
					orgId: uni.getStorageSync('orgId')
				},
				success: (res) => {
					// 1001703290110519 民用泵
					// 1001703290110977 商用泵
					let oid = uni.getStorageSync('orgId');
					if (!(oid == 1001703290110519 || oid == 1001703290110977)) {
						this.obj = res.response.data;
						return;
					}
					if (this.$store.state.radios.radioRerurnType == 1) {
						// 工废
						let arr = res.response.data;
						this.obj = arr.filter((t) => t.StorageCode == '30129' || t.StorageCode == '30319');
					} else if (this.$store.state.radios.radioRerurnType == 2) {
						// 料废
						let arr = res.response.data;
						this.obj = arr.filter((t) => t.StorageCode == '30111' || t.StorageCode == '30307');
					} else {
						this.obj = res.response.data;
					}
				},
				error: (res) => {
					uni.showModal({
						title: '提示',
						content: res.msg,
						confirmColor: '#ee6666', //确定字体颜色
						showCancel: false, //没有取消按钮的弹框
						buttonText: '确定'
					});
				}
			});
		},
		radioChange(evt) {
			var i = parseInt(evt.detail.value);
			var id = this.obj[i].Id;
			var storageCode = this.obj[i].StorageCode;
			var storageName = this.obj[i].StorageName;
			uni.setStorage({
				key: 'storageInfo',
				data: {
					id: id,
					storageCode: storageCode,
					storageName: storageName
				}
			});
			// vuex
			this.$store.commit('storage/setStorage', {
				storageId: id,
				storageCode: storageCode,
				storageName: storageName
			});

			uni.navigateBack({
				animationDuration: 1000
			});
		}
	}
};
</script>
