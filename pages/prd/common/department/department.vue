<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue">
			</uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">部门编码: {{item.DepartmentCode}}</view>
								<view class="cu-form-data-15">部门名称: {{item.DepartmentName}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.DepartmentCode === DepartmentCode" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		onLoad(e) {
			this.isProduct = e.isProduct
			this.request({
				url: '/EquipmentInfo/GetDepartmentsFull',
				method: 'GET',
				data:{
					orgId: uni.getStorageSync('orgId'),
					isProduct: e.isProduct == '2' ? null: '1',
					key: this.searchValue
				},
				success: res => {
					this.obj = res.response;
				},
					error: res =>{
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
			})
		},
		data() {
			return {
				isProduct: '',
				searchValue:'',
				DepartmentCode: '',
				DepartmentName:'',
				obj: []
			}
		},
		methods: {
			query() {
				this.request({
					url: '/EquipmentInfo/GetDepartmentsFull',
					method: 'GET',
					data:{
						orgId: uni.getStorageSync('orgId'),
						isProduct: this.isProduct || '1',
						key: this.searchValue
					},
					success: res => {
						this.obj = res.response;
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var Id = this.obj[i].Id;
				var DepartmentCode = this.obj[i].DepartmentCode;
				var DepartmentName = this.obj[i].DepartmentName;
				var OrgId = this.obj[i].OrgId;
				var OrgName = this.obj[i].OrgName;
				var ParentDepartmentID = this.obj[i].ParentDepartmentID;
				var ParentDepartmentName = this.obj[i].ParentDepartmentName;
				uni.setStorage({
					key: "departmentInfo",
					data: {
						"Id": Id,
						"DepartmentCode": DepartmentCode,
						"DepartmentName": DepartmentName,
						"OrgId": OrgId,
						"OrgName": OrgName,
						"ParentDepartmentID": ParentDepartmentID,
						"ParentDepartmentName": ParentDepartmentName,
					}
				})
				// vuex
				this.$store.commit('department/setDepartment', {
					departmentId: Id,
					departmentCode: DepartmentCode,
					departmentName: DepartmentName
				})
				
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
