<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group" @click="queryDepartment()">
				<view class="title"><text class="text-red">*</text>部门</view>
				<input v-model="DepartmentName" disabled="true" placeholder="请手动选择部门" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryLine()" v-if="DepartmentId">
				<view class="title"><text class="text-red">*</text>产线选择</view>
				<input v-model="LineName" disabled="true" placeholder="请手动选择产线" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryOrder()" v-if="LineName">
				<view class="title"><text class="text-red">*</text>工单选择</view>
				<input v-model="wo" disabled="true" placeholder="请手动选择工单" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" v-if="wo">
				<view style="width: 28%;">关键字</view>
				<uni-data-checkbox @change="change" mode="button" v-model="radio" :localdata="keys" />
			</view>
			<uni-search-bar v-if="wo" class="bg-white dashed-top" :focus="true" v-model="searchValue"
				@confirm="query" />
			<!-- 多选模式 -->
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
				<view class="cu-form-group padding panel-full">
					<view style="width: 90%;">
						<view class="uni-ellipsis cu-form-data-10">物料编码: {{item.MaterialCode}}</view>
						<view class="uni-ellipsis cu-form-data-10">物料规格: {{item.Specification}}</view>
						<view class="uni-ellipsis cu-form-data-10">计划数量: {{item.PlanQty}}</view>
					</view>
					<view style="width: 10%;">
						<checkbox-group @change="checkboxChange($event, index)">
							<label>
								<checkbox :value="String(item.isChecked)" :checked="item.isChecked" />
							</label>
						</checkbox-group>
					</view>
				</view>
			</view>
			<view class="uni-fixed-bottom">
				<!-- <uni-tag text="反选" type="warning" @click="checkAll" /> -->
				<uni-tag text="物料选择完成" style="width: 100%;" type="primary" @click="submit" />
			</view>
			<!-- 单选模式 -->
			<!-- <radio-group @change="radioChange">
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content" style="width: 85%;">
								<view class="uni-ellipsis">物料编码: {{item.MaterialCode}}</view>
								<view class="uni-ellipsis">物料规格: {{item.Specification}}</view>
								<view class="uni-ellipsis">计划数量: {{item.PlanQty}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Wo === currentOrderNo" />
							</view>
						</view>
					</view>
				</view>
			</radio-group> -->
		</view>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue'
	export default {
		components: {
			dsNone,
			uniTag
		},
		data() {
			return {
				radio: null,
				keys: [],
				currentOrderNo: '',
				obj: [],
				// 搜索条件
				searchValue: ''
			}
		},
		onLoad(e) {
			// this.$clearStore(this)
			this.obj = []
			this.getKeys()
		},
		onShow() {
			if (this.wo) {
				this.query()
			}
		},
		watch: {
			wo() {
				this.query()
			}
		},
		computed: {
			// 产线
			LineCode() {
				return this.$store.state.line.line.lineCode;
			},
			LineName() {
				return this.$store.state.line.line.lineName;
			},
			LineId() {
				return this.$store.state.line.line.lineId;
			},
			// 车间、部门
			DepartmentId() {
				return this.$store.state.department.department.departmentId;
			},
			DepartmentCode() {
				return this.$store.state.department.department.departmentCode;
			},
			DepartmentName() {
				return this.$store.state.department.department.departmentName;
			},
			// 工单信息
			woId() {
				return this.$store.state.wo.wo.woId;
			},
			wo() {
				return this.$store.state.wo.wo.wo;
			},
			woName() {
				return this.$store.state.wo.wo.woName;
			}
		},
		methods: {
			change(e) {
				this.searchValue = e.detail.value
				this.query()
			},
			// 清除 store
			clearStore() {
				this.$store.commit('wo/empty')
				this.$store.commit('department/empty')
				this.$store.commit('line/empty')
			},
			// 获取 关键字条件
			getKeys() {
				this.request({
					url: '/DictionaryDetails/GetTreeDataByCode?dataCode=KJSS',
					method: 'GET',
					success: res => {
						const keyArr = res.response.data
						for (let item of keyArr) {
							this.keys.push({
								value: item.id,
								text: item.label
							})
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询部门
			queryDepartment() {
				this.clearStore()
				uni.navigateTo({
					url: '../department/department'
				})
			},
			// 产线
			queryLine() {
				this.$store.commit('line/empty');
				uni.navigateTo({
					url: '../productionLine/productionLine?departmentId=' + this.DepartmentId
				})
			},
			// 查询工单
			queryOrder() {
				uni.navigateTo({
					url: '../../ProductionManagement/conmon/orderQuery/orderQuery?LineName=' + this.LineName
				})
			},
			// 查询工单备料详情
			query() {
				this.request({
					url: '/ShipOrderDetail/QueryShipMaterial',
					method: 'GET',
					data: {
						key: this.searchValue,
						wo: this.wo
					},
					success: res => {
						if (res.response.length == 0) {
							this.obj = []
							this.$toast('未查询到数据')
							return
						}
						this.obj = res.response;
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			/* 
				@Desc：多选模式 
				@Author：DingShuai 
				@UpdateDate：20220905 
			*/
			/* 整理多选数据并返回上一级界面 */
			submit(){
				const res = this.getCheckedDate()
				this.$store.commit('createWOMM/setCreateWOMM', res)
				uni.navigateBack({
					animationDuration: 1000
				})
			},
			/* 获取到所有已选中物料 */
			getCheckedDate(){
				const temp = this.obj
				let arr = []
				for(let i = 0; i < temp.length; i++){
					if(temp[i].isChecked){
						arr.push(temp[i])
					}
				}
				return arr
			},
			checkboxChange: function(e, index) {
				const values = e.detail.value
				const res = Boolean(values[0])
				this.obj[index].isChecked = res
			},
			/* ------------------------------------------------------------------------------------ */
			/* 单选模式 */
			radioChange(evt) {
				const i = parseInt(evt.detail.value);
				const materialId = this.obj[i].MaterialId;
				const materialCode = this.obj[i].MaterialCode;
				const materialName = this.obj[i].MaterialName;
				const Specification = this.obj[i].Specification;
				const planQty = this.obj[i].PlanQty;

				this.$store.commit('createWO/setCreateWO', {
					sourceWO: this.wo,
					materialId: materialId,
					materialCode: materialCode,
					materialName: materialName,
					specification: Specification,
					planQty: planQty,
					lineId: this.LineId,
					departmentId: this.DepartmentId,
				})

				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
