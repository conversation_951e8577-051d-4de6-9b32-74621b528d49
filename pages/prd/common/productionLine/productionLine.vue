<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue">
			</uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-10">产线编码: {{item.Code}}</view>
								<view class="cu-form-data-10">产线名称: {{item.Name}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Code === LineCode" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		onLoad(e) {
			this.departmentId = e.departmentId;
			this.query();
		},
		data() {
			return {
				departmentId: null,
				searchValue: '',
				LineCode: '',
				obj: []
			}
		},
		methods: {
			query(){
				this.request({
					url: '/EquipmentInfo/GetProductionLines',
					method: 'GET',
					data: {
						key: this.searchValue,
						departmentId: typeof(this.departmentId) == "undefined" ? 0:this.departmentId
					},
					success: res => {
						this.obj = res.response;
					},
					error: res =>{
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var id = this.obj[i].Id;
				var LineCode = this.obj[i].Code;
				var LineName = this.obj[i].Name;
				uni.setStorage({
					key: "lineInfo",
					data: {
						"id": id,
						"LineCode": LineCode,
						"LineName": LineName
					}
				})
				// vuex
				this.$store.commit('line/setLine', {
					lineId: id,
					lineCode: LineCode,
					lineName: LineName
				})
				
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
