<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<!-- <uni-search-bar :focus="true" v-model="searchValue" @input="query"></uni-search-bar> -->
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">编码: {{item.id}}</view>
								<view class="cu-form-data-15">名称: {{item.label}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.id === id" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		onLoad(e) {
			this.type = e.type;
			this.query();
		},
		data() {
			return {
				id: '',
				label:'',
				obj: [],
				// 搜索条件
				searchValue:'',
				type:''
			}
		},
		methods: {
			query(){
				this.request({
					url: '/DictionaryDetails/GetTreeDataByCode',
					method: 'GET',
					data:{
						dataCode: this.type
					},
					success: res => {
						this.obj = res.response.data;
					},
					error: res =>{
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var id = this.obj[i].id;
				var label = this.obj[i].label;
				uni.setStorage({
					key: "u9Info",
					data: {
						"id": id,
						"label": label,
					}
				})
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
