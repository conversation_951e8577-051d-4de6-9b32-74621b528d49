<template>
  <view class="uni-common-mt">
    <view class="uni-padding-wrap">
      <uni-search-bar :focus="true" v-model="searchValue" @confirm="query"></uni-search-bar>
      
      <!-- 将一键全选按钮放在固定位置 -->
      <view class="fixed-bottom">
        <button @click="selectAll">一键全选</button>
      </view>
      
      <checkbox-group @change="radioChange" class="margin-top">
        <ds-none :obj="obj"></ds-none>
        <view class="uni-list" v-if="obj.length != 0">
          <view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
            <view class="cu-form-group solid-bottom panel-full">
              <view class="content" style="width: 85%;">
                <view class="cu-form-data-14 uni-ellipsis">工单号: {{item.Wo}}</view>
                <view class="cu-form-data-14 uni-ellipsis">物料名称: {{item.MaterialName}}</view>
                <view class="cu-form-data-14 uni-ellipsis">规格: {{item.Specification}}</view>
                <view class="cu-form-data-14 uni-ellipsis">计划开工时间: {{item.PlanStartDate}}</view>
                <view class="cu-form-data-14 uni-ellipsis">计划数量: {{item.PlanQty}}</view>
                <view class="cu-form-data-14 uni-ellipsis" v-show="item.SourceWo">来源工单: {{item.SourceWo}}</view>
              </view>
              <view>
                <checkbox :value="item.Wo" :checked="item.checked" />
              </view>
            </view>
          </view>
        </view>
      </checkbox-group>
    </view>
  </view>
</template>

<script>
import dsNone from '@/components/ds-none'
export default {
  components: { dsNone },
  data() {
    return {
      woStart: null,
      currentOrderNo: '',
      MaterialCode: '',
      MaterialName: '',
      Specification: '',
      obj: [],
      LineName: null,
      finish: '',
      // 搜索条件
      searchValue: '',
      startDate: '',
      selectOrdes: [],
      // 数据量
      pageSize: 500,
    }
  },
  onLoad(e) {
    this.woStart = e.woStart;
    this.startDate = e.startDate;
    this.LineName = e.LineName
    this.finish = e.finish
    this.query();
  },
  onShow() {
    this.query();
  },
  onNavigationBarButtonTap(e) {
    if (e.index == 0) {
      uni.setStorage({
        key: "ordersInfo",
        data: this.selectOrdes
      })
      // uni.setStorage({
      // 	key: "ordersInfo",
      // 	data: {
      // 		"orderNo": orderNo,
      // 		"orderName": orderName,
      // 		"Id": id,
      // 		"Specification": Specification,
      // 		"MaterialId": MaterialId,
      // 		"MaterialCode": MaterialCode,
      // 		"MaterialName": MaterialName,
      // 		"ShipId": ShipId,
      // 		"PlanQty": PlanQty
      // 	}
      // })
      // VUEX
      // debugger
      // this.$store.commit('wo/setWO',{
      // 	wo: orderNo,
      // 	woName: orderName,
      // 	Id: id,
      // 	Specification: '',
      // 	MaterialId: MaterialId,
      // 	MaterialCode: MaterialCode,
      // 	MaterialName: MaterialName,
      // 	ShipId: ShipId
      // })
      uni.navigateBack({
        animationDuration: 1000
      })
    }
  },
  onReachBottom() {
    this.pageSize += 500
    this.query()
  },
  methods: {
    selectAll() {
      this.obj.forEach((item) => {
        this.selectOrdes = this.obj.map(item => item)
        this.$set(item, 'checked', true)
      })
    },
    query() {
      this.request({
        url: '/WOMain/Get',
        method: 'GET',
        data: {
          wo: this.searchValue,
          startDate: this.startDate || null,
          LineName: this.LineName || null,
          finish: this.finish || null,
          woStart: this.woStart || 0,
          inPageSize: this.pageSize,
          inPageIndex: 1
        },
        success: res => {
          this.obj = res.response.data;
          let items = this.obj
          let values = this.selectOrdes.map((item) => item.Id)
          if (this.selectOrdes.length) {
            // 触底重新加载数据  勾选状态被新数据覆盖了
            for (var i = 0, lenI = items.length; i < lenI; ++i) {
              const item = items[i]
              if (values.indexOf(item.Id) >= 0) {
                this.$set(item, 'checked', true);

              } else {
                this.$set(item, 'checked', false);
              }
            }
          }
        },
        error: res => {
          this.$error(res.msg)
        }
      })
    },

    radioChange(e) {
      var items = this.obj;
      var values = e.detail.value;
      this.selectOrdes = items.filter((i) => {
        return values.indexOf(i.Wo) >= 0;
      });
      for (var i = 0, lenI = items.length; i < lenI; ++i) {
        const item = items[i]
        if (values.indexOf(item.Wo) >= 0) {
          this.$set(item, 'checked', true);

        } else {
          this.$set(item, 'checked', false);
        }
      }
    },
  }
}
</script>

<style>
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  z-index: 9999;
  text-align: center;
  padding: 16px;
}
</style>