<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar :focus="true" v-model="searchValue" @confirm="query"></uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content" style="width: 85%;">
								<view class="cu-form-data-14 uni-ellipsis">工单号: {{item.Wo}}</view>
								<view class="cu-form-data-14 uni-ellipsis">物料名称: {{item.MaterialName}}</view>
								<view class="cu-form-data-14 uni-ellipsis">规格: {{item.Specification}}</view>
								<view class="cu-form-data-14 uni-ellipsis">计划开工时间: {{item.PlanStartDate}}</view>
								<view class="cu-form-data-14 uni-ellipsis" v-show="item.SourceWo">来源工单: {{item.SourceWo}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.Wo === currentOrderNo" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
import dsNone from '@/components/ds-none'
	export default {
		components:{dsNone},
		data() {
			return {
				woStart: null,
				currentOrderNo: '',
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				obj: [],
				LineName:null,
				finish: '',
				// 搜索条件
				searchValue:'',
				startDate:'',
				pageSize: 20
			}
		},
		onLoad(e) {
			this.woStart = e.woStart;
			this.startDate = e.startDate;
			this.LineName = e.LineName
			this.finish = e.finish
			this.query();
		},
		onShow() {
			this.query();
		},
		onReachBottom() {
			this.pageSize += 20
			this.query()
		},
		methods: {
			query(){
				this.request({
					url: '/WOMain/Get',
					method: 'GET',
					data: {
						wo: this.searchValue,
						startDate: this.startDate || null,
						LineName: this.LineName || null,
						finish: this.finish || null,
						woStart: this.woStart || 20,						
						inPageSize: this.pageSize,
						inPageIndex: 1
					},
					success: res => {
						this.obj = res.response.data;
					},
					error: res =>{
						this.$error(res.msg)
					}
				})
			},
			radioChange(evt) {
				// console.log("123" + evt);
				var i = parseInt(evt.detail.value);
				var orderNo = this.obj[i].Wo;
				var orderName = this.obj[i].WOName;
				var id = this.obj[i].Id;
				var MaterialCode = this.obj[i].MaterialCode;
				var MaterialName = this.obj[i].MaterialName;
				var Specification = this.obj[i].Specification;
				var MaterialId = this.obj[i].MaterialId;
				var ShipId = this.obj[i].ShipId;
				var PlanQty =  this.obj[i].PlanQty;
				uni.setStorage({
					key: "outOrderInfo",
					data: {
						"orderNo": orderNo,
						"orderName": orderName,
						"Id": id,
						"Specification": Specification,
						"MaterialId": MaterialId,
						"MaterialCode": MaterialCode,
						"MaterialName": MaterialName,
						"ShipId": ShipId,
						"PlanQty": PlanQty
					}
				})
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>

