<template>
	<view class="uni-common-mt">
		<view>班次时间:[ {{ timeShiftString }} ]</view>
		<uni-search-bar v-model="filterKey"></uni-search-bar>
		<view style="margin-top: 5px" class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(line, index) in filterList" :key="index">
			<view class="cu-form-group solid-bottom panel-full">
				<view>
					<strong>
						<view class="text-black cu-form-data-10">产线编码:{{ line.LineCode }}</view>
						<view class="text-black cu-form-data-10">产线名称:{{ line.LineName }}</view>
					</strong>
					<!-- <view v-if="nextTime(line.InspTime) == '未检验'" style="color: red">今日未检验</view> -->
					<view v-if="nextTime(line.InspTime) == '未检验'">今日未检验</view>
					<view v-else>
						<view class="text-black cu-form-data-10">检验时间:{{ line.InspTime }}</view>
						<view class="text-black cu-form-data-10">下次检验时间:{{ formatDate(nextTime(line.InspTime)) }}</view>
						<!-- <view class="text-black cu-form-data-10" :style="downcount(line.InspTime) <= 0 ? 'color: red' : ''">距离下次时间:{{ downcount(line.InspTime) }}分</view> -->
						<view class="text-black cu-form-data-10">距离下次时间:{{ downcount(line.InspTime) }}分</view>
					</view>
				</view>
				<!-- <uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25"></uni-icons> -->
			</view>
		</view>

		<view class="uni-fixed-bottom xj_button_group margin-top">
			<view class="xj_button" style="margin-left: 70%; width: 30%; background-color: #009598" @click="addInsp()">录单</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			filterKey: '',
			timeShiftString: '8:00-11:30,12:30-17:00,18:00-21:00',
			timeShift: [], // 时间班次
			lineList: [
				{
					LineCode: 'LineCode01', // 产线编码
					LineName: 'LineName01', // 产线名称
					InspTime: '2025-03-25 11:00' // 检验时间
				}
			]
		};
	},
	computed: {
		filterList() {
			if (this.filterKey == null || this.filterKey == '') return this.lineList;
			return this.lineList.filter((t) => t.LineCode.toUpperCase().includes(this.filterKey.toUpperCase()) || t.LineName.toUpperCase().includes(this.filterKey.toUpperCase()));
		}
	},
	created() {
		this.getTimeShift();
		this.getLine();
	},
	onShow() {
		this.getLine();
	},
	methods: {
		addInsp() {
			uni.navigateTo({
				url: './addInsp'
			});
		},
		/**
		 * 获取班次时间
		 */
		getTimeShift() {
			this.request({
				url: '/inspectionTaskMain/getTimeShift',
				method: 'get',
				success: (res) => {
					this.timeShiftString = res.response;
					this.timeShift = [];
					let now = new Date();
					let timeStrings = this.timeShiftString.split(',');
					for (let i = 0; i < timeStrings.length; i++) {
						let time = timeStrings[i].split('-');
						this.timeShift.push(new Date(now.getFullYear() + '-' + (now.getMonth() - -1) + '-' + now.getDate() + ' ' + time[0]));
						this.timeShift.push(new Date(now.getFullYear() + '-' + (now.getMonth() - -1) + '-' + now.getDate() + ' ' + time[1]));
					}
				},
				error: (res) => {
					this.$toast('获取班次信息失败');
				}
			});
		},
		formatDate(time) {
			if (time == '未检验') return time;
			// 将输入的时间转换为 Date 对象
			const date = new Date(time);

			// 检查时间是否有效
			if (isNaN(date.getTime())) {
				return 'Invalid Date';
			}

			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');

			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
		/**
		 * @param {Object} time
		 */
		nextTime(time) {
			// if (time == null || time == '') return new Date(this.timeShift[0]);
			if (time == null || time == '') return '未检验';
			// 间隔时间
			let gapTime = 2 * 1000 * 60 * 60;
			// 下次巡检时间
			let nextTime = new Date(time);
			// 根据班次计算下次巡检时间
			for (let i = 0; i < this.timeShift.length - 1 && gapTime > 0; i += 2) {
				if (nextTime > this.timeShift[i + 1]) continue;
				if (nextTime < this.timeShift[i]) nextTime = new Date(this.timeShift[i]);
				let remainTime = this.timeShift[i + 1] - nextTime;
				if (remainTime >= gapTime) {
					return nextTime.setMilliseconds(nextTime.getMilliseconds() + gapTime);
				} else {
					nextTime = nextTime.setMilliseconds(nextTime.getMilliseconds() + remainTime);
					gapTime -= remainTime;
				}
			}

			// 不足间隔时间 到第二天
			if (gapTime > 0) {
				return new Date(this.timeShift[0]).setDate(this.timeShift[0].getDate() + 1);
			}

			// if (gapTime == 2 * 1000 * 60 * 60) {
			// 	return this.timeShift[0].setDate(this.timeShift[0].getDate() + 1);
			// }

			// 不足间隔时间 到本日最后时间
			// if (gapTime > 0) {
			// 	return this.timeShift[this.timeShift.length - 1];
			// }

			return '请立即检验';
		},
		/**
		 * 计算距离下次时间
		 * @param {Object} time
		 */
		downcount(time) {
			if (time == null || time == '') return 0;
			let nextTime = this.nextTime(time);
			if (nextTime == '未检验') return 0;
			return Math.floor((new Date(nextTime) - new Date()) / 1000 / 60);
		},
		/**
		 * 获取产线巡检信息 (根据当日时间查询)
		 */
		getLine() {
			this.request({
				url: '/inspectionTaskMain/getLineInsp',
				method: 'get',
				success: (res) => {
					this.lineList = res.response;
				},
				error: (res) => {
					this.$toast('获取产线信息失败');
				}
			});
		}
	}
};
</script>

<style></style>
