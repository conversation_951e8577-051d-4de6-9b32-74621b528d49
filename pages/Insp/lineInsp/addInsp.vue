<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">工单号</view>
				<input v-model="form.WO" @confirm="queryWo()" placeholder="请手动扫描工单号" :focus="focusZore" />
				<uni-icons @click="scan" class="uni-''panel-icon uni-icon alin_x_center" type="scan" color="#8f8f94" size="25" />
			</view>
			<view class="cu-form-group">
				<view>
					<view class="uni-ellipsis">料号：{{ form.ItemCode }}</view>
					<view class="uni-ellipsis">料品：{{ form.ItemName }}</view>
					<view class="uni-ellipsis">规格型号：{{ form.ItemSpec }}</view>
					<view class="uni-ellipsis">产线：{{ form.LineName }}</view>
					<view class="uni-ellipsis">车间：{{ form.DepartmentName }}</view>
				</view>
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">检验数量</view>
				<input v-model="form.CheckQty" min="0" placeholder="请输入" type="number" />
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">检验结果</view>
				<uni-data-select
					style="background-color: #ffffff"
					v-model="form.Result"
					:localdata="[
						{ value: 0, text: '请选择' },
						{ value: 1, text: '合格' },
						{ value: 2, text: '不合格' }
					]"
					@change="(val) => {}"
				></uni-data-select>
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">处理方式</view>
				<uni-data-select
					style="background-color: #ffffff"
					v-model="form.Dispose"
					:localdata="[
						{ value: '0', text: '停产' },
						{ value: '1', text: '继续生产' }
					]"
					@change="(val) => {}"
				></uni-data-select>
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">满足客户需求</view>
				<uni-data-select
					style="background-color: #ffffff"
					v-model="form.Customer"
					:localdata="[
						{ value: 0, text: '请选择' },
						{ value: 1, text: '满足' },
						{ value: 2, text: '不满足' }
					]"
					@change="(val) => {}"
				></uni-data-select>
			</view>

			<view v-for="(row, index) in detail" :key="index" style="border: 1px solid rgb(192 196 204); margin-top: 5px; border-radius: 3px">
				<view class="cu-form-group">
					<view>
						<view class="uni-ellipsis">检测项目名称：{{ row.InspectionName }}</view>
						<view class="uni-ellipsis">属性：{{ row.CheckTypeName }}</view>
						<view class="uni-ellipsis">上限：{{ row.Upper }}</view>
						<view class="uni-ellipsis">目标值：{{ row.Standard }}</view>
						<view class="uni-ellipsis">下限：{{ row.Lower }}</view>
						<view class="uni-ellipsis">工序：{{ row.ProcessName }}</view>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">实测值1</view>
					<input v-model="row.TestValue" placeholder="请输入" type="number" @confirm="valueInput(row)" @blur="valueInput(row)" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">实测值2</view>
					<input v-model="row.TestValue2" placeholder="请输入" type="number" @confirm="valueInput(row)" @blur="valueInput(row)" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="width: max-content">检验结果</view>
					<uni-data-checkbox
						@change="rowResultChange"
						mode="button"
						v-model="row.Result"
						:localdata="[
							{
								text: '合格',
								value: 1
							},
							{
								text: '不合格',
								value: 2
							}
						]"
					/>
				</view>
			</view>

			<view style="height: 100px">
				<!-- 底部空隙 -->
			</view>
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="margin-right: 5%; width: 30%; color: black" @click="cancel">取消</view>
				<view class="xj_button" style="margin-left: 0%; width: 30%; background-color: #ffaa00" @click="save">保存</view>
				<view class="xj_button" style="margin-left: 5%; width: 30%; background-color: #009598" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	data() {
		return {
			focusZore: true,
			form: {
				Id: 0,
				WO: '',
				InspectionType: 15,
				CheckQty: 0,
				MaterialId: null,
				ItemCode: '',
				ItemName: '',
				ItemSpec: '',
				Result: 0,
				Dispose: null,
				Customer: 0,
				Line: null,
				LineName: '',
				DepartmentId: '',
				DepartmentName: ''
			},
			// main: {},
			detail: [],
			originDetail: []
		};
	},
	methods: {
		valueInput(val) {
			if (val.CheckType === 2) {
				let allNull = true;
				let allQua = true;
				for (let i = 1; i <= 5; i++) {
					let key = `TestValue${i == 1 ? '' : i}`;
					if (val[key] === '') val[key] = null;
					if (val[key] != null) {
						allNull = false;
						if (!(val[key] <= val.Upper && val[key] >= val.Lower)) allQua = false;
					}
				}
				val.Result = allQua ? 1 : 2;
				if (allNull) val.Result = null;
			}
		},
		rowResultChange() {},
		changeBlur() {
			this.focusZore = false;
		},
		scan() {
			uni.scanCode({
				success: (res) => {
					this.form.WO = res.result;
					this.queryWo();
				},
				fail: () => {
					this.$toast('扫码失败');
				}
			});
		},
		queryWo() {
			if (!this.form.WO) {
				this.$toast('未获取的工单号');
				return;
			}
			this.request({
				url: '/WOMain/Get',
				method: 'GET',
				data: {
					wo: this.form.WO,
					inPageSize: 10,
					inPageIndex: 1
				},
				success: (res) => {
					if (res.response.dataCount == 1 && this.form.WO == res.response.data[0].Wo) {
						let wo = res.response.data[0];

						this.form.MaterialId = wo.MaterialId;
						this.form.ItemCode = wo.MaterialCode;
						this.form.ItemName = wo.MaterialName;
						this.form.ItemSpec = wo.Specification;
						this.form.Line = wo.LineId;
						this.form.LineName = wo.LineName;
						this.form.DepartmentId = wo.DepartmentId;
						this.form.DepartmentName = wo.DepartmentName;
						// TODO 任务领取操作 以及 查询数据
						this.checKPost('?type=1');
						this.changeBlur();
					} else {
						this.form.WO = '';
						this.$error('无此工单');
					}
				},
				error: (res) => {
					this.$error(res.msg);
					this.cancel();
				}
			});
		},
		/// 新增检验
		checKPost(type = '') {
			this.request({
				url: '/InspectionTaskMain/Post' + type,
				method: 'POST',
				data: {
					status: 100,
					...this.form
				},
				success: (res) => {
					// 打印输出
					// console.log(res.response);
				},
				error: (res) => {
					if (res.msg == '添加成功') {
						let param = {
							wo: this.form.WO,
							instionType: this.form.InspectionType == null ? 15 : this.form.InspectionType
						};
						this.getInspectionTaskMain(param);
					} else if (res.msg == '当前类型检验任务已合格,确定要继续创建吗') {
						this.checKPost();
					} else {
						this.cancel();
						this.$toast(res.msg);
					}
				}
			});
		},
		getInspectionTaskMain(param) {
			this.request({
				url: '/inspectionTaskMain/getInspectionTaskMain',
				method: 'GET',
				data: param,
				success: (res) => {
					this.form = { ...this.form, ...res.response };
					let param = {
						page: 1,
						key: this.form.Id,
						intPageSize: 10000
					};
					this.getDetail(param);
				},
				error: (res) => {
					this.cancel();
					this.$toast(res.msg);
				}
			});
		},
		getDetail(param) {
			this.request({
				url: '/inspectionTaskDetail/get',
				method: 'GET',
				data: param,
				success: (res) => {
					this.detail = res.response.data;
					this.originDetail = JSON.parse(JSON.stringify(this.detail));
				},
				error: (res) => {
					this.$toast(res.msg);
				}
			});
		},

		cancel() {
			this.form = {
				Id: 0,
				WO: '',
				InspectionType: 15,
				CheckQty: 0,
				MaterialId: null,
				ItemCode: '',
				ItemName: '',
				ItemSpec: '',
				Result: 0,
				Dispose: null,
				Customer: 0,
				Line: null,
				LineName: '',
				DepartmentId: '',
				DepartmentName: ''
			};
			this.detail = [];
			this.originDetail = [];
			this.focusZore = true;
		},
		submit() {
			if (this.form.Dispose == null || this.form.Dispose == '') {
				this.$toast('请选择处理方式');
				return;
			}
			let param = {
				inspectionTaskMain: this.form,
				inspectionTaskDetails: this.detail,
				inspectionTaskDetailss: this.originDetail
			};
			if (this.form.Id != null && this.form.Id != 0) {
				this.request({
					url: '/inspectionTaskMain/put',
					method: 'put',
					data: param,
					success: (res) => {
						// this.cancel();
					},
					error: (res) => {
						if (res.msg == '检验操作成功') {
							this.$toast('提交成功');
							this.cancel();
						} else {
							this.$toast(res.msg);
						}
					}
				});
			} else {
				this.checKPost();
			}
		},
		save() {
			let param = {
				inspectionTaskMain: this.form,
				inspectionTaskDetails: this.detail,
				inspectionTaskDetailss: this.originDetail
			};
			if (this.form.Id != null && this.form.Id != 0) {
				this.request({
					url: '/inspectionTaskMain/puts',
					method: 'put',
					data: param,
					success: (res) => {
						// this.cancel();
					},
					error: (res) => {
						if (res.msg == '检验操作成功') {
							this.$toast('保存完成');
						} else {
							this.$toast(res.msg);
						}
					}
				});
			} else {
				this.checKPost();
			}
		}
	}
};
</script>

<style></style>
