<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu">
				<input v-model="MaterialCode" placeholder="扫描条码" @input="getMaterialCode()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" @click="takePhoto()" type="scan"
					color='#8f8f94' size="25" />
			</view>
			<uni-segmented-control :current="current" :values="items" :style-type="styleType"
				:active-color="activeColor" @clickItem="onClickItem" />
			<view v-if="current === 0">
				<uni-table ref="table" stripe :loading="loading" border stripe emptyText="暂无更多数据">
					<uni-tr>
						<uni-th align="center">到货单号</uni-th>
						<uni-th align="center">供应商编码</uni-th>
						<uni-th align="center">供应商名称</uni-th>
						<uni-th align="center">物料编码</uni-th>
						<uni-th align="center">物料名称</uni-th>
						<uni-th align="center">物料规格</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData" :key="index" v-show="item.Status != 0">
						<uni-td>
							<view class="uni-ellipsis">{{ item.CheckCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.VendorCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.VendorName }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.MaterialCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.MaterialName }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.Specification }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-group">
								<button @click="getDetailInfo(item, true)" size="mini" type="xj">查看</button>
							</view>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
			<view v-if="current === 1">
				<uni-table ref="table" stripe :loading="loading" border stripe emptyText="暂无更多数据">
					<uni-tr>
						<uni-th align="center">到货单号</uni-th>
						<uni-th align="center">供应商编码</uni-th>
						<uni-th align="center">供应商名称</uni-th>
						<uni-th align="center">物料编码</uni-th>
						<uni-th align="center">物料名称</uni-th>
						<uni-th align="center">物料规格</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData" :key="index"
						v-show="item.Status == 0 && item.TaskAccepter == null">
						<uni-td>
							<view class="uni-ellipsis">{{ item.CheckCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.VendorCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.VendorName }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.MaterialCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.MaterialName }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.Specification }}</view>
						</uni-td>
						<uni-td>
							<view style="width: 210upx;">
								<button size="mini" type="xj" @click="getTask(item.Id)">领取任务</button>
							</view>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
			<view v-if="current === 2">
				<uni-table ref="table" stripe :loading="loading" border stripe emptyText="暂无更多数据">
					<uni-tr>
						<uni-th align="center">到货单号</uni-th>
						<uni-th align="center">供应商编码</uni-th>
						<uni-th align="center">供应商名称</uni-th>
						<uni-th align="center">物料编码</uni-th>
						<uni-th align="center">物料名称</uni-th>
						<uni-th align="center">物料规格</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in myTableData" :key="index">
						<uni-td>
							<view class="uni-ellipsis">{{ item.CheckCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.VendorCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.VendorName }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.MaterialCode }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.MaterialName }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.Specification }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-group">
								<button @click="getDetailInfo(item, false)" class="uni-button" size="mini"
									type="xj">检验</button>
							</view>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				loading: false,
				tableData: [],
				myTableData: [],
				// 是否可编辑
				isEdit: false,
				// 条码
				barcode: '',
				// 物料
				MaterialId: null,
				MaterialCode: '',
				MaterialName: '',
				Specification: '',
				// 滑动模块配置
				items: ['已检', '待检', '我的检验'],
				current: 1,
				colorIndex: 0,
				activeColor: '#009598',
				styleType: 'button'
			}
		},
		onLoad() {
			this.queryTask();
			this.queryMyTask();
		},
		onShow() {},
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		methods: {
			// 领取任务
			getTask(id) {
				this.request({
					url: '/IQCDetail/Post/' + id,
					method: 'POST',
					success: res => {
						// 提示成功
						uni.showToast({
							title: res.msg,
							icon: 'success'
						})
						// 刷新数据
						this.queryTask();
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			// 物料码
			getMaterialCode() {
				const that = this;
				this.barcode = this.MaterialCode;
				let temp = this.MaterialCode.split('@');
				if (temp.length !== 7) {
					uni.showModal({
						title: '提示',
						content: '扫描出的条码格式错误！',
						confirmColor: '#ee6666', //确定字体颜色
						showCancel: false, //没有取消按钮的弹框
						buttonText: '确定',
						success: function(res) {}
					});
					that.MaterialCode = '';
					that.focus = true;
					return;
				} else {
					that.MaterialCode = temp[1];
					that.queryTask();
				}
			},
			// 调用相机
			takePhoto() {
				const that = this;
				uni.scanCode({
					success: function(res) {
						let temp = JSON.stringify(res.result);
						console.log(temp);
						that.MaterialCode = temp;
					}
				});
			},
			// 查询检验任务
			queryTask() {
				this.request({
					url: '/IQCDetail/Get',
					method: 'GET',
					data: {
						intPageSize: 9999,
						page: 1,
						key: this.MaterialCode || null
					},
					success: res => {
						this.tableData = res.response.data;
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			// 查询我的检验任务
			queryMyTask() {
				this.request({
					url: '/IQCDetail/Get',
					method: 'GET',
					data: {
						intPageSize: 9999,
						page: 1,
						type: 1
					},
					success: res => {
						this.myTableData = res.response.data;
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			// 查看详情
			getDetailInfo(item, isEdit) {
				uni.navigateTo({
					url: './IQCDetail?item=' + encodeURIComponent(JSON.stringify(item)) + '&isEdit=' + isEdit
				})
			},
			// 清空数据
			cancel() {

			},
			// swiper
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex
				}
			},
		}
	}
</script>
<style>
	.cu {
		border: 1px;
		border-radius: 5px;
		padding: 1upx 15upx;
		display: flex;
		align-items: center;
		min-height: 100upx;
		justify-content: space-between;
	}

	.cu input {
		flex: 1;
		font-size: 30upx;
		color: #555;
		padding-right: 20upx;
	}
</style>
