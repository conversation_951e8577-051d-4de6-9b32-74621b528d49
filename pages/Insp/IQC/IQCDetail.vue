<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item">供应商：{{info.VendorName}}</view>
				<view class="uni-flex-item">料号：{{info.MaterialCode}}</view>
				<view class="uni-flex-item">品名：{{info.MaterialName}}</view>
				<view class="uni-flex-item">规格型号：{{info.Specification}}</view>
				<view class="uni-flex-item">到货单号：{{info.RTCode}}</view>
				<view class="uni-flex-item">到货数量：{{info.Qty}}</view>
				<view class="uni-flex-item">到货时间：{{info.DeliveryDate}}</view>
			</view>
			<view class="uni-bg-white margin-top">
				<text class="cuIcon-titles text-blue"></text>检验标准
			</view>
			<view class="cu-form-group">
				<view style="width: 20%; text-align:center;"><strong>检验项目</strong></view>
				<view style="width: 60%; text-align:center;"><strong>标准要求</strong></view>
				<view style="width: 20%; text-align:center;"><strong>不合格</strong></view>
			</view>
			<view v-for="(item, index) in checkInfo" :key="index">
				<view class="cu-form-group">
					<view style="width: 20%; text-align:center; font-size: 20upx">{{item.InspectionName}}</view>
					<view style="width: 60%; text-align:center; font-size: 20upx">{{item.Standard}}</view>
					<view style="width: 20%; text-align:center; font-size: 20upx">
						<checkbox-group @change="checkboxChange($event, index)">
							<label>
								<checkbox :disabled="isEdit" :value="String(checkInfo[index].Result)"
									:checked="checkInfo[index].Result == 0" />
							</label>
						</checkbox-group>
					</view>
				</view>
			</view>
			<view class="uni-bg-white margin-top">
				<text class="cuIcon-titles text-blue"></text>交验信息
			</view>
			<view class="cu-form-group">
				<view class="title">抽检数量</view>
				<uni-number-box :disabled="isEdit" :value="samplingQty" @change="change($event, 1)" />
			</view>
			<view class="cu-form-group">
				<view class="title">抽检不良数</view>
				<uni-number-box :disabled="isEdit" :value="samplingBadQty" @change="change($event, 2)" />
			</view>
			<view class="cu-form-group">
				<view class="title">处置方式</view>
				<uni-data-select :disabled="isEdit" v-model="dealMode" :localdata="range"
					@change="changeDataSelect($event, 1)">
				</uni-data-select>
			</view>
			<view class="cu-form-group">
				<view class="title">合格收货数</view>
				<uni-number-box :value="goodReceivingQty" @change="change($event, 3)" :disabled="isEdit" />
			</view>
			<view class="cu-form-group">
				<view class="title">问题点</view>
				<input type="text" :disabled="isEdit" v-model="question" placeholder="请输入文字" />
			</view>
			<view class="cu-form-group">
				<view class="title">不良分类</view>
				<uni-data-select :disabled="isEdit" v-model="classify" :localdata="rangeClassify"
					@change="changeDataSelect($event, 2)">
				</uni-data-select>
			</view>
			<view class="cu-form-group">
				<!-- <text style="color: #E3162E;">* </text> -->
				<view class="title">故障日期</view>
				<picker :disabled="isEdit" mode="date" :value="date" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<view style="color: red;">{{date}}</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="title">赔偿比例</view>
				<uni-number-box :disabled="isEdit" :value="compensationProportion" @change="change($event, 4)" />
			</view>
			<view class="cu-form-group">
				<view class="title">赔偿金额</view>
				<uni-number-box :disabled="isEdit" :value="compensationAmount" @change="change($event, 5)" />
			</view>
			<view class="cu-form-group">
				<view class="title">备注：</view>
				<!-- <textarea v-model="note" placeholder="请输入文字" /> -->
				<input type="text" :disabled="isEdit" v-model="note" placeholder="请输入文字" />
			</view>
			<view class="cu-form-group">
				<view class="title">是否考核</view>
				<switch :disabled="isEdit" @change="switchChange" />
			</view>
			<view class="cu-form-group">
				<view class="title">不考核原因</view>
				<!-- <textarea v-model="NoCheckReason" placeholder="请输入文字" /> -->
				<input type="text" :disabled="isEdit" v-model="NoCheckReason" placeholder="请输入文字" />
			</view>
			<view class="cu-form-group margin-top" v-show="!isEdit">
				<uni-tag text="清空" type="warning" @click="cancel" />
				<uni-tag text="确认提交" type="success" @click="submit()" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import UniNumberBox from '@/components/uni-ui/uni-number-box/uni-number-box.vue'
	export default {
		components: {
			uniTag,
			UniIcons,
			UniNumberBox
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				//
				info: null,
				//
				isEdit: null,
				// 
				checkInfo: [],
				// 是否可修改标志位
				disabled: null,
				// 抽检数量
				samplingQty: 0,
				// 抽检不良数
				samplingBadQty: 0,
				// 处置方式下拉框
				dealId: null,
				dealMode: '',
				range: [],
				// 合格收货数
				goodReceivingQty: 0,
				// 问题点
				question: '',
				// 不良分类
				classify: '',
				rangeClassify: [],
				// 故障时间
				date: currentDate,
				// 赔偿比例
				compensationProportion: 0,
				// 赔偿金额
				compensationAmount: 0,
				// 备注
				note: '',
				// 是否考核
				isCheck: false,
				// 不考核原因
				NoCheckReason: ''
			}
		},
		onLoad(e) {
			// 处理传参
			this.info = JSON.parse(decodeURIComponent(e.item));
			console.log('info：' + this.info);
			const temp = e.isEdit;
			const that = this;
			if ('false' === temp) {
				that.isEdit = false;
			} else {
				that.isEdit = true;
			}
			// console.log('IQCDeatail-isEdit：' + typeof(this.isEdit));
			// console.log('IQCDeatail-isEdit：' + this.isEdit);
			// 加载处置方式
			this.loadDealMode();
			// 加载不良分类
			this.loadClassify();
		},
		onShow() {
			this.loadDetailInfo();
		},
		computed: {
			/* date picker */
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			submit(){
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要清空全部信息吗？', function(e) {
					//0==确认，否则取消  
					if(e.index == 0){
						that.reqSubmit();
					} else {
						
					}
				}, {
					"title": '提示',
					"buttons": btns,
				});
				// #endif
			},
			// 提交请求
			reqSubmit() {
				const that = this;
				that.request({
					url: '/InspectionTaskMain/IQCPut',
					method: 'PUT',
					data: {
						iqc: that.info,
						inspectionTaskDetails: that.checkInfo
					}
				})
			},
			// 清空全部
			cancel() {
				// 抽检数量
				this.samplingQty = 0;
				// 抽检不良数
				this.samplingBadQty = 0;
				// 合格收货数
				this.goodReceivingQty = 0;
				// 问题点
				this.question = '';
				// 赔偿比例
				this.compensationProportion = 0;
				// 赔偿金额
				this.compensationAmount = 0;
				// 备注
				this.note = '';
				// 是否考核
				this.isCheck = false;
				// 不考核原因
				this.NoCheckReason = ''
			},
			// 加载不良分类
			loadClassify() {
				this.request({
					url: '/BadGroup/Get',
					method: 'GET',
					success: res => {
						for (let i = 0; i < res.response.data.length; i++) {
							let temp = {
								text: res.response.data[i].BadGroupName,
								value: res.response.data[i].Id,
							};
							this.rangeClassify.push(temp);
						}
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			// 加载处置方式
			loadDealMode() {
				this.request({
					url: '/BadGroup/Get',
					method: 'GET',
					success: res => {
						for (let i = 0; i < res.response.data.length; i++) {
							let temp = {
								text: res.response.data[i].BadGroupName,
								value: res.response.data[i].Id,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			// 加载任务明细
			loadDetailInfo() {
				this.request({
					url: '/InspectionTaskDetail/Get',
					method: 'GET',
					data: {
						key: this.info.InspectionTaskMainId,
						page: 1,
						intPageSize: 9999
					},
					success: res => {
						this.checkInfo = res.response.data
					},
					error: res => {

					}
				})
			},
			// checkbox 发生变化
			checkboxChange: function(e, index) {
				const that = this;
				let values = e.detail.value;
				let res = Number(values[0]);
				if (res == 1) {
					that.checkInfo[index].Result = 0;
				} else {
					that.checkInfo[index].Result = 1;
				}
				console.log('结果值：' + that.checkInfo[index].Result);
			},
			// 数字选择框
			change(value, sign) {
				const that = this;
				if (sign == 1) {
					that.samplingQty = value;
				} else if (sign == 2) {
					that.samplingBadQty = value;
				} else if (sign == 3) {
					that.goodReceivingQty = value;
				} else if (sign == 4) {
					that.compensationProportion = value;
				} else if (sign == 5) {
					that.compensationAmount = value;
				}
			},
			// 选择框
			changeDataSelect(e, sign) {
				const that = this;
				// 1 处置方式 2 不良分类
				if (sign == 1) {
					console.log("dealId：", e);
					this.dealId = e;
				} else if (sign == 2) {
					console.log("dealId：", e);
					// this.dealId = e;
				}

			},
			/* date picker */
			bindDateChange: function(e) {
				this.date = e.detail.value
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			// 开关按钮
			switchChange: function(e) {
				console.log('switch2 发生 change 事件，携带值为', e.detail.value);
				this.isCheck = e.detail.value;
			},
		}
	}
</script>
