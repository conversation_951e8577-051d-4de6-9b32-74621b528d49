<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-segmented-control :current="current" :values="items" :style-type="styleType" :active-color="activeColor"
				@clickItem="onClickItem" />
			
			<!-- 送样页面 -->
			<view v-if="current === 0">
				<view class="cu-form-group margin-top">
					<view class="title">序列号</view>
					<input v-model="serialNo" 
					             placeholder="请输入序列号" 
					             @blur="querySerial" />
				</view>

				<view class="cu-form-group">
					<view>
						<view class="uni-ellipsis">执行标准：{{form.Standard}}</view>
						<view class="uni-ellipsis">出厂编号：{{form.FactoryNo}}</view>
						<view class="uni-ellipsis">泵型号：{{form.PumpModel}}</view>
						<view class="uni-ellipsis">泵名称：{{form.PumpName}}</view>
						<view class="uni-ellipsis">额定流量：{{form.RatedFlow}}</view>
						<view class="uni-ellipsis">额定扬程：{{form.RatedHead}}</view>
						<view class="uni-ellipsis">额定转速：{{form.RatedSpeed}}</view>
						<view class="uni-ellipsis">额定电流：{{form.RatedCurrent}}</view>
						<view class="uni-ellipsis">额定效率：{{form.RatedEfficiency}}</view>
						<view class="uni-ellipsis">进口管径：{{form.InletDiameter}}</view>
						<view class="uni-ellipsis">出口管径：{{form.OutletDiameter}}</view>
						<view class="uni-ellipsis">电机效率：{{form.MotorEfficiency}}</view>
					</view>
				</view>

				<view class="cu-form-group">
					<view class="title">送样时间</view>
					<picker mode="date" :value="form.SampleTime" @change="onDateChange">
						<view>{{form.SampleTime || '请选择日期'}}</view>
					</picker>
				</view>

				<view class="cu-form-group">
					<view class="title">送样人</view>
					<input v-model="form.Submitter" placeholder="请输入送样人" />
				</view>

				<view class="cu-form-group">
					<view class="title">试验项目</view>
					<checkbox-group @change="onCheckboxChange">
					  <label v-for="(item, index) in checkItems" :key="index">
					    <!-- 改为基于 form.TestItems 的绑定方式 -->
					    <checkbox 
					      :value="item.value" 
					      :checked="form.TestItems.includes(item.value)" 
					    />
					    {{item.name}}
					  </label>
					</checkbox-group>
				</view>

				<!-- 底部提交按钮 -->
				    <view class="submit-btn">
				      <button @click="submit" type="primary" style="background-color: #009598;">提交</button>
				    </view>
			</view>

			<!-- 我的送样记录页面 -->
			<view v-if="current === 1">
				<uni-table ref="table" stripe :loading="loading" border emptyText="暂无更多数据">
					<uni-tr>
						<uni-th align="center">序列号</uni-th>
						<uni-th align="center">泵型号</uni-th>
						<uni-th align="center">试验类型</uni-th>
						<uni-th align="center">送样时间</uni-th>
                        <uni-th align="center">试验结果</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in tableData" :key="index">
						<uni-td>
							<view class="uni-ellipsis">{{ item.SerialNo }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.PumpModel }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.TestItems }}</view>
						</uni-td>
						<uni-td>
							<view class="uni-ellipsis">{{ item.SampleTime }}</view>
						</uni-td>
						<uni-td>
						                            <button 
						                                class="view-result-btn" 
						                                size="mini" 
						                                type="primary" 
						                                @click="viewTestResult(item)"
						                            >查看</button>
						</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// tab页配置
				items: ['送样', '我的送样'],
				current: 0,
				activeColor: '#009598',
				styleType: 'button',
				
				// 表单数据
				serialNo: '',
				userName: '',
				form: {
				      SerialNo: '',          // 序列号
				      Standard: '',          // 执行标准
				      FactoryNo: '',         // 出厂编号
				      PumpModel: '',         // 泵型号
				      PumpName: '',          // 泵名称
				      RatedFlow: '',         // 额定流量
				      RatedHead: '',         // 额定扬程
				      RatedSpeed: '',        // 额定转速
				      RatedCurrent: '',      // 额定电流
				      RatedEfficiency: '',   // 额定效率
				      InletDiameter: '',     // 进口管径
				      OutletDiameter: '',    // 出口管径
				      MotorEfficiency: '',   // 电机效率
				      SampleTime: new Date().toISOString().split('T')[0], // 送样时间
				      Submitter: '',         // 送样人
				      TestItems: []          // 试验项目
				    },
				
				// 检查项
				checkItems: [{
					name: '水泵性能试验',
					value: '1'
				}, {
					name: '电机温升试验',
					value: '2'
				}, {
					name: '水泵运转试验',
					value: '3'
				}, {
					name: '水泵汽蚀试验',
					value: '4'
				}, {
					name: '水泵噪声试验',
					value: '5'
				}, {
					name: '水泵振动试验',
					value: '6'
				}, {
					name: '轴承温度试验',
					value: '7'
				}],
				
				// 表格数据
				loading: false,
				tableData: []
			}
		},
		onLoad() {
			 	let user = uni.getStorageSync('user');
			 	this.request({
			 		url:'/EquipRepairMain/GetSysUser',
			 		method: 'GET',
			 		data: {
			 			acount: user
			 		},
			 		success: res => {
						this.userName= res.response.UserName;
			 			this.form.Submitter= res.response.UserName;
			 		}
			 	})
			this.queryMyRecords()
		},
		methods: {
			// Tab切换
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex
					if (this.current === 1) {
						this.queryMyRecords()
					}
				}
			},
			    
			    getStatusText(status) {
			        switch(status) {
			            case '1':
			                return '测试中';
			            case '2':
			                return '不合格';
			            default:
			                return '合格';
			        }
			    },
			// 扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.serialNo = res.result
						this.querySerial()
					},
					fail: () => {
						uni.showToast({
							title: '扫码失败',
							icon: 'none'
						})
					}
				})
			},
			
			// 查询序列号
			querySerial() {
				if (!this.serialNo) {
					uni.showToast({
						title: '请输入序列号',
						icon: 'none'
					})
					return
				}
				
				this.request({
					url: '/SendSample/GetPumpInfo',
					method: 'GET',
					data: {
						serialNo: this.serialNo
					},
					success: res => {
						this.form = {
							...this.form,
							...res.response,
							SerialNo: this.serialNo
						}
					},
					error: res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			
			// 日期选择
			onDateChange(e) {
				this.form.SampleTime = e.detail.value
			},
			
			
			// 检查项选择
			onCheckboxChange(e) {
				this.form.TestItems = e.detail.value
			},
			
			// 提交
			submit() {
			  if (!this.form.SerialNo) {
			    uni.showToast({
			      title: '请先查询泵信息',
			      icon: 'none'
			    })
			    return
			  }
			  
			  if (!this.form.SampleTime) {
			    uni.showToast({
			      title: '请选择送样时间',
			      icon: 'none'
			    })
			    return
			  }
			  
			  if (!this.form.Submitter) {
			    uni.showToast({
			      title: '请输入送样人',
			      icon: 'none'
			    })
			    return
			  }
			  
			  if (this.form.TestItems.length === 0) {
			    uni.showToast({
			      title: '请选择至少一项试验项目',
			      icon: 'none'
			    })
			    return
			  }
			  
			  // 处理试验项目数据
			  const submitData = {
			      SerialNo: this.form.SerialNo,
			        PumpModel: this.form.PumpModel,
			        PumpName: this.form.PumpName,
			        Standard: this.form.Standard,
			        FactoryNo: this.form.FactoryNo,
			        RatedFlow: this.form.RatedFlow,
			        RatedHead: this.form.RatedHead,
			        RatedSpeed: this.form.RatedSpeed,
			        RatedCurrent: this.form.RatedCurrent,
			        RatedEfficiency: this.form.RatedEfficiency,
			        InletDiameter: this.form.InletDiameter,
			        OutletDiameter: this.form.OutletDiameter,
			        MotorEfficiency: this.form.MotorEfficiency,
			        SampleTime: this.form.SampleTime,
			        Submitter: this.form.Submitter,
			    // 将选中的试验项目值转换为对应的名称字符串
			    TestItems: this.form.TestItems.map(value => {
			      const item = this.checkItems.find(item => item.value === value)
			      return item ? item.name : ''
			    }).join(',') // 转换为逗号分隔的字符串
			  }
			  
			  this.request({
			    url: '/SendSample/Submit',
			    method: 'POST',
			    data: submitData, // 使用处理后的数据
			    success: res => {
			          uni.showModal({
			            title: '提示',
			            content: '提交成功',
			            showCancel: false,
			            success: () => {
			              // 用户点击确定后执行重置操作
			              this.resetForm()
			              // 刷新记录列表
			              this.queryMyRecords()
			              // 切换到记录列表页面
			              this.current = 0
			            }
			          })
			        },
			        error: res => {
			          uni.showModal({
			            title: '错误',
			            content: res.msg || '提交失败',
			            showCancel: false
			          })
			        }
			  })
			},
			
			// 添加重置表单方法
			resetForm() {
			  this.form = {
			    SerialNo: '',          
			    Standard: '',          
			    FactoryNo: '',         
			    PumpModel: '',         
			    PumpName: '',          
			    RatedFlow: '',         
			    RatedHead: '',         
			    RatedSpeed: '',        
			    RatedCurrent: '',      
			    RatedEfficiency: '',   
			    InletDiameter: '',     
			    OutletDiameter: '',    
			    MotorEfficiency: '',   
			    SampleTime: new Date().toISOString().split('T')[0],
			    Submitter: this.userName,
			    TestItems: []
			  }
			  this.serialNo = ''
			  
			 // 强制刷新视图（关键步骤）
			  this.$forceUpdate()
			},
			
			
			// 查询我的送样记录
			queryMyRecords() {
			  //this.loading = true
			  this.request({
			    url: '/SendSample/GetMyRecords',
			    method: 'GET',
			    success: res => {
			      // 从 response.data 中获取数据
			      this.tableData = res.response.data || []
			      
			      // 格式化日期显示（去掉时间部分）
			      this.tableData = this.tableData.map(item => ({
			        ...item,
			        SampleTime: item.SampleTime ? item.SampleTime.split(' ')[0] : ''
			      }))
			      
			      //this.loading = false
			     
			    },
			    error: res => {
			      uni.showToast({
			        title: res.msg,
			        icon: 'none'
			      })
			      this.loading = false
			    }
			  })
			},
			 
			    // 查看测试结果
			    viewTestResult(item) {
			      // 检查是否有测试结果可查看
			     /* if (item.Status === '1') {
			        uni.showToast({
			          title: '测试尚未完成，暂无结果',
			          icon: 'none'
			        });
			        return;
			      }
			      */
			      // 跳转到结果详情页面
			      uni.navigateTo({
			        url: `/pages/Insp/sendSamples/detail?serialNo=${item.SerialNo}`
			      });
			    },
		}
	}
</script>

<style>
	.cu-form-group {
		background-color: #ffffff;
		padding: 1rpx 30rpx;
		display: flex;
		align-items: center;
		min-height: 100rpx;
		justify-content: space-between;
	}
	
	.cu-form-group .title {
		text-align: justify;
		padding-right: 30rpx;
		font-size: 30rpx;
		position: relative;
		height: 60rpx;
		line-height: 60rpx;
	}
	
	checkbox-group {
		display: flex;
		flex-wrap: wrap;
	}
	
	checkbox-group label {
		margin: 10rpx;
	}
	/* 添加固定底部按钮样式 */
	.submit-btn {
	  position: fixed;
	  bottom: 0;
	  left: 0;
	  right: 0;
	  padding: 20rpx;
	  background-color: #ffffff;
	  box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.1);
	}
	
	.submit-btn button {
	  width: 100%;
	  height: 80rpx;
	  line-height: 80rpx;
	  font-size: 32rpx;
	}
	
	/* 为了防止内容被底部按钮遮挡,给内容区添加底部间距 */
	.uni-padding-wrap {
	  padding-bottom: 120rpx;
	}
	
	/* 操作按钮容器样式 */
	    .action-button-wrapper {
	        display: flex;
	        flex-direction: row;
	        justify-content: center;
	        align-items: center;
	    }
	    
	    /* 查看结果按钮样式 */
	    .view-result-btn {
	        font-size: 24rpx;
	        height: 60rpx;
	        line-height: 60rpx;
	        background-color: #009598;
	        color: #ffffff;
	        padding: 0 20rpx;
	        border-radius: 6rpx;
	        margin: 0 10rpx;
	        min-width: 140rpx;  /* 设置最小宽度 */
	        white-space: nowrap; /* 防止文本换行 */
	        text-align: center;
	    }
</style>