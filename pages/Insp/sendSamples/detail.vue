<template>
  <view class="result-detail">
    <view class="page-header">
      <view class="header-title">测试结果详情</view>
    </view>

    <view class="loading-container" v-if="loading">
      <uni-load-more status="loading" :contentText="loadingText"></uni-load-more>
    </view>

    <view class="no-data" v-else-if="!detailData || detailData.length === 0">
      <text>暂无测试结果数据</text>
    </view>

    <view class="detail-list" v-else>
      <!-- 遍历所有测试项目结果 -->
      <view class="detail-card" v-for="(item, index) in detailData" :key="index">
        <view class="card-header">
          <text class="test-item">{{ item.TestItem }}</text>
          <text class="test-result" :class="getResultClass(item.Result)">
            {{ item.Result }}
          </text>
        </view>

        <view class="card-body">
          <view class="info-row">
            <text class="info-label">性能规范编号:</text>
            <text class="info-value">{{ item.SpecificationNo || '无' }}</text>
          </view>

          <view class="info-row">
            <text class="info-label">测试报告编号:</text>
            <text class="info-value">{{ item.TestReportNo || '无' }}</text>
          </view>

          <view class="info-row">
            <text class="info-label">测试日期:</text>
            <text class="info-value">{{ formatDate(item.TestDate) }}</text>
          </view>

          <view class="info-row">
            <text class="info-label">电泵型号:</text>
            <text class="info-value">{{ item.PumpModel || '无' }}</text>
          </view>

          <!-- 性能测量数据 -->
            <view class="section-title">性能测量数据</view>
            
            <view class="info-row" v-if="item.MeasuredRatedFlow !== null">
              <text class="info-label">实测规定扬程点流量:</text>
              <text class="info-value">{{ item.MeasuredRatedFlow }}m³/h</text>
            </view>

            <view class="info-row" v-if="item.MeasuredFullFlow !== null">
              <text class="info-label">实测全开流量:</text>
              <text class="info-value">{{ item.MeasuredFullFlow }}m³/h</text>
            </view>

            <view class="info-row" v-if="item.MeasuredRatedHead !== null">
              <text class="info-label">实测规定流量点扬程:</text>
              <text class="info-value">{{ item.MeasuredRatedHead }}m</text>
            </view>

            <view class="info-row" v-if="item.MeasuredMaxHead !== null">
              <text class="info-label">实测最高扬程:</text>
              <text class="info-value">{{ item.MeasuredMaxHead }}m</text>
            </view>

            <view class="info-row" v-if="item.MeasuredRatedEfficiency !== null">
              <text class="info-label">实测规定流量点电泵效率:</text>
              <text class="info-value">{{ item.MeasuredRatedEfficiency }}%</text>
            </view>

            <view class="info-row" v-if="item.MeasuredMaxInputPower !== null">
              <text class="info-label">实测最大输入功率:</text>
              <text class="info-value">{{ item.MeasuredMaxInputPower }}kW</text>
            </view>

          <!-- 其他测量数据 -->
            <view class="section-title">其他测量数据</view>
            
            <view class="info-row" v-if="item.TemperatureRise !== null">
              <text class="info-label">温升:</text>
              <text class="info-value">{{ item.TemperatureRise }}°C</text>
            </view>

            <view class="info-row" v-if="item.Noise !== null">
              <text class="info-label">噪音:</text>
              <text class="info-value">{{ item.Noise }}dB</text>
            </view>

            <view class="info-row" v-if="item.Vibration !== null">
              <text class="info-label">振动:</text>
              <text class="info-value">{{ item.Vibration }}mm/s</text>
            </view>
        
        </view>
      </view>
    </view>

  
  </view>
</template>

<script>
export default {
  data() {
    return {
      serialNo: '',
      detailData: [],
      loading: true,
      loadingText: {
        contentdown: '加载中...',
        contentrefresh: '加载中...',
        contentnomore: '没有更多数据了'
      }
    }
  },
  onLoad(options) {
    // 获取传递的序列号参数
    if (options.serialNo) {
      this.serialNo = options.serialNo;
      this.fetchTestResultDetails();
    } else {
      this.loading = false;
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
    }
  },
  methods: {
    // 获取测试结果详情
    fetchTestResultDetails() {
      this.loading = true;
      
      this.request({
        url: '/SendSampleDetail/GetTestResultDetails',
        method: 'GET',
        data: {
          serialNo: this.serialNo
        },
        success: res => {
          // 处理返回的数据
          this.detailData = res.response || [];
          this.loading = false;
        },
        error: res => {
          uni.showToast({
            title: res.msg || '获取测试结果失败',
            icon: 'none'
          });
          this.loading = false;
        }
      });
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '无';
      // 假设后端返回的格式为 yyyy-MM-dd HH:mm:ss 或 ISO 格式
      return dateStr.split('T')[0] || dateStr.split(' ')[0] || dateStr;
    },
    
    // 根据结果获取样式类
    getResultClass(result) {
      if (!result) return '';
      
      if (result.includes('合格') || result === '通过') {
        return 'result-pass';
      } else if (result.includes('不合格') || result === '不通过') {
        return 'result-fail';
      }
      return '';
    },
    
   
    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style>
.result-detail {
  padding-bottom: 120rpx;
}

.page-header {
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.loading-container {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
}

.no-data {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 30rpx;
}

.detail-list {
  padding: 20rpx;
}

.detail-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.test-item {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.test-result {
  font-size: 28rpx;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
}

.result-pass {
  background-color: rgba(0, 149, 148, 0.1);
  color: #009594;
}

.result-fail {
  background-color: rgba(255, 87, 71, 0.1);
  color: #ff5747;
}

.card-body {
  padding: 20rpx 30rpx;
}

.info-row {
  display: flex;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
}

.info-label {
  width: 320rpx;
  color: #666;
  font-size: 28rpx;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.data-section {
  margin-top: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  padding-bottom: 10rpx;
  border-bottom: 1px solid #eee;
}

.bottom-btns {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
}

.back-btn {
  width: 90%;
  background-color: #009598;
  color: #fff;
}
</style>