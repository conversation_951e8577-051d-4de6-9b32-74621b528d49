<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">工单号</view>
				<input v-model="woCode" @confirm="queryWo()" placeholder="请手动扫描工单号" :focus="focusZore" />
				<uni-icons @click="scan" class="uni-panel-icon uni-icon alin_x_center" type="scan" color="#8f8f94" size="25" />
			</view>
			<view class="cu-form-group">
				<view>
					<view class="uni-ellipsis">车间：{{ form.WorkshopName }}</view>
					<view class="uni-ellipsis">产线：{{ form.LineName }}</view>
					<view class="uni-ellipsis">料号：{{ form.ItemCode }}</view>
					<view class="uni-ellipsis">料品：{{ form.ItemName }}</view>
					<view class="uni-ellipsis">规格型号：{{ form.ItemSpec }}</view>
					<view class="uni-ellipsis">工单数量：{{ form.Qty }}</view>
					<view class="uni-ellipsis">工单不合格数：{{ form.BadQty }}</view>
				</view>
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">外协供应商</view>
				<input v-model="form.SupplierName" placeholder="请选择" type="text" @confirm="sup.fetchSuggestions" />
				<picker mode="selector" :range="sup.option" range-key="value" @change="sup.onChange" filterable>
					<view class="picker">选择</view>
				</picker>
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">加工人</view>
				<input v-model="form.ProcessorName" placeholder="请选择" type="text" @confirm="user.method" />
				<picker mode="selector" :range="user.option" range-key="value" @change="user.onChange" filterable>
					<view class="picker">选择</view>
				</picker>
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">赔偿比例</view>
				<input v-model="form.Ratio" min="0" placeholder="请输入" type="number" />
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">赔偿金额</view>
				<input v-model="form.Amount" min="0" placeholder="请输入" type="number" />
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">处置方式</view>
				<input v-model="form.Dispose" min="0" placeholder="请输入" type="number" />
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">合同号</view>
				<input v-model="form.ContractNo" placeholder="请输入" type="text" />
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">客户名称</view>
				<input v-model="form.CustomerName" placeholder="请输入" type="text" />
			</view>

			<view class="cu-form-group">
				<view class="title" style="width: max-content">不合格原因</view>
				<textarea v-model="form.BadReason" placeholder="请输入不合格原因" />
			</view>

			<view v-for="(detail, index) in form.Details" :key="index">
				<uni-swipe-action-item
					:right-options="[
						{
							index: index,
							text: '删除',
							style: {
								backgroundColor: '#dd524d'
							}
						}
					]"
					:left-options="[
						{
							index: index,
							text: '删除',
							style: {
								backgroundColor: '#dd524d'
							}
						}
					]"
					@click="onClick"
				>
					<view style="font-size: 18px">第{{ index + 1 }}项{{ index == 0 ? '(左右滑动此处删除)' : '' }}</view>
				</uni-swipe-action-item>
				<view class="cu-form-group">
					<view style="width: 50%">
						<view class="title" style="width: max-content">类型</view>
						<uni-data-select
							style="background-color: #ffffff"
							v-model="detail.BadType"
							:localdata="[
								{ value: '金工', text: '金工' },
								{ value: '铸件', text: '铸件' },
								{ value: '铸件', text: '铸件' },
								{ value: '电机', text: '电机' },
								{ value: '冲焊', text: '金工' },
								{ value: '组装试气', text: '组装试气' },
								{ value: '组装空载', text: '组装空载' },
								{ value: '组装外观', text: '组装外观' },
								{ value: '井泵电机空载', text: '井泵电机空载' },
								{ value: '不锈钢组装', text: '不锈钢组装' },
								{ value: '一次性测试', text: '一次性测试' }
							]"
							@change="
								(val) => {
									detail.BadType = '';
									detail.WasteQty = null;
									detail.ProdWasteQty = null;
									detail.BadQty = null;
								}
							"
						></uni-data-select>
					</view>

					<view style="width: 50%">
						<view class="title" style="width: max-content">项目</view>
						<uni-data-select
							style="background-color: #ffffff"
							v-model="detail.BadProject"
							:localdata="badMapArr[detail.BadType == null || detail.BadType == '' ? 'default' : detail.BadType]"
						></uni-data-select>
					</view>
				</view>
				<view v-if="['冲件', '金工', '铸件'].includes(detail.BadType == null || detail.BadType == '' ? 'default' : detail.BadType)" class="cu-form-group">
					<view class="title" style="width: max-content">废料</view>
					<input
						v-model="detail.WasteQty"
						placeholder="请输入"
						type="number"
						@blur="
							() => {
								detail.BadQty = detail.WasteQty - -detail.ProdWasteQty;
								setFromQty();
							}
						"
						@confirm="
							() => {
								detail.BadQty = detail.WasteQty - -detail.ProdWasteQty;
								setFromQty();
							}
						"
					/>
				</view>
				<view v-if="['冲件', '金工', '铸件'].includes(detail.BadType == null || detail.BadType == '' ? 'default' : detail.BadType)" class="cu-form-group">
					<view class="title" style="width: max-content">工废</view>
					<input
						v-model="detail.ProdWasteQty"
						placeholder="请输入"
						type="number"
						@blur="
							() => {
								detail.BadQty = detail.ProdWasteQty - -detail.WasteQty;
								setFromQty();
							}
						"
						@confirm="
							() => {
								detail.BadQty = detail.ProdWasteQty - -detail.WasteQty;
								setFromQty();
							}
						"
					/>
				</view>
				<view v-if="!['冲件', '金工', '铸件'].includes(detail.BadType == null || detail.BadType == '' ? 'default' : detail.BadType)" class="cu-form-group">
					<view class="title" style="width: max-content">不合格总数</view>
					<input
						v-model="detail.BadQty"
						placeholder="请输入"
						type="number"
						@blur="
							() => {
								detail.WasteQty = null;
								detail.ProdWasteQty = null;
								setFromQty();
							}
						"
						@confirm="
							() => {
								detail.WasteQty = null;
								detail.ProdWasteQty = null;
								setFromQty();
							}
						"
					/>
				</view>
			</view>

			<view style="height: 100px">
				<!-- 底部空隙 -->
			</view>
			<!-- 		<view class="inputArea">
				<view class="login-button" @click="add">添加不合格项</view>
			</view> -->
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 15%; color: black" @click="cancel">取消</view>
				<view class="xj_button" style="width: 35%; background-color: #ffaa00" @click="add">项目增加</view>
				<view class="xj_button" style="width: 50%; background-color: #009598" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			woCode: '',
			badMapArr: {
				default: [],
				金工: [
					{
						text: '车加工不良',
						value: '车加工不良'
					},
					{
						text: '磨加工不良',
						value: '磨加工不良'
					},
					{
						text: '热套不良',
						value: '热套不良'
					},
					{
						text: '轴料不良',
						value: '轴料不良'
					},
					{
						text: '压不进',
						value: '压不进'
					},
					{
						text: '料弯',
						value: '料弯'
					},
					{
						text: '尺寸超差',
						value: '尺寸超差'
					},
					{
						text: '跳动大',
						value: '跳动大'
					},
					{
						text: '乱牙',
						value: '乱牙'
					},
					{
						text: '转子溜轴',
						value: '转子溜轴'
					},
					{
						text: '撞砂轮',
						value: '撞砂轮'
					},
					{
						text: '纵车比例不对',
						value: '纵车比例不对'
					},
					{
						text: '调试机床',
						value: '调试机床'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '钻加工不良',
						value: '钻加工不良'
					},
					{
						text: '轴压断',
						value: '轴压断'
					},
					{
						text: '转子压不到位',
						value: '转子压不到位'
					},
					{
						text: '盖板不良',
						value: '盖板不良'
					},
					{
						text: '漏气',
						value: '漏气'
					},
					{
						text: '珩磨泵盖',
						value: '珩磨泵盖'
					},
					{
						text: '珩磨屏蔽套',
						value: '珩磨屏蔽套'
					},
					{
						text: '过切',
						value: '过切'
					},
					{
						text: '压轴承',
						value: '压轴承'
					},
					{
						text: '磨泵盖',
						value: '磨泵盖'
					},
					{
						text: '磨屏蔽套',
						value: '磨屏蔽套'
					},
					{
						text: '轴压不到位',
						value: '轴压不到位'
					},
					{
						text: '膨胀套压不进',
						value: '膨胀套压不进'
					},
					{
						text: '屏蔽套磕碰',
						value: '屏蔽套磕碰'
					},
					{
						text: '屏蔽套变形',
						value: '屏蔽套变形'
					},
					{
						text: '泵盖内孔车大',
						value: '泵盖内孔车大'
					},
					{
						text: '与屏蔽套配合孔大',
						value: '与屏蔽套配合孔大'
					},
					{
						text: '转子坯内压裂',
						value: '转子坯内压裂'
					},
					{
						text: '转子坯内压弯',
						value: '转子坯内压弯'
					},
					{
						text: '泵轴有裂纹',
						value: '泵轴有裂纹'
					}
				],
				铸件: [
					{
						text: '拉孔拉偏',
						value: '拉孔拉偏'
					},
					{
						text: '拉孔尺寸不良 ',
						value: '拉孔尺寸不良 '
					},
					{
						text: '粗车尺寸不良 ',
						value: '粗车尺寸不良 '
					},
					{
						text: '压转子尺寸不良',
						value: '压转子尺寸不良'
					},
					{
						text: '压转子断轴 ',
						value: '压转子断轴 '
					},
					{
						text: '压转子压坏',
						value: '压转子压坏'
					},
					{
						text: '半精车尺寸不良',
						value: '半精车尺寸不良'
					},
					{
						text: '半精车断轴',
						value: '半精车断轴'
					},
					{
						text: '压盖板压坏',
						value: '压盖板压坏'
					},
					{
						text: '压盖板尺寸不良',
						value: '压盖板尺寸不良'
					},
					{
						text: '精车尺寸不良',
						value: '精车尺寸不良'
					},
					{
						text: '精车断轴',
						value: '精车断轴'
					},
					{
						text: '压套筒褶皱',
						value: '压套筒褶皱'
					},
					{
						text: '轴漏气',
						value: '轴漏气'
					},
					{
						text: '转子盖板尺寸不良',
						value: '转子盖板尺寸不良'
					},
					{
						text: '转子盖板外观不良',
						value: '转子盖板外观不良'
					},
					{
						text: '转子套筒尺寸不良',
						value: '转子套筒尺寸不良'
					},
					{
						text: '转子套筒外观不良',
						value: '转子套筒外观不良'
					},
					{
						text: '转子焊接不良',
						value: '转子焊接不良'
					},
					{
						text: '轴组件不良',
						value: '轴组件不良'
					},
					{
						text: '轴套不良',
						value: '轴套不良'
					},
					{
						text: '轴不良',
						value: '轴不良'
					},
					{
						text: '压装尺寸不良',
						value: '压装尺寸不良'
					},
					{
						text: '珩磨尺寸不良',
						value: '珩磨尺寸不良'
					},
					{
						text: '漏珩磨',
						value: '漏珩磨'
					},
					{
						text: '磕碰',
						value: '磕碰'
					},
					{
						text: '压装损坏',
						value: '压装损坏'
					},
					{
						text: '来料尺寸不良',
						value: '来料尺寸不良'
					},
					{
						text: '来料变形',
						value: '来料变形'
					},
					{
						text: '来料加工不到位',
						value: '来料加工不到位'
					},
					{
						text: '来料开裂',
						value: '来料开裂'
					},
					{
						text: '来料混料',
						value: '来料混料'
					},
					{
						text: '来料结构缺损',
						value: '来料结构缺损'
					},
					{
						text: '径向轴承尺寸不良',
						value: '径向轴承尺寸不良'
					},
					{
						text: '来料外观不良',
						value: '来料外观不良'
					},
					{
						text: '金加工尺寸不良',
						value: '金加工尺寸不良'
					},
					{
						text: '焊接外观不良',
						value: '焊接外观不良'
					},
					{
						text: '调平衡NG',
						value: '调平衡NG'
					},
					{
						text: '过程混料',
						value: '过程混料'
					}
				],
				冲件: [
					{
						text: '定子叠高超差',
						value: '定子叠高超差'
					},
					{
						text: '定子孔小',
						value: '定子孔小'
					},
					{
						text: '定子裂开',
						value: '定子裂开'
					},
					{
						text: '定子毛刺',
						value: '定子毛刺'
					},
					{
						text: '定子内圆小',
						value: '定子内圆小'
					},
					{
						text: '定子片错位',
						value: '定子片错位'
					},
					{
						text: '定子散开',
						value: '定子散开'
					},
					{
						text: '少片错片',
						value: '少片错片'
					},
					{
						text: '生锈',
						value: '生锈'
					}
				],
				电机: [
					{
						text: '匝间绝缘',
						value: '匝间绝缘'
					},
					{
						text: '耐压试验',
						value: '耐压试验'
					},
					{
						text: '电阻',
						value: '电阻'
					},
					{
						text: '相间绝缘',
						value: '相间绝缘'
					},
					{
						text: '整形',
						value: '整形'
					},
					{
						text: '槽楔',
						value: '槽楔'
					},
					{
						text: '焊接',
						value: '焊接'
					},
					{
						text: '接头',
						value: '接头'
					},
					{
						text: '接线错误',
						value: '接线错误'
					},
					{
						text: '内圈断线',
						value: '内圈断线'
					},
					{
						text: '其它',
						value: '其它'
					},
					{
						text: '不通电',
						value: '不通电'
					},
					{
						text: '搭线',
						value: '搭线'
					},
					{
						text: '击穿',
						value: '击穿'
					},
					{
						text: '飞线',
						value: '飞线'
					},
					{
						text: '漏铜',
						value: '漏铜'
					},
					{
						text: '夹线',
						value: '夹线'
					},
					{
						text: '外观注塑不良',
						value: '外观注塑不良'
					},
					{
						text: '手工焊断线',
						value: '手工焊断线'
					},
					{
						text: '自动焊断线',
						value: '自动焊断线'
					},
					{
						text: '不抽水',
						value: '不抽水'
					},
					{
						text: '电流小',
						value: '电流小'
					},
					{
						text: '无电流',
						value: '无电流'
					},
					{
						text: '电流大',
						value: '电流大'
					},
					{
						text: '无流量',
						value: '无流量'
					},
					{
						text: '烧机',
						value: '烧机'
					},
					{
						text: '断线',
						value: '断线'
					},
					{
						text: '外观缺料',
						value: '外观缺料'
					},
					{
						text: '挂线不良',
						value: '挂线不良'
					},
					{
						text: '绕线不良',
						value: '绕线不良'
					},
					{
						text: '异物',
						value: '异物'
					}
				],
				冲焊: [
					{
						text: '外观不行',
						value: '外观不行'
					},
					{
						text: '尺寸不对',
						value: '尺寸不对'
					},
					{
						text: '冲变形',
						value: '冲变形'
					},
					{
						text: '焊接不良',
						value: '焊接不良'
					},
					{
						text: '圆跳动达不到',
						value: '圆跳动达不到'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '车加工不良',
						value: '车加工不良'
					},
					{
						text: '调试报废',
						value: '调试报废'
					}
				],
				组装试气: [
					{
						text: '机械密封漏气',
						value: '机械密封漏气'
					},
					{
						text: '泵体漏气',
						value: '泵体漏气'
					},
					{
						text: '封环漏气',
						value: '封环漏气'
					},
					{
						text: '螺丝孔漏气',
						value: '螺丝孔漏气'
					},
					{
						text: '联接件漏气',
						value: '联接件漏气'
					},
					{
						text: '泵盖漏气',
						value: '泵盖漏气'
					},
					{
						text: '止口漏气',
						value: '止口漏气'
					},
					{
						text: '上/顶盖封环漏气',
						value: '上/顶盖封环漏气'
					},
					{
						text: '下/封环漏气',
						value: '下/封环漏气'
					},
					{
						text: 'HZ漏装配件',
						value: 'HZ漏装配件'
					},
					{
						text: '机筒漏气',
						value: '机筒漏气'
					},
					{
						text: '电机漏气',
						value: '电机漏气'
					},
					{
						text: '接线盒漏气',
						value: '接线盒漏气'
					},
					{
						text: '底盖漏气',
						value: '底盖漏气'
					},
					{
						text: '插针漏气',
						value: '插针漏气'
					},
					{
						text: '电缆线灌胶处漏气',
						value: '电缆线灌胶处漏气'
					},
					{
						text: '屏蔽套漏气',
						value: '屏蔽套漏气'
					},
					{
						text: '流量开关漏气',
						value: '流量开关漏气'
					},
					{
						text: 'HZ螺丝未打紧',
						value: 'HZ螺丝未打紧'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '油室O型圈漏气',
						value: '油室O型圈漏气'
					},
					{
						text: '插针O型圈漏气',
						value: '插针O型圈漏气'
					},
					{
						text: '放气旋塞漏气',
						value: '放气旋塞漏气'
					},
					{
						text: '放气旋塞无O型圈',
						value: '放气旋塞无O型圈'
					},
					{
						text: '排气阀漏气',
						value: '排气阀漏气'
					},
					{
						text: '浮球开关漏气',
						value: '浮球开关漏气'
					},
					{
						text: '橡胶垫圈漏气',
						value: '橡胶垫圈漏气'
					},
					{
						text: '机壳螺丝孔漏气',
						value: '机壳螺丝孔漏气'
					},
					{
						text: '不启动',
						value: '不启动'
					},
					{
						text: '顶盖漏气',
						value: '顶盖漏气'
					},
					{
						text: '顶盖螺丝孔漏气',
						value: '顶盖螺丝孔漏气'
					},
					{
						text: '电缆线漏气',
						value: '电缆线漏气'
					},
					{
						text: '二次不启动',
						value: '二次不启动'
					},
					{
						text: '批量问题',
						value: '批量问题'
					},
					{
						text: '功率异常',
						value: '功率异常'
					},
					{
						text: '漏水',
						value: '漏水'
					},
					{
						text: '转子漏气',
						value: '转子漏气'
					}
				],
				组装空载: [
					{
						text: '叶轮摩擦',
						value: '叶轮摩擦'
					},
					{
						text: '耐压击穿',
						value: '耐压击穿'
					},
					{
						text: '堵转',
						value: '堵转'
					},
					{
						text: '绝缘电阻小',
						value: '绝缘电阻小'
					},
					{
						text: '不通电',
						value: '不通电'
					},
					{
						text: '低压不启动',
						value: '低压不启动'
					},
					{
						text: '启动异常',
						value: '启动异常'
					},
					{
						text: '风叶摩擦',
						value: '风叶摩擦'
					},
					{
						text: '泄漏',
						value: '泄漏'
					},
					{
						text: '前轴承异响',
						value: '前轴承异响'
					},
					{
						text: '流道堵塞',
						value: '流道堵塞'
					},
					{
						text: '接线错误',
						value: '接线错误'
					},
					{
						text: '卡死',
						value: '卡死'
					},
					{
						text: '空载功率大',
						value: '空载功率大'
					},
					{
						text: '空载功率小',
						value: '空载功率小'
					},
					{
						text: '空载电流大',
						value: '空载电流大'
					},
					{
						text: '空载电流小',
						value: '空载电流小'
					},
					{
						text: '直流电阻不良',
						value: '直流电阻不良'
					},
					{
						text: '电机异常',
						value: '电机异常'
					},
					{
						text: '电缆线灌胶不良',
						value: '电缆线灌胶不良'
					},
					{
						text: '后轴承异响',
						value: '后轴承异响'
					},
					{
						text: '电容用错',
						value: '电容用错'
					},
					{
						text: '定转子摩擦',
						value: '定转子摩擦'
					},
					{
						text: '反转',
						value: '反转'
					},
					{
						text: '漏油',
						value: '漏油'
					},
					{
						text: '缺相',
						value: '缺相'
					},
					{
						text: '叶轮松动',
						value: '叶轮松动'
					},
					{
						text: '噪音大',
						value: '噪音大'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '接地电阻大',
						value: '接地电阻大'
					},
					{
						text: '信号无反馈',
						value: '信号无反馈'
					},
					{
						text: '震动不良',
						value: '震动不良'
					},
					{
						text: '面传感器漏气',
						value: '面传感器漏气'
					},
					{
						text: '传感器漏水',
						value: '传感器漏水'
					},
					{
						text: '面板显示E1故障',
						value: '面板显示E1故障'
					},
					{
						text: '面板显示E2故障',
						value: '面板显示E2故障'
					},
					{
						text: '面板显示E3故障',
						value: '面板显示E3故障'
					},
					{
						text: '面板显示E4故障',
						value: '面板显示E4故障'
					},
					{
						text: '面板显示E5故障',
						value: '面板显示E5故障'
					},
					{
						text: '面板显示E6故障',
						value: '面板显示E6故障'
					},
					{
						text: '绝缘电阻大',
						value: '绝缘电阻大'
					},
					{
						text: '通讯故障',
						value: '通讯故障'
					},
					{
						text: '温度器故障',
						value: '温度器故障'
					},
					{
						text: '面板少键',
						value: '面板少键'
					},
					{
						text: '耐压不过',
						value: '耐压不过'
					},
					{
						text: '压力传感器故障',
						value: '压力传感器故障'
					},
					{
						text: '接线盒不通电',
						value: '接线盒不通电'
					},
					{
						text: '控制板不通电',
						value: '控制板不通电'
					},
					{
						text: '电机不通电',
						value: '电机不通电'
					},
					{
						text: '电缆线不通电',
						value: '电缆线不通电'
					},
					{
						text: '面板显示E7',
						value: '面板显示E7'
					},
					{
						text: '电机抖动',
						value: '电机抖动'
					},
					{
						text: '噪音(磨叶轮 )',
						value: '噪音(磨叶轮 )'
					},
					{
						text: '噪音(磨泵体 )',
						value: '噪音(磨泵体 )'
					},
					{
						text: '噪音(屏蔽套内有异物 ) ',
						value: '噪音(屏蔽套内有异物 ) '
					},
					{
						text: '转子有凹凸点   ',
						value: '转子有凹凸点   '
					},
					{
						text: '线路板绝缘不良',
						value: '线路板绝缘不良'
					},
					{
						text: '接地不良',
						value: '接地不良'
					},
					{
						text: '程序错误 ',
						value: '程序错误 '
					},
					{
						text: '信号线频率不良',
						value: '信号线频率不良'
					},
					{
						text: '时间显示0',
						value: '时间显示0'
					},
					{
						text: '噪音（磨口环) ',
						value: '噪音（磨口环) '
					},
					{
						text: '控制板E4(短路）',
						value: '控制板E4(短路）'
					},
					{
						text: '电缆线端子不良',
						value: '电缆线端子不良'
					},
					{
						text: '面板显示E7故障',
						value: '面板显示E7故障'
					},
					{
						text: '面板显示E8故障',
						value: '面板显示E8故障'
					},
					{
						text: '线路板欠压',
						value: '线路板欠压'
					},
					{
						text: '出风小',
						value: '出风小'
					},
					{
						text: '烧机',
						value: '烧机'
					},
					{
						text: '无功率',
						value: '无功率'
					},
					{
						text: '过流保护',
						value: '过流保护'
					},
					{
						text: '磨轴',
						value: '磨轴'
					},
					{
						text: '温度传感器连接漏气',
						value: '温度传感器连接漏气'
					},
					{
						text: '转子密封圈漏气',
						value: '转子密封圈漏气'
					},
					{
						text: '元器件脱落',
						value: '元器件脱落'
					},
					{
						text: '电阻为0',
						value: '电阻为0'
					}
				],
				组装外观: [
					{
						text: '喷涂不均匀',
						value: '喷涂不均匀'
					},
					{
						text: '流漆',
						value: '流漆'
					},
					{
						text: '油漆脱落',
						value: '油漆脱落'
					},
					{
						text: '铸件缺陷',
						value: '铸件缺陷'
					},
					{
						text: '铸件/表面磕碰',
						value: '铸件/表面磕碰'
					},
					{
						text: '网罩不良',
						value: '网罩不良'
					},
					{
						text: '铭牌不良',
						value: '铭牌不良'
					},
					{
						text: '装配间隙大',
						value: '装配间隙大'
					},
					{
						text: '包装箱错误',
						value: '包装箱错误'
					},
					{
						text: '标识不良',
						value: '标识不良'
					},
					{
						text: '色泽错误',
						value: '色泽错误'
					},
					{
						text: '泵体出水口堵死',
						value: '泵体出水口堵死'
					},
					{
						text: '泵体滑丝（错位）',
						value: '泵体滑丝（错位）'
					},
					{
						text: '泵体破裂',
						value: '泵体破裂'
					},
					{
						text: '部件变形',
						value: '部件变形'
					},
					{
						text: '错装、漏装配件',
						value: '错装、漏装配件'
					},
					{
						text: '打包不良',
						value: '打包不良'
					},
					{
						text: '电缆线不良',
						value: '电缆线不良'
					},
					{
						text: '封箱不整齐',
						value: '封箱不整齐'
					},
					{
						text: '机筒有油',
						value: '机筒有油'
					},
					{
						text: '漏电',
						value: '漏电'
					},
					{
						text: '螺丝未打紧',
						value: '螺丝未打紧'
					},
					{
						text: '压板变形',
						value: '压板变形'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '橡胶垫安装不到位',
						value: '橡胶垫安装不到位'
					},
					{
						text: '贴纸不良',
						value: '贴纸不良'
					},
					{
						text: '铜套未锁紧',
						value: '铜套未锁紧'
					},
					{
						text: '螺丝未点漆',
						value: '螺丝未点漆'
					},
					{
						text: '出水口生锈',
						value: '出水口生锈'
					},
					{
						text: '油漆磕碰',
						value: '油漆磕碰'
					},
					{
						text: '死角未喷到',
						value: '死角未喷到'
					},
					{
						text: '落灰',
						value: '落灰'
					},
					{
						text: '批量问题',
						value: '批量问题'
					},
					{
						text: '线路板灯亮不全',
						value: '线路板灯亮不全'
					},
					{
						text: '砂眼',
						value: '砂眼'
					},
					{
						text: '螺纹孔大',
						value: '螺纹孔大'
					},
					{
						text: '电机裂纹',
						value: '电机裂纹'
					},
					{
						text: '端子歪',
						value: '端子歪'
					},
					{
						text: '电机缺料',
						value: '电机缺料'
					},
					{
						text: '灯不亮',
						value: '灯不亮'
					},
					{
						text: '面板装反',
						value: '面板装反'
					},
					{
						text: '未钻孔',
						value: '未钻孔'
					},
					{
						text: '螺纹孔错位',
						value: '螺纹孔错位'
					},
					{
						text: '无螺纹孔',
						value: '无螺纹孔'
					},
					{
						text: '面板显示E3',
						value: '面板显示E3'
					},
					{
						text: '面板显示E1',
						value: '面板显示E1'
					},
					{
						text: '风叶不良',
						value: '风叶不良'
					},
					{
						text: '面板无按键',
						value: '面板无按键'
					},
					{
						text: '面板外观不良',
						value: '面板外观不良'
					},
					{
						text: '线路板插件不良',
						value: '线路板插件不良'
					},
					{
						text: '按键不灵',
						value: '按键不灵'
					},
					{
						text: '线路板接触不良',
						value: '线路板接触不良'
					},
					{
						text: '灯一直亮',
						value: '灯一直亮'
					},
					{
						text: 'WIFI异常',
						value: 'WIFI异常'
					},
					{
						text: '泄压冒汗',
						value: '泄压冒汗'
					},
					{
						text: '线路板缺针',
						value: '线路板缺针'
					},
					{
						text: '机壳裂纹',
						value: '机壳裂纹'
					},
					{
						text: '机壳砂眼',
						value: '机壳砂眼'
					},
					{
						text: '机壳缺料',
						value: '机壳缺料'
					},
					{
						text: '机壳无螺纹',
						value: '机壳无螺纹'
					},
					{
						text: '泵体砂眼',
						value: '泵体砂眼'
					},
					{
						text: '泵体无螺纹',
						value: '泵体无螺纹'
					},
					{
						text: '泵体进出水口螺纹缺料',
						value: '泵体进出水口螺纹缺料'
					},
					{
						text: '多料/缺料',
						value: '多料/缺料'
					},
					{
						text: '泵体堵孔',
						value: '泵体堵孔'
					},
					{
						text: '泵体止口不良',
						value: '泵体止口不良'
					},
					{
						text: '泵体无口环/口环变形',
						value: '泵体无口环/口环变形'
					},
					{
						text: '盖板不到位',
						value: '盖板不到位'
					},
					{
						text: '出水口偏差',
						value: '出水口偏差'
					}
				],
				井泵电机空载: [
					{
						text: '异响',
						value: '异响'
					},
					{
						text: '后轴承响',
						value: '后轴承响'
					},
					{
						text: '空载电流大',
						value: '空载电流大'
					},
					{
						text: '空载电流小',
						value: '空载电流小'
					},
					{
						text: '空载功率大',
						value: '空载功率大'
					},
					{
						text: '空载功率小',
						value: '空载功率小'
					},
					{
						text: '绝缘电阻不良',
						value: '绝缘电阻不良'
					},
					{
						text: '耐压击穿',
						value: '耐压击穿'
					},
					{
						text: '堵转',
						value: '堵转'
					},
					{
						text: '接线错误',
						value: '接线错误'
					},
					{
						text: '接线端脱离',
						value: '接线端脱离'
					},
					{
						text: '线接触不良',
						value: '线接触不良'
					},
					{
						text: '不通电',
						value: '不通电'
					},
					{
						text: '启动异常',
						value: '启动异常'
					},
					{
						text: '电缆线不通电',
						value: '电缆线不通电'
					},
					{
						text: '低压不启动',
						value: '低压不启动'
					},
					{
						text: '缺相',
						value: '缺相'
					},
					{
						text: '定转子摩擦',
						value: '定转子摩擦'
					},
					{
						text: '转速低',
						value: '转速低'
					},
					{
						text: '绝缘漏电',
						value: '绝缘漏电'
					},
					{
						text: '直流电阻不良',
						value: '直流电阻不良'
					},
					{
						text: '吸机',
						value: '吸机'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '批量问题',
						value: '批量问题'
					}
				],
				不锈钢组装: [
					{
						text: '电机座漏水',
						value: '电机座漏水'
					},
					{
						text: '泵座漏水',
						value: '泵座漏水'
					},
					{
						text: '其他电机异常',
						value: '其他电机异常'
					},
					{
						text: '电机轴承响',
						value: '电机轴承响'
					},
					{
						text: '电机噪音',
						value: '电机噪音'
					},
					{
						text: '机械密封漏水',
						value: '机械密封漏水'
					},
					{
						text: '铸件有沙眼',
						value: '铸件有沙眼'
					},
					{
						text: '泵噪音',
						value: '泵噪音'
					},
					{
						text: '泵体O型圈失效',
						value: '泵体O型圈失效'
					},
					{
						text: '放气阀漏水',
						value: '放气阀漏水'
					},
					{
						text: '电机座O形圈槽漏水',
						value: '电机座O形圈槽漏水'
					},
					{
						text: '泵座O形圈槽漏水',
						value: '泵座O形圈槽漏水'
					},
					{
						text: '超功率',
						value: '超功率'
					},
					{
						text: '差扬程',
						value: '差扬程'
					},
					{
						text: '泵体漏水',
						value: '泵体漏水'
					},
					{
						text: '抱箍漏水',
						value: '抱箍漏水'
					},
					{
						text: '其他',
						value: '其他'
					},
					{
						text: '放水阀漏气',
						value: '放水阀漏气'
					},
					{
						text: '青稞纸密封漏水',
						value: '青稞纸密封漏水'
					},
					{
						text: '变频器失效',
						value: '变频器失效'
					}
				],
				一次性测试: [
					{
						text: '出水口偏差',
						value: '出水口偏差'
					},
					{
						text: '扬程效率不合格',
						value: '扬程效率不合格'
					},
					{
						text: '扬程不合格',
						value: '扬程不合格'
					},
					{
						text: '流量不合格',
						value: '流量不合格'
					},
					{
						text: '温升不合格',
						value: '温升不合格'
					},
					{
						text: '效率不合格',
						value: '效率不合格'
					},
					{
						text: '最大流量不合格',
						value: '最大流量不合格'
					},
					{
						text: '最高扬程不合格',
						value: '最高扬程不合格'
					},
					{
						text: '低压温升不合格',
						value: '低压温升不合格'
					},
					{
						text: '额定扬程不合格',
						value: '额定扬程不合格'
					},
					{
						text: '额定扬程效率不合格',
						value: '额定扬程效率不合格'
					},
					{
						text: 'HZ不启动',
						value: 'HZ不启动'
					},
					{
						text: '额定最高扬程不合格',
						value: '额定最高扬程不合格'
					},
					{
						text: '额定扬程效率最高扬程不合格',
						value: '额定扬程效率最高扬程不合格'
					},
					{
						text: '最大流量额定扬程不合格',
						value: '最大流量额定扬程不合格'
					},
					{
						text: 'HZ噪音',
						value: 'HZ噪音'
					},
					{
						text: 'HZ功率异常',
						value: 'HZ功率异常'
					},
					{
						text: 'HZ半流量扬程不良',
						value: 'HZ半流量扬程不良'
					},
					{
						text: 'HZ最大扬程不良',
						value: 'HZ最大扬程不良'
					},
					{
						text: 'HZ二次不启动',
						value: 'HZ二次不启动'
					},
					{
						text: 'HZ装配不良',
						value: 'HZ装配不良'
					},
					{
						text: 'HZ人为损坏',
						value: 'HZ人为损坏'
					},
					{
						text: 'HZ线序错误',
						value: 'HZ线序错误'
					},
					{
						text: 'HZ驱动板不良',
						value: 'HZ驱动板不良'
					},
					{
						text: 'HZ转子充磁不合格',
						value: 'HZ转子充磁不合格'
					},
					{
						text: 'HZ电机缺相',
						value: 'HZ电机缺相'
					},
					{
						text: 'HZ不启动',
						value: 'HZ不启动'
					},
					{
						text: 'HZ功率不合格',
						value: 'HZ功率不合格'
					},
					{
						text: 'HZ泄露',
						value: 'HZ泄露'
					},
					{
						text: 'HZ耐压不合格',
						value: 'HZ耐压不合格'
					},
					{
						text: 'HZ绝缘不合格',
						value: 'HZ绝缘不合格'
					},
					{
						text: 'HZ接地不合格',
						value: 'HZ接地不合格'
					},
					{
						text: 'HZ叶轮卡擦',
						value: 'HZ叶轮卡擦'
					},
					{
						text: 'HZ噪音大',
						value: 'HZ噪音大'
					},
					{
						text: 'HZ漏水',
						value: 'HZ漏水'
					},
					{
						text: 'HZ不停机',
						value: 'HZ不停机'
					},
					{
						text: 'HZ不启动',
						value: 'HZ不启动'
					},
					{
						text: 'HZ过流',
						value: 'HZ过流'
					},
					{
						text: 'HZ输入欠压',
						value: 'HZ输入欠压'
					},
					{
						text: 'HZ电机缺相',
						value: 'HZ电机缺相'
					},
					{
						text: 'HZ电机堵转',
						value: 'HZ电机堵转'
					},
					{
						text: 'HZ功率模块过温',
						value: 'HZ功率模块过温'
					},
					{
						text: 'HZ通信故障',
						value: 'HZ通信故障'
					},
					{
						text: 'HZ缺水保护',
						value: 'HZ缺水保护'
					},
					{
						text: 'HZ干转保护',
						value: 'HZ干转保护'
					},
					{
						text: 'HZ漏电',
						value: 'HZ漏电'
					},
					{
						text: 'HZ压力传感器故障',
						value: 'HZ压力传感器故障'
					},
					{
						text: 'HZ未启动',
						value: 'HZ未启动'
					},
					{
						text: 'HZ功率不达标',
						value: 'HZ功率不达标'
					},
					{
						text: 'HZ不二次重启',
						value: 'HZ不二次重启'
					},
					{
						text: 'HZ漏水',
						value: 'HZ漏水'
					},
					{
						text: 'HZ不停机',
						value: 'HZ不停机'
					}
				]
			},
			user: {
				option: [],
				method: () => {
					this.request({
						url: '/SysUser/GetUserPageInfo',
						method: 'GET',
						data: {
							page: 1,
							intPageSize: 1000,
							key: this.form.ProcessorName
						},
						success: (res) => {
							let tempArr = [];
							for (let i = 0; i < res.response.data.length; i++) {
								tempArr.push({
									value: res.response.data[i].UserName,
									text: res.response.data[i].Id
								});
							}
							this.user.option = tempArr;
						},
						error: (res) => {
							this.$error(res.msg);
						}
					});
				},
				onChange: (val) => {
					let idx = val.detail.value;
					let us = this.user.option[idx];
					this.form.Processor = us.text;
					this.form.ProcessorName = us.value;
				}
			},
			sup: {
				option: [],
				// 远程方法
				fetchSuggestions: () => {
					this.request({
						url: '/Supplier/Get',
						method: 'GET',
						data: {
							page: 1,
							intPageSize: 1000,
							name: this.form.SupplierName
						},
						success: (res) => {
							let tempArr = [];
							for (let i = 0; i < res.response.data.length; i++) {
								tempArr.push({
									value: res.response.data[i].SupplierName,
									text: res.response.data[i].Id
								});
							}
							this.sup.option = tempArr;
						},
						error: (res) => {
							this.$error(res.msg);
						}
					});
				},
				onChange: (val) => {
					let idx = val.detail.value;
					let supplier = this.sup.option[idx];
					this.form.OutSupplier = supplier.text;
					this.form.SupplierName = supplier.value;
				}
			},
			form: {
				Id: 0,
				Code: '',
				Org: null,
				Date: null,
				WorkShop: null,
				Line: null,
				Pipeline: null,
				Item: null,
				DocNo: '',
				Qty: null,
				WasteQty: null,
				ProdWasteQty: null,
				BadQty: null,
				Dispose: null,
				BadProject: null,
				Processor: null,
				OutSupplier: null,
				BadReason: '',
				Ratio: null,
				Amount: null,
				Status: 0,
				CreateUserId: null,
				CreateTime: null,
				UpdateUserId: null,
				UpdateTime: null,
				CreateUserName: '',
				UdpateUserName: '',
				ProcessorName: '',
				SupplierName: '',
				SupplierCode: '',
				ItemName: '',
				ItemCode: '',
				ItemSpec: '',
				Details: [],
				WorkshopName: '',
				ContractNo: '',
				CustomerName: '',
				LineName: ''
			},
			focusZore: true
		};
	},
	created() {
		this.sup.fetchSuggestions();
		this.user.method();
	},
	methods: {
		setFromQty() {
			this.form.BadQty = this.form.Details.reduce((s, c) => s - -c.BadQty, 0);
		},
		add() {
			this.form.Details.push({
				BadType: '',
				BadProject: '',
				WasteQty: null,
				ProdWasteQty: null,
				BadQty: null,
				show: 'none'
			});
		},
		changeBlur() {
			this.focusZore = false;
		},
		scan() {
			uni.scanCode({
				success: (res) => {
					this.woCode = res.result;
					this.queryWo();
				},
				fail: () => {
					this.$toast('扫码失败');
				}
			});
		},
		queryWo() {
			if (!this.woCode) {
				this.$toast('未获取的工单号');
				return;
			}
			this.request({
				url: '/WOMain/Get',
				method: 'GET',
				data: {
					wo: this.woCode,
					inPageSize: 10,
					inPageIndex: 1
				},
				success: (res) => {
					if (res.response.dataCount == 1 && this.woCode == res.response.data[0].Wo) {
						let wo = res.response.data[0];
						this.form.WorkshopName = wo.DepartmentName;
						this.form.Workshop = wo.DepartmentId;
						this.form.LineName = wo.LineName;
						this.form.Line = wo.LineId;
						this.form.Item = wo.MaterialId;
						this.form.ItemCode = wo.MaterialCode;
						this.form.ItemName = wo.MaterialName;
						this.form.ItemSpec = wo.Specification;
						this.form.Qty = wo.PlanQty;
						this.form.DocNo = wo.Wo;
						this.changeBlur();
						this.add();
					} else {
						this.woCode == '';
						this.$error('无此工单');
					}
				},
				error: (res) => {
					this.$error(res.msg);
					this.cancel();
				}
			});
		},
		cancel() {
			this.woCode = '';
			this.form = {
				Id: 0,
				Code: '',
				Org: null,
				Date: null,
				WorkShop: null,
				Line: null,
				Pipeline: null,
				Item: null,
				DocNo: '',
				Qty: null,
				WasteQty: null,
				ProdWasteQty: null,
				BadQty: null,
				Dispose: null,
				BadProject: null,
				Processor: null,
				OutSupplier: null,
				BadReason: '',
				Ratio: null,
				Amount: null,
				Status: 0,
				CreateUserId: null,
				CreateTime: null,
				UpdateUserId: null,
				UpdateTime: null,
				CreateUserName: '',
				UdpateUserName: '',
				ProcessorName: '',
				SupplierName: '',
				SupplierCode: '',
				ItemName: '',
				ItemCode: '',
				ItemSpec: '',
				Details: [],
				WorkshopName: '',
				ContractNo: '',
				CustomerName: '',
				LineName: ''
			};
			this.sup.fetchSuggestions();
			this.user.method();
		},
		submit() {
			if (this.form.DocNo == null || this.form.DocNo == '') {
				this.$error('工单未输入');
				return;
			}
			if (this.form.Details == null || this.form.Details.length == 0) {
				this.$error('必需至少添加一项项目');
			}

			for (var i = 0; i < this.form.Details.length; i++) {
				if (this.form.Details[i].BadType == null || this.form.Details[i].BadType == '') {
					this.$error('第' + (i + 1) + '项必选择类型');
					return;
				}
				if (
					this.form.Details[i].BadProject != null &&
					this.form.Details[i].BadProject != '' &&
					this.form.Details[i].ProdWasteQty == 0 &&
					this.form.Details[i].WasteQty == 0 &&
					this.form.Details[i].BadQty == 0
				) {
					this.$error('第' + (i + 1) + '项目不允许选择不合格项目后数量留空');
					return;
				}
			}

			this.request({
				url: '/CodeRulesMain/GetCodeRule',
				method: 'GET',
				data: {
					ruleCode: 'XJ'
				},
				success: (res) => {
					this.form.Code = res.response;
					this.post();
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		post() {
			this.form.Date = new Date();
			this.request({
				url: '/workshopInspection/post?type=submit',
				method: 'post',
				data: this.form,
				success: (res) => {
					this.$success('添加成功');
					this.cancel();
					this.focusZore = true;
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		onClick(e) {
			if (e.content.text == '删除') {
				this.form.Details.splice(e.content.index, 1);
				this.setFromQty();
			}
		}
	}
};
</script>

<style>
.login-button {
	background: #009598;
	width: 40%;
	color: #ffffff;
	border-radius: 14upx;
	font-size: 18upx;
	text-align: center;
	padding: 10upx;
	margin-top: 10upx;
	margin-left: 25%;
}
</style>
