<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<radio-group @change="radioChange" class="margin-top">
				<view class="uni-list">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
							<view class="cu-form-group solid-bottom panel-full" >
								<view class="content">
									<view class="cu-form-data-15">生产工单: {{item[0]}}</view>
									<view class="cu-form-data-15">产品编码: {{item[1]}}</view>
									<view class="text-black cu-form-data-15">产品名称: {{item[2]}} </view>
								</view>
								<view>
									<radio :value="index + ''" :checked="item[0] === currentOrderNo"/>
								</view>
							</view>
					</view>
				</view>	
			</radio-group>
		</view>
	</view>
</template>

<script>
	export default {
		onLoad(e) {
			console.log(e);
			this.request({
				url : '/WOMain/Get',
				method:'GET',
				data:{
					wo: e.orderNo,
					startDate: e.startDate 
				},
				success: res=>{
					this.obj=res.data;
				},
					error: res =>{
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
			})
		},
		data() {
			return {
					currentOrderNo:'',
					obj:[]
				}

			},
		methods: {
			radioChange(evt) {
				var i=  parseInt(evt.detail.value);
				var orderNo= this.obj[i][0];
				var productNo= this.obj[i][1];
				var productName = this.obj[i][2]
				uni.setStorage({
					key:"orderInfo",
					data:{
						"orderNo":orderNo,
						"proName":productName,
						"productNo":productNo
					}
				})
				uni.navigateBack({
					animationDuration:1000
				})
			}
		}
	}
</script>

