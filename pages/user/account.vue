<template>
	<view style="padding-top: 40upx;">
		<scroll-view scroll-x class="bg-white nav">
			<view class="flex text-center">
				<view class="cu-item flex-sub" :class="index==TabCur?'text-orange cur':''" v-for="(item,index) in 1" :key="index" @tap="tabSelect" :data-id="index">
					当日任务
				</view>
				<view class="cu-item flex-sub" :class="index==TabCur?'text-orange ':''" v-for="(item,index) in 1" :key="index" @tap="tabSelect" :data-id="index">
					明日任务
				</view>
			</view>
		</scroll-view>
		<view class="cu-form-group margin-top">
			<view class="title">维修计划</view>
			<input placeholder="未完成任务5个" name="input"></input>
			<button class='cu-btn bg-red shadow'>延迟</button>
		</view>
		
		<view class="cu-form-group">
			<view class="title">保养计划</view>
			<input placeholder="未完成任务1个" name="input"></input>
			<button class='cu-btn bg-green shadow'>正常</button>
		</view>
		
		<view class="cu-form-group">
			<view class="title">点检计划</view>
			<input placeholder="未完成任务1个" name="input"></input>
			<button class='cu-btn bg-green shadow'>正常</button>
		</view>
		<view class="cu-form-group">
			<view class="title">归还计划</view>
			<input placeholder="未完成任务3个" name="input"></input>
			<button class='cu-btn bg-green shadow'>正常</button>
		</view>	
		<view class="cu-form-group">
			<view class="title">大修计划</view>
			<input placeholder="未完成任务3个" name="input"></input>
			<button class='cu-btn bg-green shadow'>正常</button>
		</view>	
		<view class="cu-form-group margin-top">
			<view class="title">质量稽核QC</view>
			<input placeholder="未完成任务2个" name="input"></input>
			<button class='cu-btn bg-red shadow'>延迟</button>
		</view>
		
		<view class="cu-form-group">
			<view class="title">质量稽核QM</view>
			<input placeholder="未完成任务1个" name="input"></input>
			<button class='cu-btn bg-green shadow'>正常</button>
		</view>
		
	
		<view style="padding: 0 10%;">
			<view style="margin-top: 80upx;">
				<button class="login-button" @click="logout">返回</button>
			</view>
		</view>
	</view>
	
</template>

<script>
	export default {
		data() {
			return {
				userinfo: '',
				TabCur: 0,
				scrollLeft: 0,
				index:[{index:1,name:"今日计划"},{index:2,name:"明日计划"}],
			}
		},
		onLoad() {
			console.log('logout页面onLoad');
			this.userinfo = uni.getStorageSync('userinfo');
		},
		methods: {
			logout() {
				uni.showLoading({
					title: 'waiting',
					mask: false
				});
				
				uni.removeStorageSync('userinfo');
						
				uni.hideLoading();
				uni.reLaunch({
					url: 'index'
				});
				
				
				
			}
		}
	}
</script>

<style>
.listcell{
	padding: 20upx 0;
	border-bottom: 1px solid #ccc;
}
.login-button {
		background: #E3162E;
		color: #FFFFFF;
		border-radius: 44upx;
	}
</style>
