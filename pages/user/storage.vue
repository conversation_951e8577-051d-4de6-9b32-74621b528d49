<template>
	<view style="padding-top: 40upx;" class="uni-padding-wrap uni-common-mt">
		<view class="inputArea" style="text-align: center;">
			<image class="logo-img" src="../../static/images/XJ_LOGO.png"></image>
		</view>
		<view class="inputArea">
			<uni-data-select style="background-color: #FFFFFF;" v-model="valueURL" :localdata="rangeURL"
				@change="changeURL" />
		</view>
		<view class="inputArea">
			<uni-data-select style="background-color: #FFFFFF;" v-model="value" :localdata="range" @change="change" />
		</view>
		<view class="inputArea">
			<input v-model="loginUsername" placeholder="请输入用户名" type="text" maxlength="11" class="inputClass" />
		</view>
		<view class="inputArea">
			<input v-model="loginPassword" placeholder="请输入登录密码" type="password" class="inputClass" />
		</view>
		<view class="inputArea">
			<checkbox-group @change="checkboxChange">
				<label>
					<checkbox class="uni-checkbox" :checked="rememberPsw" />
					<text class="margin-left-sm text-xl">记住我</text>
				</label>
			</checkbox-group>
		</view>
		<view style="padding: 0 10%;text-align: center;">
			<text style="color: red;">{{message}}</text>
		</view>
		<view class="inputArea">
			<view class="login-button" @click="goLogin">登 录</view>
		</view>
	</view>
</template>

<script>
	import md5 from '../../common/js/md5.min.js';
	export default {
		data() {
			return {
				loginUsername: 'admin',
				loginPassword: '123456',
				// loginUsername: '',
				// loginPassword: '',
				message: '',
				rememberPsw: false,
				loginFlag: 0,
				errorMsg: '',
				/* 扩展组件 下拉框 */
				range: [],
				value: '',
				/* 扩展组件 下拉框 */
				rangeURL: [{
					text: '正式环境',
					value: 'http://*************:9291/api'
				}, {
					text: '测试环境',
					value: 'http://*************:8091/api'
				}, {
					text: '云环境',
					value: 'http://**************:8091/api'
				}],
				valueURL: 'http://*************:9291/api',

			}
		},
		onLoad() {
			uni.setStorageSync('URL',this.valueURL);
		},	
		onShow() {
			
			let rememberPsw = uni.getStorageSync("rememberPsw");
			if (rememberPsw) {
				this.loginUsername = uni.getStorageSync("user");
				this.loginPassword = uni.getStorageSync("password");
				// 自动登录
				// this.goLogin();
			}
			this.getOrgData();
		},
		// 下拉刷新
		onPullDownRefresh() {
			// 开发版本的 服务请求地址初始化
			uni.removeStorageSync('URL')
			uni.stopPullDownRefresh()
		},
		methods: {
			// 存储 URl地址到本地缓存
			changeURL(e) {
				uni.setStorageSync('URL', e)
				this.getOrgData();
			},
			// 存储组织 ID 到本地缓存
			change(e) {
				uni.setStorageSync('orgId', e)
			},
			// 是否记住密码 改变事件监听
			checkboxChange() {
				this.rememberPsw = !this.rememberPsw
			},
			// 获取组织数据
			getOrgData() {
				this.range = []
				this.request({
					url: '/Org/GetOrgAllByTree',
					method: 'GET',
					success: res => {
						for (let i = 0; i < res.response.data.length; i++) {
							let temp = {
								text: res.response.data[i].label,
								value: res.response.data[i].id,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 获取后台指向路径
			getServerUrl() {
				this.range = []
				this.request({
					url: '/Org/GetOrgAllByTree',
					method: 'GET',
					success: res => {
						for (let i = 0; i < res.response.data.length; i++) {
							let temp = {
								text: res.response.data[i].label,
								value: res.response.data[i].id,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 登录
			goLogin() {
				if (!this.loginUsername) {
					this.message = "用户名不能为空。";
					return;
				}
				if (!this.loginPassword) {
					this.message = "密码不能为空。";
					return;
				}
				if (this.value == '') {
					this.message = "一定要选择组织。";
					return;
				}
				this.request({
					url: '/Login/GetToken',
					method: 'POST',
					data: {
						name: this.loginUsername,
						pwd: md5(this.loginPassword),
						orgId: this.value
					},
					success: res => {
						let that = this;
						if (res.success == false) {
							this.message = res.msg;
							return;
						}
						if (this.rememberPsw) {
							uni.setStorageSync("rememberPsw", this.rememberPsw);
						}
						uni.setStorageSync("user", this.loginUsername);
						uni.setStorageSync("password", this.loginPassword);
						uni.setStorageSync('token', res.response.token);
						this.loginFlag = 1;
						uni.reLaunch({
							url: '../prd/product'
						});
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			}
		}
	}
</script>

<style>
	.inputArea {
		padding: 30upx 10%;
	}

	.pages-mine-sign_in .uni-page-head-hd {
		display: none;
	}

	.uni-list-cell {
		justify-content: flex-start
	}

	.inputClass {
		border: 2px solid #ccc;
		border-radius: 10upx;
		outline: 0;
		width: 100%;
		padding: 16upx 30upx;
		background-color: #FFFFFF;
		height: 80upx;
	}

	.logo-img {
		width: 708upx;
		height: 232upx;
		/* border-radius: 150upx; */
	}

	.login-button {
		background: #009598;
		color: #FFFFFF;
		border-radius: 28upx;
		font-size: 36upx;
		text-align: center;
		padding: 20upx;
	}

	.uni-list-cell {
		justify-content: flex-start
	}
</style>
