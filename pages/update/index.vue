<template>
	<view class="content">
		<image class="logo" src="../../static/images/XJ_LOGO.png"></image>
		<view class="mainInfo">
			<text class="tip">{{ Mtip }}</text>
			<text class="title">\n{{ info }} | {{ Minfo }}</text>
		</view>
		<text class="line" />
		<view class="minorContent bottom_aera">
			<view v-if="startProgress && !currentIsLatest" class="smallTitle">
				<text>下载进度:{{ downloadProgress }}%</text>
				<progress :percent="downloadProgress" stroke-width="4" />
			</view>

			<view class="btn-row">
				<button v-if="!startProgress && !currentIsLatest" type="primary" @click="handleUpdate()">立即更新</button>
				<button v-if="currentIsLatest" :loading="buttonLoading" type="primary"
					@click="getLatest()">检查更新</button>
			</view>

		</view>
	</view>



</template>

<script>
	export default {
		components: {

		},
		data() {
			return {
				info: '正在加载', // 主标题显示名称 版本号
				Minfo: '未知', // 副标题显示版本类型
				Mtip: '', // 小提示标语
				updateInfo: '无', // 更新摘要
				updateDate: null, //更新时间

				latest: null, // 版本信息
				packgeSize: null, // 更新包大小
				packgePath: null, // 更新包的文件地址
				downloadTask: null, // 下载任务
				downloadProgress: 0, // 下载进度

				buttonLoading: false, // 加载 - 标记
				installed: false, // 是否执行了安装 - 标记
				startProgress: false, // 下载进行 - 标记
				currentIsLatest: true, // 当前版本就是最新版本 - 标记

				// androidUrl: 'http://*************:5100/XinjiePDA/MobileMes.apk',
				androidUrl: 'http://*************:5100/XinjiePDA/MobileMes.apk',
				iosUrl: null,
				appName: null
			}
		},
		onReady() {
			this.getLatest()
			const updated = uni.getStorageSync('updated')
			if (updated.packgePath) {
				this.packgePath = updated.packgePath
			}
		},
		// 如果用户下载后没有完成安装，却回到app，则执行这里
		onShow() {
			if (this.installed === true && this.packgePath) {
				this.installed = false
				this.haveDownloaded()
			}
		},
		// 用户关闭页面时检查是否存在下载任务
		onUnload() {
			if (this.downloadTask) {
				this.closeTask()
				this.showToast('更新被取消')
			}
		},
		// 下拉刷新更新
		onPullDownRefresh() {
			this.getLatest()
			uni.stopPullDownRefresh()
		},
		methods: {
			// 封装个Toast方便用
			showToast(text) {
				uni.showToast({
					title: text,
					duration: 3000,
					icon: 'none'
				})
			},
			installPackge() {
				// 安装更新
				plus.runtime.install(this.packgePath, {
					force: true
				})
				this.installed = true
				// 保存更新记录到stroage，方便下次启动app时删除安装包
				uni.setStorage({
					key: 'updated',
					data: {
						completed: true,
						packgePath: this.packgePath
					},
					success: (res) => {
						console.log('成功保存更新记录')
						//取消红点提示
						uni.hideTabBarRedDot({
							index: 3
						})
					}
				})
				// 判断是否为热更新（判断文件名中是否含有.wgt）
				if (this.packgePath.match(RegExp(/.wgt/))) {
					this.installed = false
					uni.showModal({
						title: '提示',
						content: '应用将重启以完成更新',
						showCancel: false,
						complete: () => {
							plus.runtime.restart()
						}
					})
				}
			},
			// 已经下载了更新包但是没有安装
			haveDownloaded() {
				uni.showModal({
					title: '更新尚未完成',
					content: '您已下载更新包，但是还没有完成安装，请问是否要继续安装更新包呢？',
					success: (res) => {
						if (res.confirm) {
							// 安装
							this.installPackge()
						} else if (res.cancel) {
							this.showToast('更新被取消')
						}
					}
				})
			},
			// 取得最新版本及其所有信息
			getLatest() {
				this.info = '正在加载' // 主标题显示版本号
				this.Minfo = '未知' // 副标题显示版本类型
				this.updateInfo = '无' // 更新摘要

				this.buttonLoading = true
				this.latest = null

				uni.showLoading({
					title: '检查中...',
					mask: true
				})

				this.request({
					url: '/Pda/GetSysParameter',
					method: 'GET',
					success: res => {
						this.latest = res.response.ParameterValue
						this.updateDate = res.response.UpdateTime
						// this.androidUrl = res.response.androidUrl
						this.setMinfo('Minfo')
						this.appName = res.response.Describe
						this.buttonLoading = false
						this.checkLatest()
					}
				});

				uni.hideLoading()
			},
			// 检查版本
			checkLatest() {
				if (!this.latest) {
					this.Mtip = '未找到发行版本'
				} else if (this.$current.id < this.latest) {
					this.currentIsLatest = false
					this.Mtip = '发现新版本'
					this.info = this.appName + ' ' + this.latest //名称 版本
					//显示红点提示
					uni.showTabBarRedDot({
						index: 3
					})
				} else {
					this.showToast('当前是最新版了')
					this.latest = this.$current.id
					this.currentIsLatest = true
					this.Mtip = '当前是最新版'
					this.info = this.appName + ' ' + this.latest //名称 版本
				}
			},
			// 根据英文版本类型选择中文版本类型 
			setMinfo(type) {
				switch (type) {
					case 'base':
						this.Minfo = '结构版'
						break
					case 'Minfo':
						this.Minfo = '内测版'
						break
					case 'beta':
						this.Minfo = '公测版'
						break
					case 'rc':
						this.Minfo = '终测版'
						break
					case 'release':
						this.Minfo = '正式版'
						break
					default:
						this.Minfo = '未知'
				}
			},
			// 关闭下载任务
			closeTask() {
				this.downloadTask.abort()
				this.downloadTask = null
				this.startProgress = false
			},
			// 开始下载任务
			createTask(downloadLink) {
				this.$current.id = this.latest
				this.$forceUpdate()
				console.log(this.$current.id)

				//判断是否已经存在任务
				console.log(downloadLink)
				if (this.packgePath) {
					this.haveDownloaded()
				} else {
					this.downloadProgress = 0
					this.startProgress = true
					// 创建下载任务对象
					this.downloadTask = uni.downloadFile({
						url: downloadLink,
						success: (res) => {
							console.log(res)
							if (res.statusCode === 200) {
								// 保存下载的安装包
								uni.saveFile({
									tempFilePath: res.tempFilePath,
									success: (res) => {
										this.packgePath = res.savedFilePath
										// 进行安装
										this.installPackge()
										// 任务完成，关闭下载任务
										this.closeTask()
									}
								})
							}
						}
					})
					// 进度条更新
					this.downloadTask.onProgressUpdate((res) => {
						this.downloadProgress = res.progress
					})
				}
			},
			handleUpdate() {
				console.log(this.androidUrl)
				if (this.androidUrl == '') {
					uni.showModal({
						title: '提示',
						content: 'APK 下载链接为空',
						showCancel: false,
						complete: () => {}
					})
				} else {
					this.createTask(this.androidUrl)
				}
			}
		}
	}
</script>

<style>
	.mainInfo {
		left: 40%;
		text-align: center;
	}

	.title {
		font-size: 36upx;
		color: #373737;
		font-weight: bold;
	}

	.infoContentTitle {
		display: flex;
		align-items: center;
		padding: 40upx 40upx;
		font-weight: bold;
	}

	.infoContent {
		display: flex;
		align-items: center;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.content {
		text-align: center;
	}

	.minorContent {
		width: 100%;
		padding: 0 50upx;
	}

	.process {
		margin-top: 200upx;
		margin-left: 30%;
	}

	.logo {
		height: 200upx;
		width: 500upx;
		margin-top: 50upx;
	}

	.title {
		font-size: 36upx;
		color: #373737;
		font-weight: bold;
	}

	.infoTitle {
		font-size: 32upx;
		color: #373737;
		padding-left: 15upx;
		font-weight: bold;
	}

	.tip {
		font-size: 28upx;
		color: #7E7E83;
		vertical-align: text-top;
	}

	.bottom_aera {
		position: absolute;
		bottom: 0;
		height: 12%;
	}

	.line {
		padding: 0 600upx;
		border-bottom: 2upx solid #D8D8D8;
	}

	.infoPic {
		height: 50upx;
		width: 50upx;
	}

	.infoContentTitle {
		display: flex;
		align-items: center;
		padding: 40upx 40upx;
	}

	.infoContent {
		display: flex;
		align-items: center;
	}

	.updateInfo {
		font-size: 28upx;
		color: #7E7E83;
		padding: 0 80upx;
	}

	.smallTitle {
		font-size: 26upx;
		font-weight: 500;
		padding: 20upx 0;
		line-height: 1.5;
		color: #888;
	}
</style>
