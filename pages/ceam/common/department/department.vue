<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<radio-group @change="radioChange" class="margin-top">
				<view class="uni-list">
					<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">车间编码: {{ item.DepartmentCode }}</view>
								<view class="cu-form-data-15">车间名称: {{ item.DepartmentName }}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.DepartmentCode === DepartmentCode" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
export default {
	onLoad(e) {
		let orgId = uni.getStorageSync('orgId');
		this.request({
			url: '/CraftEquipmentInfo/GetDepartment/' + orgId,
			method: 'GET',
			success: (res) => {
				this.obj = res.response;
			},
			error: (res) => {
				uni.showModal({
					title: '提示',
					content: res.msg,
					confirmColor: '#ee6666', //确定字体颜色
					showCancel: false, //没有取消按钮的弹框
					buttonText: '确定'
				});
			}
		});
	},
	data() {
		return {
			DepartmentCode: '',
			DepartmentName: '',
			obj: []
		};
	},
	methods: {
		radioChange(evt) {
			var i = parseInt(evt.detail.value);
			var Id = this.obj[i].Id;
			var DepartmentCode = this.obj[i].DepartmentCode;
			var DepartmentName = this.obj[i].DepartmentName;
			var OrgId = this.obj[i].OrgId;
			var OrgName = this.obj[i].OrgName;
			var ParentDepartmentID = this.obj[i].ParentDepartmentID;
			var ParentDepartmentName = this.obj[i].ParentDepartmentName;
			uni.setStorage({
				key: 'craftDepartmentInfo',
				data: {
					Id: Id,
					DepartmentCode: DepartmentCode,
					DepartmentName: DepartmentName,
					OrgId: OrgId,
					OrgName: OrgName,
					ParentDepartmentID: ParentDepartmentID,
					ParentDepartmentName: ParentDepartmentName
				}
			});
			uni.navigateBack({
				animationDuration: 1000
			});
		}
	}
};
</script>
