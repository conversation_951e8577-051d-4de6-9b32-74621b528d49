<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<radio-group @change="radioChange" class="margin-top">
				<view class="uni-list">
					<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">产线编码: {{ item.LineCode }}</view>
								<view class="cu-form-data-15">产线名称: {{ item.LineName }}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.LineCode === LineCode" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
export default {
	onLoad(e) {
		let DepartmentId = e.DepartmentId;
		this.request({
			url: '/CraftEquipmentInfo/GetProductionLine/' + DepartmentId,
			method: 'GET',
			success: (res) => {
				this.obj = res.response;
			},
			error: (res) => {
				uni.showModal({
					title: '提示',
					content: res.msg,
					confirmColor: '#ee6666', //确定字体颜色
					showCancel: false, //没有取消按钮的弹框
					buttonText: '确定'
				});
			}
		});
	},
	data() {
		return {
			LineCode: '',
			LineCode: '',
			obj: []
		};
	},
	methods: {
		radioChange(evt) {
			var i = parseInt(evt.detail.value);
			var Id = this.obj[i].Id;
			var LineCode = this.obj[i].LineCode;
			var LineName = this.obj[i].LineName;
			uni.setStorage({
				key: 'craftProductionLineInfo',
				data: {
					Id: Id,
					LineCode: LineCode,
					LineName: LineName
				}
			});
			uni.navigateBack({
				animationDuration: 1000
			});
		}
	}
};
</script>
