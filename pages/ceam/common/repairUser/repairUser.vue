<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue">
			</uni-search-bar>
			<radio-group @change="radioChange" class="margin-top">
				<ds-none :obj="obj"></ds-none>
				<view class="uni-list" v-if="obj.length != 0">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">工号: {{item.PersonnelCode}}</view>
								<view class="cu-form-data-15">名字: {{item.PersonnelName}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.PersonnelCode === PersonnelCode" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
	import dsNone from '@/components/ds-none'
	export default {
		components: {
			dsNone
		},
		onLoad(e) {
			this.query()
		},
		data() {
			return {
				searchValue: '',
				PersonnelCode: '',
				obj: []
			}
		},
		methods: {
			query() {
				this.request({
					url: '/EquipRepairMain/AllotPersonnel?key='+this.searchValue,
					method: 'GET',
					success: res => {
						this.obj = res.response;
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var Id = this.obj[i].Id;
				let PersonnelCode = this.obj[i].PersonnelCode;
				let PersonnelName = this.obj[i].PersonnelName;

				this.$store.commit('repairUser/setRepairUser', {
					repairUserId: Id,
					repairUserCode: PersonnelCode,
					repairUserName: PersonnelName
				})

				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>
