<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<view class="groupS">
					<text>工装编号：{{ EquipmentCode }}</text>
					<text>工装名称：{{ EquipmentName }}</text>
					<text>车间：{{ DepartmentName }}</text>
					<text>线别：{{ LineName }}</text>
				</view>
				<view>
					<button class="cu-btn bg-blue shadow" @click="one()" data-target="menuModal" style="width: 100%">未开机一键点检</button>
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="action">
					<text class="text-blue">点检项目</text>
				</view>
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in MaintCheckDetailList" :key="index">
				<view class="cu-form-group padding panel-full">
					<view class="groupS">
						<view class="action">
							<text class="cuIcon-title text-blue"></text>
							{{ item.MaintanceContent }}
						</view>
						<view class="uni-px-5">
							<uni-data-checkbox mode="tag" @change="change(item.Id)" :localdata="res" v-model="item.IsCheckDetail"></uni-data-checkbox>
						</view>
					</view>
				</view>
			</view>
			<!-- 			<view class="cu-form-group">
				<view class="title">点检结果：</view>
				<view class="#">
					<picker @change="bindPickerChange" :value="index" :range="sType">
						<view class="#">{{sType[index]}}</view>
					</picker>
				</view>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowdown" color='#8f8f94' size="25" />
			</view> -->
			<view class="cu-form-group margin-top">
				<uni-tag text="取消" type="warning" @click="cancel" />
				<uni-tag text="完成点检" type="success" @click="submit()" />
			</view>
		</view>
	</view>
</template>
<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
export default {
	components: {
		uniTag
	},
	data() {
		return {
			DepartmentName: '',
			LineName: '',
			EquipId: '',
			EquipmentCode: '',
			EquipmentName: '',
			// 点检类型
			type: null,
			// 点检 ID
			Id: null,
			// 单选数据源
			res: [
				{
					text: '是',
					value: 0
				},
				{
					text: '否',
					value: 1
				},
				{
					text: '未开机',
					value: 2
				}
			],
			// 点检内容
			MaintCheckDetailList: [],
			// slList:[],
			// swiper
			sType: ['通过', '未通过', '未开机'],
			index: 0
		};
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.cancel();
		uni.stopPullDownRefresh();
	},
	onLoad(e) {
		this.Id = e.Id;
		// 2 基础点检 3 运行点检
		this.type = e.type;
		this.getSI();
	},
	methods: {
		change(e) {
			// alert(e);
			// for(let i = 0; i < this.MaintCheckDetailList.length; i++){
			// 	if(this.MaintCheckDetailList[i].Id === e){
			// 		this.MaintCheckDetailList[i].IsCheckDetail = value;
			// 	}
			// }
		},
		one() {
			let that = this;
			for (let i = 0; i < this.MaintCheckDetailList.length; i++) {
				that.MaintCheckDetailList[i].IsCheckDetail = 2;
			}
		},
		// 获取点检项目
		getSI() {
			this.request({
				url: '/CraftEquipMaintCheckMain/GetEquipMaintCheckDetail',
				method: 'GET',
				data: {
					checkType: this.type,
					id: this.Id
				},
				success: (res) => {
					this.EquipmentCode = res.response.EquipmentCode;
					this.EquipmentName = res.response.EquipmentName;
					this.DepartmentName = res.response.DepartmentName;
					this.LineName = res.response.LineName;
					this.MaintCheckDetailList = res.response.MaintCheckDetailList;
				}
			});
		},
		// 取消
		cancel() {
			setTimeout(function () {
				uni.navigateBack({
					delta: 1 //返回层数，2则上上页
				});
			}, 500);
		},
		submit() {
			let that = this;
			// #ifdef APP-PLUS
			let platform = uni.getSystemInfoSync().platform;
			let btns = platform == 'android' ? ['确认', '取消'] : ['取消', '确认'];
			plus.nativeUI.confirm(
				'确认要提交吗？',
				function (e) {
					if (e.index == 0) {
						that.reqSI();
					} else {
					}
				},
				{
					title: '提示',
					buttons: btns
				}
			);
			// #endif
			// #ifdef H5
			uni.showModal({
				title: '提示',
				content: '确认要提交吗？',
				success: function (res) {
					if (res.confirm) {
						that.reqSI();
					} else if (res.cancel) {
						// 执行逻辑
					}
				}
			});
			// #endif
		},
		// 点检请求
		reqSI() {
			//let isResult = this.sType[this.index] === '通过' ? true : false;
			this.request({
				url: '/CraftEquipMaintCheckMain/AddEquipMaintCheck',
				method: 'POST',
				data: {
					MaintanceType: this.type,
					EquipId: this.Id,
					IsCheckPass: true,
					MaintCheckDetailList: this.MaintCheckDetailList
				},
				success: (res) => {
					this.$toast(res.msg);
					setTimeout(function () {
						uni.navigateBack({
							delta: 1 //返回层数，2则上上页
						});
					}, 1500);
				},
				error: (res) => {
					uni.showModal({
						title: '提示',
						content: res.msg,
						confirmColor: '#ee6666', //确定字体颜色
						showCancel: false, //没有取消按钮的弹框
						buttonText: '确定',
						success: function (res) {
							if (res.confirm) {
								console.log('用户点击确定');
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
			});
		},
		/* picker */
		bindPickerChange: function (e) {
			console.log('picker发送选择改变，携带值为', e.detail.value);
			this.index = e.detail.value;
		}
	}
};
</script>
<style>
.groupS {
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.uni-px-5 {
	padding-left: 10px;
	padding-right: 10px;
}
</style>
