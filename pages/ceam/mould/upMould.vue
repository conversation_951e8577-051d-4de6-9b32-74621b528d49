<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">工单号</view>
				<input v-model="wo" @confirm="queryWo()" placeholder="请手动扫描工单号" :focus="focusZore" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">设备码</view>
				<input v-model="machineCode" @confirm="queryMachineInfo()" placeholder="请手动扫描设备码" :focus="focusOne" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon" />
				<view class="title">模具码</view>
				<input v-model="mouldCode" @confirm="queryMouldInfo()" placeholder="请手动扫描模具码" :focus="focusTwo" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>

			<view class="cu-form-group">
				<view>
					<view class="uni-ellipsis">设备名称：{{machineInfo.EquipmentName}}</view>
					<view class="uni-ellipsis">设备型号：{{machineInfo.EquipmentModel}}</view>

					<!-- <view class="uni-ellipsis">模具名称：{{mouldInfo.MouldName}}</view>
					<view class="uni-ellipsis">模具型号：{{mouldInfo.MouldType}}</view> -->
					<view class="uni-ellipsis">模具名称：{{mouldInfo.EquipmentName}}</view>
					<view class="uni-ellipsis">模具型号：{{mouldInfo.EquipmentModel}}</view>
					<view class="uni-ellipsis">剩余寿命：{{mouldInfo.ResidualLife}}</view>
				</view>
			</view>

			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">取消</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				wo: '',
				machineCode: '',
				mouldCode: '',

				actualQty: '',
				machineInfo: {},
				mouldInfo: {},

				msg: '',
				// 焦点控制
				focusZore: true,
				focusOne: false,
				focusTwo: false
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		methods: {
			// 工单查询
			queryWo() {
				if (!this.wo) {
					this.$toast('未获取的工单号')
					return
				}
				this.request({
					url: '/WOMain/Get',
					method: 'GET',
					data: {
						wo: this.wo,
						inPageSize: 1,
						inPageIndex: 1,
					},
					success: res => {
						if (res.response.dataCount == 1 && this.wo == res.response.data[0].Wo) this.changeBlur();
						else {
							this.wo == ''
							this.$error('无此工单')
						}
					},
					error: res => {
						this.$error(res.msg)
						this.cancel();
					}
				});
			},
			// 查询设备信息
			queryMachineInfo() {
				this.machineInfo = {};
				if (!this.machineCode) {
					this.$toast('未获取到设备码')
					return
				}
				this.request({
					url: '/EquipmentInfo/GetEquipmentInfoByCode',
					method: 'GET',
					data: {
						code: this.machineCode,
					},
					success: res => {
						this.machineInfo = res.response
					},
					error: res => {
						this.$error(res.msg)
						this.cancel();
					}
				})
				this.changeBlur();
			},
			// 查询模具信息
			queryMouldInfo() {
				this.mouldInfo = {};
				if (!this.mouldCode) {
					this.$toast('未获取到模具码')
					return
				}
				this.request({
					// url: '/Mould/GetMouldInfoByCode',
					url: '/CraftEquipmentInfo/GetEquipmentInfoByCode',
					method: 'GET',
					data: {
						code: this.mouldCode,
					},
					success: res => {
						this.mouldInfo = res.response
					},
					error: res => {
						this.$error(res.msg)
						this.cancel();
					}
				})
				this.changeBlur();
			},
			// 提交真实数量
			submit() {
				let that = this;
				if (!this.wo) {
					uni.showToast({
						title: '请扫工单号!',
						icon: 'error'
					})
				}
				if (!this.machineInfo) {
					uni.showToast({
						title: '请扫设备码！',
						icon: 'error'
					})
				} else if (!this.mouldInfo) {
					uni.showToast({
						title: '请扫模具码！',
						icon: 'error'
					})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要上模吗？', function(e) {
						//0==确认，否则取消  
						if (e.index == 0) {
							that.req()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要上模吗？',
						success: function(res) {
							if (res.confirm) {
								that.req()
							} else if (res.cancel) {}
						}
					})
					// #endif
				}
			},
			// 请求
			req() {
				this.request({
					url: '/CraftMouldOperationRecord/Post',
					method: 'POST',
					data: {
						ResidualLife: this.mouldInfo.ResidualLife,
						MouldId: this.mouldInfo.Id,
						EquipId: this.machineInfo.Id,
						Wo: this.wo
					},
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'success'
						});
						this.cancel();
						this.changeBlur();
					},
					error: res => {
						this.$error(res.msg)
						this.cancel();
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.wo = '';
				this.machineCode = '';
				this.mouldCode = '';
				this.machineInfo = {};
				this.mouldInfo = {};
			},
			// 焦点处理
			changeBlur() {

				let that = this;
				if (this.wo == '') {
					that.focusZore = true;
					that.focusOne = false;
					that.focusTwo = false;
				} else if (this.machineCode == '') {
					that.focusZore = false;
					that.focusOne = true;
					that.focusTwo = false;
				} else if (this.mouldCode == '') {
					that.focusZore = false;
					that.focusTwo = true;
					that.focusOne = false;
				}
			},
			// 
			reset() {
				this.machineCode = '';
				this.mouldCode = '';
			}
		}
	}
</script>

<style>

</style>