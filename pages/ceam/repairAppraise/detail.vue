<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item uni-ellipsis">报修人姓名：{{ repairDetail.RRepairUser }}</view>
				<view class="uni-flex-item uni-ellipsis">报修人电话：{{ repairDetail.Phone }}</view>
				<view class="uni-flex-item uni-ellipsis">报修时间：{{ repairDetail.CreateTime }}</view>
				<view class="uni-flex-item uni-ellipsis">工装名称：{{ repairDetail.EquipmentName }}</view>
				<view class="uni-flex-item uni-ellipsis">事业部：{{ repairDetail.OrgName }}</view>
				<view class="uni-flex-item uni-ellipsis">车间：{{ repairDetail.DepartmentName }}</view>
				<view class="uni-flex-item uni-ellipsis">产线：{{ repairDetail.LineName }}</view>
				<view class="uni-flex-item uni-ellipsis">故障描述：{{ repairDetail.RepairfaultSituation }}</view>
				<view class="uni-flex-item uni-ellipsis">计划维修时间：{{ repairDetail.PlanTime }}</view>
				<view class="uni-flex-item uni-ellipsis">需求工种：{{ repairDetail.RequiredName }}</view>
				<view class="uni-flex-item uni-ellipsis">安排维修工：{{ repairDetail.TaskAccepter }}</view>
				<view class="uni-flex-item uni-ellipsis">派工时间：{{ repairDetail.TaskAcceptDate }}</view>
				<view class="uni-flex-item uni-ellipsis">故障原因分析：{{ analysis }}</view>
				<view class="uni-flex-item uni-ellipsis">维修处理情况：{{ dealRes }}</view>
			</view>
			<!-- <view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-blue"></text> 故障原因分析
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<textarea v-model="analysis" disabled="true" placeholder="请填写故障原因分析" />
			</view>
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-blue "></text> 维修处理情况
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<textarea v-model="dealRes" disabled="true" placeholder="请填写维修处理情况" />
			</view> -->
			<view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue"></text>
					评价
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<view class="title">
					<text style="color: #e3162e">*</text>
					评价等级
				</view>
				<uni-rate :disabled="isEdit" @change="onChange" :value="level" margin="20" />
			</view>
			<view class="cu-form-group solid-bottom">
				<view class="title">
					<text style="color: #e3162e">*</text>
					详细评价
				</view>
				<textarea v-model="appraise" :disabled="isEdit" placeholder="请填写详细评价" />
			</view>
			<view class="margin-top" />
			<uni-tag text="提交评价" type="success" @click="submit" v-show="!isEdit" />
			<view class="margin-top" />
		</view>
	</view>
</template>

<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
import uniRate from '@/components/uni-rate/uni-rate.vue';
export default {
	components: {
		uniTag,
		UniIcons,
		uniRate
	},
	data() {
		return {
			appraise: '',
			// 等级
			level: 0,
			// 不可编辑
			isEdit: true,
			// 列表
			repairDetail: {},
			// 主
			id: null,
			status: '',
			// 故障原因分析
			analysis: '',
			// 处理情况
			dealRes: ''
		};
	},
	onLoad(e) {
		this.id = e.id;
		this.status = e.status;
		this.queryRepairDetail(e.id);
	},
	computed: {
		repairUser() {
			return this.$store.state.repairUser.repairUser.repairUserName;
		}
	},
	methods: {
		submit() {
			const app = this;
			console.log(app.level);
			if (app.level == 0) {
				app.$error('至少给颗小星星鼓励一下啊 ಥ_ಥ');
			} else if (!app.appraise) {
				app.$error('期待您的评价 ༼ つ ◕_◕ ༽つ');
			} else {
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ['确认', '取消'] : ['取消', '确认'];
				plus.nativeUI.confirm(
					'确认要提交吗？',
					function (e) {
						if (e.index == 0) {
							app.req();
						} else {
						}
					},
					{
						title: '提示',
						buttons: btns
					}
				);
				// #endif
				// #ifdef H5
				uni.showModal({
					title: '提示',
					content: '确认要提交吗？',
					success: function (res) {
						if (res.confirm) {
							app.req();
						} else if (res.cancel) {
							// 执行逻辑
						}
					}
				});
				// #endif
			}
		},
		req() {
			this.request({
				url: '/CraftEquipRepairMain/UpdateEvaluate',
				method: 'PUT',
				data: {
					Id: this.repairDetail.Id,
					EvaluationLever: this.level,
					EvaluationContent: this.appraise
				},
				success: (res) => {
					this.queryRepairDetail(this.id);
					this.$toast(res.msg);
					setTimeout(function () {
						uni.navigateBack({
							delta: 1 //返回层数，2则上上页
						});
					}, 2000);
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 查询维修工单明细
		queryRepairDetail(id) {
			const app = this;
			this.request({
				url: '/CraftEquipRepairMain/Get/' + id,
				method: 'GET',
				success: (res) => {
					this.repairDetail = res.response;
					app.analysis = app.repairDetail.RepairfaultReason;
					app.dealRes = app.repairDetail.RepairfaultProject;
					app.level = app.repairDetail.EvaluationLever;
					app.appraise = app.repairDetail.EvaluationContent;
					if (app.repairDetail.Status == 'RepairOK') {
						app.isEdit = false;
					}
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		onChange(e) {
			this.level = e.value;
		}
	}
};
</script>
