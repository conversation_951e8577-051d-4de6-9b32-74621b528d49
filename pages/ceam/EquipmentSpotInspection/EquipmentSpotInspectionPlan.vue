<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="margin-top"></view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in planList" :key="index">
				<view class="cu-form-group solid-bottom panel-full " @click="getDetailInfo(item.equipNo)">
					<view style="width: 20%; ">
						<image style="width: 50px; height: 50px;" :src="item.image"></image>
					</view>
					<view style="width: 70%;">
						<strong>
							<view class="text-black cu-form-data-10">{{item.maintenanceLevel}}</view>
							<view class="text-black cu-form-data-10">{{item.maintenanceNo}}</view>
						</strong>
						<view class="text-gray cu-form-data-10">设备名称：{{item.equipDesc}}</view>
						<view class="text-gray cu-form-data-10">设备编码：{{item.equipNo}}</view>
						<view class="text-gray cu-form-data-10">保养时间：{{item.repairTime}}</view>
					</view>
					<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94'
						size="25" />
				</view>
			</view>
			<view style="height: 100upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom">
				<uni-tag text="查询" type="success" @click="submit()" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				planList: [
					{	
						maintenanceLevel: '一级保养',
						maintenanceNo:'SPKJ00001',
						image: '../../static/images/none.jpg',
						equipDesc: '液压机BC01',
						equipNo: 'SP124321',
						repairTime: '2022-5-19'
					},
					{
						maintenanceLevel: '二级保养',
						maintenanceNo:'SPKJ00001',
						image: '../../static/images/none.jpg',
						equipDesc: '液压机BC01',
						equipNo: 'SP124321',
						repairTime: '2022-5-19'
					},
				]
			}
		},
		methods: {

		}
	}
</script>

<style>

</style>

