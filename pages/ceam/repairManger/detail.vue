<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item">报修人姓名：{{ repairDetail.RRepairUser }}</view>
				<view class="uni-flex-item">报修人电话：{{ repairDetail.Phone }}</view>
				<view class="uni-flex-item">报修时间：{{ repairDetail.CreateTime }}</view>
				<view class="uni-flex-item">工装名称：{{ repairDetail.EquipmentName }}</view>
				<view class="uni-flex-item">事业部：{{ repairDetail.OrgName }}</view>
				<view class="uni-flex-item">车间：{{ repairDetail.DepartmentName }}</view>
				<view class="uni-flex-item">产线：{{ repairDetail.LineName }}</view>
				<view class="uni-flex-item">故障描述：{{ repairDetail.RepairfaultSituation }}</view>
				<view class="uni-flex-item">计划时间：{{ repairDetail.StartTime }}</view>
				<view class="uni-flex-item">需求工种：{{ repairDetail.RequiredName }}</view>
			</view>

			<view class="cu-form-group margin-top" @click="getRepairUser">
				<view class="title" style="width: 120px">安排维修工</view>
				<input v-model="repairUser" disabled="true" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25" />
			</view>
			<view class="cu-form-group">
				<view class="title" style="width: 120px">计划完成时间</view>
				<!-- <picker mode="date" :value="date" @change="bindDateChange">
					<view style="color: red;">{{date}}</view>
				</picker> -->
				<picker mode="multiSelector" :value="dateTime" @change="changeDateTime" @columnchange="changeDateTimeColumn" :range="dateTimeArray" style="min-height: 30px">
					<view class="lableBox" style="margin-top: 4rpx">
						<view style="color: red" class="otherCheck">{{ timeStr }}</view>
					</view>
				</picker>
			</view>

			<view class="cu-form-group margin-top">
				<uni-tag text="清空" type="warning" @click="cancel" />
				<uni-tag text="确认" type="success" @click="submit" />
			</view>
		</view>
	</view>
</template>

<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
const { dateTimePicker, getMonthDay, generateTimeStr } = require('../../../common/js/dateTimePicker.js');

export default {
	components: {
		uniTag
	},
	data() {
		const currentDate = this.getDate({
			format: true
		});
		return {
			repairDetail: {},
			date: currentDate,
			id: null,
			dateTime: null,
			dateTimeArray: null,
			startYear: 2020,
			timeStr: ''
		};
	},
	onLoad(e) {
		this.id = e.id;
		this.queryRepairDetail(e.id);
		this.initTime();
	},
	computed: {
		repairUser() {
			return this.$store.state.repairUser.repairUser.repairUserName;
		}
	},
	methods: {
		submit() {
			const app = this;
			if (app.repairUser == '') {
				app.$error('请选择维修人员');
				return;
			}
			if (app.timeStr == '') {
				app.$error('请选择计划维修时间');
				return;
			}

			// #ifdef APP-PLUS
			let platform = uni.getSystemInfoSync().platform;
			let btns = platform == 'android' ? ['确认', '取消'] : ['取消', '确认'];
			plus.nativeUI.confirm(
				'确认要提交吗？',
				function (e) {
					//0==确认，否则取消
					if (e.index == 0) {
						app.req();
					} else {
					}
				},
				{
					title: '提示',
					buttons: btns
					// "verticalAlign":"bottom"
				}
			);
			// #endif
			// #ifdef H5
			uni.showModal({
				title: '提示',
				content: '确认要提交吗？',
				success: function (res) {
					if (res.confirm) {
						app.req();
					} else if (res.cancel) {
						// 执行逻辑
					}
				}
			});
			// #endif
		},
		req() {
			this.request({
				url: '/CraftEquipRepairMain/SubmitDispatching',
				method: 'PUT',
				data: {
					Id: this.id,
					PlanTime: this.timeStr,
					TaskAccepter: this.$store.state.repairUser.repairUser.repairUserId
				},
				success: (res) => {
					this.$toast(res.msg);
					setTimeout(function () {
						uni.navigateBack({
							delta: 1 //返回层数，2则上上页
						});
					}, 2000);
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		cancel() {
			this.timeStr = '';
			this.$store.commit('repairUser/empty');
		},
		// 维修工
		getRepairUser() {
			uni.navigateTo({
				url: '../common/repairUser/repairUser'
			});
		},
		// 查询维修工单明细
		queryRepairDetail(id) {
			this.request({
				url: '/CraftEquipRepairMain/Get/' + id,
				method: 'GET',
				success: (res) => {
					this.repairDetail = res.response;
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		/* date picker */
		bindDateChange: function (e) {
			this.date = e.detail.value;
		},
		getDate(type) {
			const date = new Date();
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let day = date.getDate();

			if (type === 'start') {
				year = year - 60;
			} else if (type === 'end') {
				year = year + 2;
			}
			month = month > 9 ? month : '0' + month;
			day = day > 9 ? day : '0' + day;
			return `${year}-${month}-${day}`;
		},
		initTime() {
			let date = new Date();
			let endYear = date.getFullYear() + 1;
			// 获取完整的年月日 时分秒，以及默认显示的数组
			let obj = dateTimePicker(this.startYear, endYear);
			// 精确到分的处理，将数组的秒去掉
			// let lastArray = obj.dateTimeArray.pop();
			// let lastTime = obj.dateTime.pop();

			this.dateTimeArray = obj.dateTimeArray;
			this.dateTime = obj.dateTime;
		},
		/* date picker */
		changeDateTime(e) {
			this.dateTime = e.detail.value;
			this.timeStr = generateTimeStr(this.dateTimeArray, this.dateTime);
			//ios时间不能用'-'解析成时间戳
		},
		/*年,月切换时重新更新计算*/
		changeDateTimeColumn(e) {
			//let {id} = e.target;
			let { column, value } = e.detail;
			if (column == 0 || column == 1) {
				//直接修改数组下标视图不更新,用深拷贝之后替换数组
				let dateTime = JSON.parse(JSON.stringify(this.dateTime));
				let dateTimeArray = JSON.parse(JSON.stringify(this.dateTimeArray));
				dateTime[column] = value;
				dateTimeArray[2] = getMonthDay(dateTimeArray[0][dateTime[0]], dateTimeArray[1][dateTime[1]]);
				this.dateTime = dateTime;
				this.dateTimeArray = dateTimeArray;
			}
		},
		getDate(type) {
			const date = new Date();
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let day = date.getDate();

			if (type === 'start') {
				year = year - 60;
			} else if (type === 'end') {
				year = year + 2;
			}
			month = month > 9 ? month : '0' + month;
			day = day > 9 ? day : '0' + day;
			return `${year}-${month}-${day}`;
		}
	}
};
</script>
