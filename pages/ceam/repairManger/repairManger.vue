<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue"></uni-search-bar>
			<view class="cu-form-group" v-show="obj.length != 0">
				<text class="text-blue cuIcon-titles">待安排维修单</text>
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
				<view class="cu-form-group solid-bottom panel-full padding-xs" @click="getDetailInfo(item.Id)">
					<view style="width: 80%">
						<view class="text-black uni-ellipsis">工装编号：{{ item.EquipmentCode }}</view>
						<view class="text-black uni-ellipsis">工装名称：{{ item.EquipmentName }}</view>
						<view class="text-black uni-ellipsis">报修日期：{{ item.CreateTime }}</view>
						<view class="text-black uni-ellipsis">报修人员：{{ item.RRepairUser }}</view>
					</view>
					<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color="#8f8f94" size="25" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			obj: [],
			searchValue: ''
		};
	},
	onLoad() {
		this.query();
	},
	onShow() {
		this.query();
	},
	methods: {
		query() {
			this.request({
				url: '/CraftEquipRepairMain/GetDispatching',
				method: 'GET',
				data: {
					inPageSize: 20,
					inPageIndex: 1,
					key: this.searchValue
				},
				success: (res) => {
					this.obj = res.response.data;
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 查询 维修安排 明细
		getDetailInfo(id) {
			uni.navigateTo({
				url: './detail?id=' + id
			});
		}
	}
};
</script>

<style></style>
