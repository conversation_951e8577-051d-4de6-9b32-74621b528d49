<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue"></uni-search-bar>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item, index) in obj" :key="index">
				<view class="cu-form-group solid-bottom panel-full padding-xs" @click="getDetailInfo(item.Id, item.Status, item.EvaluationLever, item.EvaluationContent)">
					<view style="width: 80%">
						<view class="text-black uni-ellipsis">工装编号：{{ item.EquipmentCode }}</view>
						<view class="text-black uni-ellipsis">工装名称：{{ item.EquipmentName }}</view>
						<view class="text-black uni-ellipsis">报修日期：{{ item.CreateTime }}</view>
						<view class="text-black uni-ellipsis">报修人员：{{ item.RRepairUser }}</view>
					</view>
					<view class="text-black cu-form-data-10 uni-ellipsis" style="20%">
						<uni-tag text="未维修" inverted="true" type="default" v-if="item.Status == 'ToAllot'" />
						<uni-tag text="已派工" inverted="true" type="warning" v-if="item.Status == 'Assigned'" />
						<uni-tag text="已接收" inverted="true" type="success" v-if="item.Status == 'Receive'" />
						<uni-tag text="配件申请" inverted="true" type="primary" v-if="item.Status == 'AccessoriesApply'" />
						<uni-tag text="配件到位" inverted="true" type="error" v-if="item.Status == 'AccessoriesPlace'" />
						<uni-tag text="维修中" inverted="true" type="warning" v-if="item.Status == 'StartRepair'" />
						<uni-tag text="维修完毕" inverted="true" type="success" v-if="item.Status == 'RepairOK'" />
						<uni-tag text="评价完成" inverted="true" type="primary" v-if="item.Status == 'Evaluate'" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
import vTabs from '@/components/v-tabs/v-tabs.vue';
export default {
	components: {
		uniTag,
		UniIcons,
		vTabs
	},
	data() {
		return {
			obj: [],
			searchValue: ''
		};
	},
	onLoad() {
		this.query();
	},
	methods: {
		// 查询信息列表
		query() {
			this.request({
				url: '/CraftEquipRepairMain/GetMyOrderList',
				method: 'GET',
				data: {
					inPageSize: 20,
					inPageIndex: 1,
					key: this.searchValue
				},
				success: (res) => {
					this.obj = res.response.data;
				},

				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 查询 明细
		getDetailInfo(id) {
			uni.navigateTo({
				url: './detail?id=' + id
			});
		}
	}
};
</script>
