<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue"></text>
					报修信息
				</view>
			</view>
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item uni-ellipsis">报修人员：{{ repairDetail.RRepairUser }}</view>
				<view class="uni-flex-item uni-ellipsis">报修人员电话：{{ repairDetail.Phone }}</view>
				<view class="uni-flex-item uni-ellipsis">工装编号：{{ repairDetail.EquipmentCode }}</view>
				<view class="uni-flex-item uni-ellipsis">工装名称：{{ repairDetail.EquipmentName }}</view>
				<view class="uni-flex-item uni-ellipsis">故障描述：{{ repairDetail.RepairfaultSituation }}</view>

				<view class="uni-flex-item uni-ellipsis">事业部：{{ repairDetail.OrgName }}</view>
				<view class="uni-flex-item uni-ellipsis">车间：{{ repairDetail.DepartmentName }}</view>
				<view class="uni-flex-item uni-ellipsis">产线：{{ repairDetail.LineName }}</view>
				<view class="uni-flex-item uni-ellipsis">报修时间：{{ repairDetail.CreateTime }}</view>
				<view class="uni-flex-item uni-ellipsis">计划维修时间：{{ repairDetail.StartTime }}</view>
				<view class="uni-flex-item uni-ellipsis">需求工种：{{ repairDetail.RequiredName }}</view>
			</view>
			<view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue"></text>
					维修安排
				</view>
			</view>
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item uni-ellipsis">维修工：{{ repairDetail.TaskAccepter }}</view>
				<view class="uni-flex-item uni-ellipsis">计划完成时间：{{ repairDetail.PlanTime }}</view>
				<view class="uni-flex-item uni-ellipsis">维修安排时间：{{ repairDetail.TaskAcceptDate }}</view>
			</view>
			<view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue"></text>
					维修处理
				</view>
			</view>
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item uni-ellipsis">故障类型：{{ repairDetail.FaultName }}</view>
				<view class="uni-flex-item uni-ellipsis">故障原因分析：{{ repairDetail.RepairfaultReason }}</view>
				<view class="uni-flex-item uni-ellipsis">维修处理情况：{{ repairDetail.RepairProcessing }}</view>
				<view class="uni-flex-item uni-ellipsis">处理接收时间：{{ repairDetail.TaskAcceptDate }}</view>
				<view class="uni-flex-item uni-ellipsis">开始维修时间：{{ repairDetail.StartRepairDate }}</view>
				<view class="uni-flex-item uni-ellipsis">维修完毕时间：{{ repairDetail.RepairOKDate }}</view>
				<view class="uni-flex-item uni-ellipsis">备品申请时间：{{ repairDetail.AccessoriesApplyDate }}</view>
				<view class="uni-flex-item uni-ellipsis">备品到位时间：{{ repairDetail.AccessoriesPlaceDate }}</view>
				<view class="uni-flex-item uni-ellipsis">备品申请明细：{{ repairDetail.ChoiceName }}</view>
				<view class="uni-flex-item uni-ellipsis">实际维修时间(h)：{{ repairDetail.RealityTime }}</view>
			</view>
			<!-- 维修安排 -->
			<!-- <view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue "></text> 维修安排
				</view>
			</view>
			<view class="cu-form-group">
				<view style="width: 28%;">安排维修工</view>
				<input v-model="repairUser" disabled="isEdit" />
			</view>
			<view class="cu-form-group">
				<view style="width: 28%;">计划维修时间</view>
				<input v-model="planRepairDate" disabled="isEdit" />
			</view>
			<view class="cu-form-group">
				<view style="width: 28%;">维修安排时间</view>
				<input v-model="mangerRepairDate" disabled="isEdit" />
			</view> -->
			<!-- 维修处理 -->
			<!-- <view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue "></text> 维修处理
				</view>
			</view>
			<view class="cu-form-group">
				<view style="width: 28%;">故障类型</view>
				<input v-model="malfunctionType" disabled="isEdit" />
			</view> -->
			<!-- <view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-blue"></text> 故障原因分析
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<textarea v-model="analysis" disabled="isEdit" placeholder="请填写故障原因分析" />
			</view>
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-blue "></text> 维修处理情况
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<textarea v-model="dealRes" disabled="isEdit" placeholder="请填写维修处理情况" />
			</view> -->
			<!-- <view class="cu-form-group">
				<view style="width: 28%;">开始维修时间</view>
				<input v-model="beginRepairDate" disabled="isEdit" />
			</view>
			<view class="cu-form-group">
				<view style="width: 28%;">维修完成时间</view>
				<input v-model="endRepairDate" disabled="isEdit" />
			</view> -->
			<view class="cu-bar bg-white dashed margin-top">
				<view class="action">
					<text class="cuIcon-titles text-blue"></text>
					评价
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<view style="width: 28%">
					<text style="color: #e3162e">*</text>
					评价等级
				</view>
				<uni-rate :disabled="isEdit" :value="repairDetail.EvaluationLever" margin="20" />
			</view>
			<view class="cu-form-group solid-bottom">
				<view style="width: 28%">
					<text style="color: #e3162e">*</text>
					详细评价
				</view>
				<textarea v-model="repairDetail.EvaluationContent" :disabled="isEdit" placeholder="请填写详细评价" />
			</view>
			<view class="cu-form-group solid-bottom">
				<view style="width: 28%">
					<text style="color: #e3162e">*</text>
					评价提交时间
				</view>
				<input v-model="repairDetail.EvaluationDate == '1900-01-01 00:00:00' ? '' : repairDetail.EvaluationDate" :disabled="isEdit" placeholder="评价提交时间" />
			</view>
			<view class="margin-top" /
			<view class="cu-form-group margin-top">
				<uni-tag
					text="删除"
					type="error"
					@click="deleteFalse"
					v-if="IsChange"
					:disabled="Status === 'RepairOK' ? true : false || Status === 'Evaluate' ? true : false || Status === 'ToAllot' ? true : false"
				/>
				<uni-tag
					text="退回"
					type="error"
					inverted="true"
					v-if="IsChange"
					@click="rollback"
					:disabled="Status === 'RepairOK' ? true : false || Status === 'Evaluate' ? true : false"
				/>
			</view>
			<view class="margin-top" />
		</view>
	</view>
</template>

<script>
import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
import uniRate from '@/components/uni-rate/uni-rate.vue';
export default {
	components: {
		uniTag,
		UniIcons,
		uniRate
	},
	data() {
		return {
			isEdit: true,
			// 主
			id: null,
			Status: '',
			// 列表
			repairDetail: {},
			/* 安排 */
			repairUser: '',
			mangerRepairDate: '',
			planRepairDate: '',
			/* 处理 */
			malfunctionType: '',
			beginRepairDate: '',
			endRepairDate: '',
			analysis: '',
			dealRes: '',
			/* 评价 */
			// 评价
			appraise: '',
			// 等级
			level: 0,
			// 故障原因分析
			analysis: '',
			// 处理情况
			dealRes: '',
			IsChange: false
		};
	},
	onLoad(e) {
		this.id = e.id;
		this.queryRepairDetail(e.id);
	},
	methods: {
		// 假删除
		deleteFalse() {
			this.request({
				url: '/CraftEquipRepairMain/DeleteDetailById?id=' + this.repairDetail.Id,
				method: 'PUT',
				success: (res) => {
					this.$error(res.msg);
					setTimeout(function () {
						uni.navigateBack({
							delta: 1 //返回层数，2则上上页
						});
					}, 2000);
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 退回
		rollback() {
			this.request({
				url: '/CraftEquipRepairMain/SendBack?id=' + this.repairDetail.Id,
				method: 'PUT',
				success: (res) => {
					this.$error(res.msg);
					setTimeout(function () {
						uni.navigateBack({
							delta: 1 //返回层数，2则上上页
						});
					}, 2000);
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		},
		// 查询维修工单明细
		queryRepairDetail(id) {
			const app = this;
			this.request({
				url: '/CraftEquipRepairMain/Get/' + id,
				method: 'GET',
				success: (res) => {
					this.repairDetail = res.response;
					app.analysis = app.repairDetail.RepairfaultReason;
					app.dealRes = app.repairDetail.RepairfaultProject;
					app.level = app.repairDetail.EvaluationLever;
					app.appraise = app.repairDetail.EvaluationContent;
					app.IsChange = app.repairDetail.IsChange;
					app.Status = app.repairDetail.Status;
				},
				error: (res) => {
					this.$error(res.msg);
				}
			});
		}
	}
};
</script>
