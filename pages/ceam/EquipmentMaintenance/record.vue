<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar :focus="true" v-model="searchValue" @confirm="query"></uni-search-bar>
			<!-- <view class="cu-form-group" @click="queryDepartment()">
				<view class="title">车间：</view>
				<input v-model="DepartmentName" disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group" @click="queryProductionLine()">
				<view class="title">线别：</view>
				<input v-model="LineName" disabled="true" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view> -->
			<view class="uni-list-cell margin-top" hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
				:key="index">
				<view class="cu-form-group padding panel-full "  @click="getDetailInfo(item.Id,item.EquipmentCode,item.EquipmentName,item.MaintanceType,item.IsPass)">
					<view style="width: 60%; " >
						<view class="text-black cu-form-data-10">保养单号：{{item.RecordNo}}</view>
						<view class="text-black cu-form-data-10">设备编码：{{item.EquipmentCode}}</view>
						<view class="text-black cu-form-data-10">设备名称：{{item.EquipmentName}}</view>
						<view class="text-black cu-form-data-10">类型：{{item.MaintanceType=='FirstMaintenance'?'一级保养':'二级保养'}}</view>
						<view class="text-black cu-form-data-10">保养人：{{item.EquipMaintUser}}</view>
						<view class="text-black cu-form-data-10" style="min-width: 300px;">保养时间：{{item.UpdateTime}}</view>
						
						
					</view>
					<view style="width: 30%; display: flex; flex-direction: column; justify-content: space-between;">
						<view 
							<uni-tag text="待保养" type="warning"  v-if="item.IsPass==null" inverted="true"/>
							<uni-tag text="通过" type="success" v-if="item.IsPass==true" inverted="true"/>
							<uni-tag text="不通过" type="error" v-if="item.IsPass==false" inverted="true"/>
							
						</view>
					</view>
				</view>
			</view>
			<!-- <view style="height: 100upx;"> -->
				<!-- 撑一下 -->
			<!-- </view>
			<view class="uni-fixed-bottom">
				<uni-tag text="查询" type="success" @click="queryEquipmentSpotInspection()" />
			</view> -->
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				// 车间
				DepartmentId: '',
				DepartmentCode: '',
				DepartmentName: '',
				// 产线
				Id: '',
				LineCode: '',
				LineName: '',
				// 设备
				EquipId: null,
				EquipmentCode: '',
				EquipmentName: '',
				// 列表
				obj: [],
				// 搜索条件
				searchValue:''
			}
		},
		onLoad() {

		},
		onShow() {
			// uni.getStorage({
			// 	key: 'departmentInfo',
			// 	success: o => {
			// 		this.DepartmentId = o.data.Id,
			// 			this.DepartmentCode = o.data.DepartmentCode,
			// 			this.DepartmentName = o.data.DepartmentName
			// 	}
			// });
			// uni.getStorage({
			// 	key: 'productionLineInfo',
			// 	success: o => {
			// 		this.Id = o.data.Id,
			// 			this.LineCode = o.data.LineCode,
			// 			this.LineName = o.data.LineName
			// 	}
			// });
			this.query();
		},
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		methods: {
			query(){
				this.request({
					// url: '/EquipMaintCheckMain/Get',
					url: '/CraftEquipMaintRecordMain/QueryMyFinish',
					method: 'GET',
					data: {
						inPageSize: 9999,
						inPageIndex: 1,
						key: this.searchValue,
					},
					success: res => {
						this.obj = res.response.data;
					}
				})
			},
			// 查询车间
			/* queryDepartment() {
				uni.removeStorage({
					key: 'departmentInfo',
					success() {
						console.log("车间 缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../common/department/department'
				})
			}, */
			// 查询车间
			/* queryProductionLine() {
				if (this.DepartmentId == '') {
					uni.showToast({
						title: '请先选择车间',
						icon: 'loading'
					})
					return;
				}
				uni.removeStorage({
					key: 'productionLineInfo',
					success() {
						console.log("产线 缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../common/productionLine/productionLine?DepartmentId=' + this.DepartmentId
				})
			}, */
			// 查询点检列表
			/* queryEquipmentSpotInspection() {
				let that = this;
				if (this.DepartmentName == '') {
					uni.showToast({
						title: '请先选择部门',
						icon: 'loading'
					})
					return;
				} else if (this.LineName == '') {
					uni.showToast({
						title: '请先选择产线',
						icon: 'loading'
					})
					return;
				} else {
					this.request({
						url: '/EquipMaintCheckMain/GetShopAndLine/',
						method: 'GET',
						data: {
							shopId: this.DepartmentId,
							lineId: this.Id
						},
						success: res => {
							this.obj = res.response;
						}
					})
				}
			}, */
			//
			getDetailInfo(Id,EquipmentCode,EquipmentName,MaintanceType,IsPass) {
				uni.navigateTo({
					url: './recordDetail?Id=' + Id+'&EquipmentCode='+ EquipmentCode+'&EquipmentName='+ EquipmentName+'&MaintanceType='+MaintanceType+'&IsPass='+(IsPass==true?1:0)
				})
			},
			cancel(){
				this.obj = [];
			}
		}
	}
</script>

<style>

</style>
