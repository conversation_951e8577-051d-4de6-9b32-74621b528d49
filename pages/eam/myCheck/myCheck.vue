<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<uni-search-bar @confirm="query" :focus="true" v-model="searchValue">
			</uni-search-bar>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in obj" :key="index">
				<view class="cu-form-group solid-bottom panel-full padding-xs"
					>
					<view @click="getDetailFull(item.Id,item.EquipmentId)" style="width: 60%;">
						<view class="text-black uni-ellipsis">设备编号：{{item.EquipmentCode}}</view>
						<view class="text-black uni-ellipsis">设备名称：{{item.EquipmentName}}</view>
						<view class="text-black uni-ellipsis">点检时间：{{item.CreateTime}}</view>
						<view class="text-black uni-ellipsis">点检人：{{item.EquipCheckUser}}</view>
						<view class="text-black uni-ellipsis">运行点检是否做完：{{item.IsRunDone==true?'是':'否'}}</view>

					</view>
					<view class="text-black cu-form-data-10 uni-ellipsis" style="width: 40%; display: flex; flex-direction: column; justify-content: space-between;">
						<uni-tag text="跳转生成点检单" inverted="true" type="warning"  	@click="getDetailInfo(item.EquipmentId,2)"/>

					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import vTabs from '@/components/v-tabs/v-tabs.vue'
	export default {
		components: {
			uniTag,
			UniIcons,
			vTabs
		},
		data() {
			return {
				obj: [],
				searchValue: ''
			}
		},
		onLoad() {
			this.query()
		},
		onShow() {
			this.query()
		},
		methods: {
			// 查询信息列表
			query() {
				this.request({
					url: '/EquipMaintCheckMain/GetPDAEquipMaintOperationCheck',
					method: 'GET',
					data: {
						inPageSize: 20,
						inPageIndex: 1,
						key: this.searchValue
					},
					success: res => {
						this.obj = res.response.data;
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 创建基础点检
			getDetailInfo(id,type) {
				uni.navigateTo({
					url: './detail?Id=' + id+'&type='+type
				})
			},// 创建运行点检
			getDetailFull(id,equipmentId) {
				uni.navigateTo({
					url: './detailFull?Id=' + id+'&EquipmentId='+equipmentId
				})
			},
		}
	}
</script>
