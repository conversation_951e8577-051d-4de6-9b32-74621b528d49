<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<view class="title">设备编码</view>
				<input v-model="equipNo" @confirm="getEquipNo()" @blur="getEquipNo()" :focus="focus" />
				<uni-icons class="uni-panel-icon uni-icon alin_x_center" type="scan" @click="showScan()" color='#8f8f94'
					size="25" />
			</view>
			<view class="cu-form-group">
				<view class="title">设备名称</view>
				<input v-model="equipDesc" disabled="true" />
				<!-- <uni-icons class="uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" /> -->
			</view>
			<view class="cu-bar bg-white solid-bottom margin-top">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 故障现象
				</view>
			</view>
			<view class="cu-form-group">
				<textarea v-model="text" placeholder="请输入故障现象" :focus="!focus" />
			</view>
			<view class="cu-form-group">
				<view class="title">需求工种</view>
				<uni-data-select style="background-color: #FFFFFF;" v-model="value" :localdata="range" @change="change"
					label="工种选择"></uni-data-select>
			</view>
			<view class="cu-form-group">
				<view class="title">计划时间</view>
				<!-- <picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange">
					<view style="color: red;">{{date}}</view>
				</picker> -->
				<picker mode="multiSelector" :value="dateTime" @change="changeDateTime"
					style="width: 100%;min-height:30px;" @columnchange="changeDateTimeColumn" :range="dateTimeArray">
					<view class='lableBox' style='width: 100%;'>
						<view style="color: red;width: 100%;" class="otherCheck">{{timeStr}}</view>
					</view>
				</picker>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">报修人</view>
				<input v-model="userName" @confirm="getUser()" disabled="true" />
			</view>
			<view class="cu-form-group">
				<view class="title">报修人电话</view>
				<input v-model="phoneNumber" />
			</view>
			<view class="cu-form-group margin-top">
				<uni-tag text="清空" type="warning" @click="cancel" />
				<uni-tag text="确认报修" type="success" @click="submit()" />
			</view>
		</view>
	</view>
</template>

</view>
<view class="cu-form-group margin-top">
	<view class="title">报修人</view>
	<input v-model="userName" @confirm="getUser()" disabled="true" />
</view>
<view class="cu-form-group">
	<view class="title">报修人电话</view>
	<input v-model="phoneNumber" />
</view>
<view class="cu-form-group margin-top">
	<uni-tag text="清空" type="warning" @click="cancel" />
	<uni-tag text="确认报修" type="success" @click="submit()" />
</view>
</view>
</view>
</template>

<script>
	const {
		dateTimePicker,
		getMonthDay,
		generateTimeStr
	} = require('../../../common/js/dateTimePicker.js');

	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	export default {
		components: {
			uniTag
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				// 是否展示 设备信息
				equipShow: false,
				// 需求工种
				needType: '',
				// 时间
				date: currentDate,
				// 工种选择框
				/* 扩展组件 下拉框 */
				range: [],
				value: '',
				repairNo: '',
				// 设备信息
				equipId: null,
				equipNo: '',
				equipDesc: '',
				// 
				repairSource: '',
				repairSourceNo: '',
				// 故障信息
				text: '',
				// 报修人员信息
				userId: null,
				userName: '',
				phoneNumber: '',
				// 焦点
				focus: true,
				dateTime: null,
				dateTimeArray: null,
				startYear: 2020,
				timeStr: ''
			}
		},
		onLoad() {
			this.clearCach();
			this.initTime();
		},
		onShow() {
			this.getNeedType();
			this.getUserInfo();
			uni.getStorage({
				key: 'equipInfo',
				success: o => {
					this.equipId = o.data.EquipmentId;
					this.equipNo = o.data.EquipmentCode;
					this.equipDesc = o.data.EquipmentName;
					this.equipShow = true;
				}
			})
		},
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		computed: {
			/* date picker */
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			// 确认报修
			submit() {
				if (this.equipId == null) {
					uni.showToast({
						title: '请扫描设备编码',
						icon: 'loading'
					});
					return;
				}
				if (this.text == '') {
					uni.showToast({
						title: '请输入故障现象',
						icon: 'loading'
					});
					return;
				}
				if (this.userId == null) {
					uni.showToast({
						title: '人员信息未获取到',
						icon: 'loading'
					});
					return;
				}
				if (this.phoneNumber == '') {
					uni.showToast({
						title: '请输入电话',
						icon: 'loading'
					});
					return;
				}
				if (this.value == '') {
					uni.showToast({
						title: '请选择工种',
						icon: 'loading'
					});
					return;
				}
				if (this.timeStr == '') {
					uni.showToast({
						title: '请选择计划时间',
						icon: 'loading'
					});
					return;
				}
				this.request({
					url: '/EquipRepairMain/Post',
					method: 'POST',
					data: {
						// 设备ID
						EquipId: this.equipId,
						// 故障现象
						RepairfaultSituation: this.text,
						// 报修人（需ID）
						RRepairUseId: this.userId,
						// 电话
						Phone: this.phoneNumber,
						// 需求工种
						DemandType: this.value,
						// 计划维修时间
						StartTime: this.timeStr
					},
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'loading',
							duration: '3000'
						});
						this.text = '';
						this.value = '';
						this.equipNo = '';
						this.equipDesc = '';
						this.focus = true;
						this.equipShow = false;
						this.clearCach();
						uni.showToast({
							icon: 'none',
							title: '报修成功！',
						});
						setTimeout(function() {
							uni.navigateBack({
								delta: 1, //返回层数，2则上上页
							})
						}, 2000)


					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
							success: function(res) {
								if (res.confirm) {
									console.log('用户点击确定');
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						});
					}
				})
			},
			// 获取登录人员信息
			getUserInfo() {
				let user = uni.getStorageSync('user');
				this.request({
					url: '/EquipRepairMain/GetSysUser',
					method: 'GET',
					data: {
						acount: user
					},
					success: res => {
						this.userId = res.response.Id;
						this.userName = res.response.UserName;
						this.phoneNumber = res.response.PhoneNo;
					}
				})
			},
			// 获取需求工种
			getNeedType() {
				this.request({
					url: '/EquipRepairMain/GetRequireOccupations',
					method: 'GET',
					success: res => {
						this.range = [];
						for (let i = 0; i < res.response.length; i++) {
							let temp = {
								text: res.response[i].RequiredName,
								value: res.response[i].Id,
							};
							this.range.push(temp);
						}
					},
					error: res => {
						this.msg = res.msg;
						this.cancel();
					}
				})
			},
			// 获取设备信息
			getEquipId() {
				this.equipId = '';
				this.equipNo = '';
				this.equipDesc = '';
				this.equipShow = false;
				this.clearCach();
				uni.navigateTo({
					url: '../../eam/common/equipment/equipment'
				})
			},
			// 通过设备编码获取设备信息
			getEquipNo() {
				let that = this;
				if (this.equipNo == '') {
					uni.showToast({
						title: '请先扫描设备编码',
						icon: 'loading'
					})
					return;
				} else {
					that.request({
						url: '/EquipmentInfo/Get',
						method: 'GET',
						data: {
							equipmentNameOrCode: this.equipNo,
							inPageSize: 9999,
							inPageIndex: 1
						},
						success: res => {
							if (res.response.data.length === 0) {
								uni.showToast({
									title: '未查找到该编码设备',
									icon: 'error'
								})
								this.equipNo = '';
								this.equipDesc = '';
								this.focus = true;
								return;
							}

							let idx = res.response.data.findIndex(t => t.EquipmentCode == this.equipNo)
							if (idx == -1) {
								uni.showToast({
									title: '未查找到该编码工装',
									icon: 'error'
								})
								this.equipNo = '';
								this.equipDesc = '';
								this.focus = true;
								return;
							}
							that.equipId = res.response.data[idx].Id;
							that.equipNo = res.response.data[idx].EquipmentCode;
							that.equipDesc = res.response.data[idx].EquipmentName;

							that.equipShow = true;
							that.focus = false;
						}
					})
				}
			},
			// 初始化
			cancel() {
				let that = this;
				that.equipNo = '';
				that.equipDesc = '';
				that.text = '';
				that.value = 0;
				that.timeStr = '';
				that.phoneNumber = '';
				uni.showToast({
					title: '清空成功',
				})
			},
			// 清楚缓存
			clearCach() {
				uni.removeStorage({
					key: 'equipInfo',
					success() {
						console.log("设备缓存清理成功！");
					}
				})
			},
			initTime() {
				let date = new Date();
				let endYear = date.getFullYear() + 1;
				// 获取完整的年月日 时分秒，以及默认显示的数组
				let obj = dateTimePicker(this.startYear, endYear);
				// 精确到分的处理，将数组的秒去掉
				// let lastArray = obj.dateTimeArray.pop();
				// let lastTime = obj.dateTime.pop();

				this.dateTimeArray = obj.dateTimeArray
				this.dateTime = obj.dateTime
			},
			/* date picker */
			changeDateTime(e) {
				this.dateTime = e.detail.value;
				this.timeStr = generateTimeStr(this.dateTimeArray, this.dateTime);
				//ios时间不能用'-'解析成时间戳
			},
			/*年,月切换时重新更新计算*/
			changeDateTimeColumn(e) {
				//let {id} = e.target;
				let {
					column,
					value
				} = e.detail;
				if (column == 0 || column == 1) {
					//直接修改数组下标视图不更新,用深拷贝之后替换数组
					let dateTime = JSON.parse(JSON.stringify(this.dateTime));
					let dateTimeArray = JSON.parse(JSON.stringify(this.dateTimeArray));
					dateTime[column] = value;
					dateTimeArray[2] = getMonthDay(dateTimeArray[0][dateTime[0]], dateTimeArray[1][dateTime[1]]);
					this.dateTime = dateTime;
					this.dateTimeArray = dateTimeArray;
				}
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			/* select */
			change() {

			},
			showScan() {
				let self = this;
				// 允许从相机和相册扫码
				uni.scanCode({
					success: function(res) {
						self.equipNo = res.result;
					}
				});

			}
		}
	}
</script>

<style>

</style>