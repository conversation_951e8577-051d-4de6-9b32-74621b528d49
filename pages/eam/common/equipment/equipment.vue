<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<uni-easyinput v-model="EquipmentName" focus placeholder="请输入搜索条件" @input="getEquipList"></uni-easyinput>
			</view>
			<radio-group @change="radioChange">
				<view class="uni-list">
					<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in obj"
						:key="index">
						<view class="cu-form-group solid-bottom panel-full">
							<view class="content">
								<view class="cu-form-data-15">设备ID: {{item.EquipmentId}}</view>
								<view class="cu-form-data-15">设备编码: {{item.EquipmentCode}}</view>
								<view class="cu-form-data-15">设备名称: {{item.EquipmentName}}</view>
							</view>
							<view>
								<radio :value="index + ''" :checked="item.EquipmentId === EquipmentId" />
							</view>
						</view>
					</view>
				</view>
			</radio-group>
		</view>
	</view>
</template>

<script>
	export default {
		onLoad() {
			this.getEquipList();
		},
		data() {
			return {
				EquipmentId: '',
				EquipmentName:'',
				obj: []
			}
		},
		methods: {
			getEquipList(){
				this.request({
					url: '/EquipMaintRecordMain/Get',
					method: 'GET',
					data: {
						equipmentName: this.EquipmentName,
						inPageSize: 9999,
						inPageIndex: 1
					},
					success: res => {
						this.obj = res.response.data;
					},
					error: res =>{
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
						});
					}
				})
			},
			radioChange(evt) {
				var i = parseInt(evt.detail.value);
				var EquipmentId = this.obj[i].EquipmentId;
				var EquipmentCode = this.obj[i].EquipmentCode;
				var EquipmentName = this.obj[i].EquipmentName;
				uni.setStorage({
					key: "equipInfo",
					data: {
						"EquipmentId": EquipmentId,
						"EquipmentCode": EquipmentCode,
						"EquipmentName": EquipmentName
					}
				})
				uni.navigateBack({
					animationDuration: 1000
				})
			}
		}
	}
</script>

