<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="uni-flex uni-column uni-bg-white uni-common-pl">
				<view class="uni-flex-item uni-ellipsis">报修人姓名：{{repairDetail.RRepairUser}}</view>
				<view class="uni-flex-item uni-ellipsis">报修人电话：{{repairDetail.Phone}}</view>
				<view class="uni-flex-item uni-ellipsis">报修时间：{{repairDetail.CreateTime}}</view>
				<view class="uni-flex-item uni-ellipsis">设备名称：{{repairDetail.EquipmentName}}</view>
				<view class="uni-flex-item uni-ellipsis">事业部：{{repairDetail.OrgName}}</view>
				<view class="uni-flex-item uni-ellipsis">车间：{{repairDetail.DepartmentName}}</view>
				<view class="uni-flex-item uni-ellipsis">产线：{{repairDetail.LineName}}</view>
				<view class="uni-flex-item uni-ellipsis">故障描述：{{repairDetail.RepairfaultSituation}}</view>
				<view class="uni-flex-item uni-ellipsis">计划维修时间：{{repairDetail.StartTime}}</view>
				<view class="uni-flex-item uni-ellipsis">需求工种：{{repairDetail.RequiredName}}</view>
				<view class="uni-flex-item uni-ellipsis">安排维修工：{{repairDetail.TaskAccepter}}</view>
				<view class="uni-flex-item uni-ellipsis">派工时间：{{repairDetail.TaskAcceptDate}}</view>
				<view class="uni-flex-item uni-ellipsis">计划完成时间：{{repairDetail.PlanTime}}</view>

			</view>

			<view class="cu-form-group margin-top">
				<view class="title">故障类别</view>

				<uni-data-select style="background-color: #FFFFFF;" v-model="value" :localdata="range" @change="change"
					v-if="Status=='StartRepair'" />
				<input disabled="true" v-if="Status!='StartRepair'" placeholder="请选择" />


			</view>
			<view class="cu-form-group solid-bottom" v-if="ChoiceState==0">
				<view style="width: 28%;">是否需要备品</view>
				<uni-data-checkbox @change="changeSpare" mode="button" :disabled="Status=='Assigned'"
					v-model="checkValue" :localdata="isSpareType" />
			</view>
			<view class="cu-form-group solid-bottom" @click="ChoiceSpare" :disabled="Status=='Assigned'"
				v-if="checkValue==1&&ChoiceState==0">
				<view class="title">备品</view>
				<input disabled="true" placeholder="请选择备品" />
			</view>
			<view class="cu-form-group solid-bottom" v-if="checkValue==1&&ChoiceState!=0">
				<view class="title">备品</view>
			</view>
			<view class="margin-top"></view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in spareList"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full">
					<view class="content">
						<view class="uni-ellipsis">备品编码：{{item.ChoiceCode}} </view>
						<view class="uni-ellipsis">备品名称：{{item.ChoiceName}} </view>
					</view>
					<view class="text-black cu-form-data-10 uni-ellipsis" style="20%" v-if="ChoiceState==0">
						<uni-tag size="small" text="删除" :inverted="true" type="error" @click="DelChoice(index)" />

					</view>
				</view>
			</view>

			<!-- <view class="cu-form-group" @click="queryOrder()" v-if="isShow">
				<view class="iconfont xj-gongdan xj_form_icon" />
				<view class="title"><text class="text-red">*</text>源工单号</view>
				<input v-model="wo" disabled="true" placeholder="请手动选择工单" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="25" />
			</view> -->

			<view class="cu-form-group solid-bottom text-right" v-if="checkValue==1">
				<uni-tag text="申请" type="warning" :disabled="ChoiceState!=0" @click="applyOfSpare" />
				<uni-tag text="完成" type="success" :disabled="ChoiceState!=1" @click="confirmOfSpare" />
			</view>
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 故障原因分析
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<textarea v-model="analysis" :disabled="Status!='StartRepair'" placeholder="请填写故障原因分析" />
			</view>
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 维修处理情况
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<textarea v-model="dealRes" :disabled="Status!='StartRepair'" placeholder="请填写维修处理情况" />
			</view>
			
			
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-orange"></text> 实际维修时间（h）
				</view>
			</view>
			<view class="cu-form-group solid-bottom">
				<uni-number-box :value="realDealTime" @change="dealTimeChange" :disabled="Status!='StartRepair'" :max='9999' :min='0'/>
			</view>
			<view class="cu-form-group margin-top">
				<uni-tag text="接收" :disabled="Status!='Assigned'" type="warning" @click="StartReceive" />
				<uni-tag text="开始维修" :disabled="Status!='Receive'" type="warning" @click="StartRepair" />
				<uni-tag text="保存" :disabled="Status!='StartRepair'" type="warning" @click="submit" />
				<uni-tag text="维修完成" :disabled="Status!='StartRepair'" type="success" @click="submitConfirm" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	import UniNumberBox from '@/components/uni-ui/uni-number-box/uni-number-box.vue'

	export default {
		components: {
			uniTag,
			UniIcons,
			UniNumberBox,
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				// 
				isEdit: false,
				// 确认备品
				isConfirm: true,
				repairDetail: {},
				date: null,
				id: null,
				// 故障原因分析
				analysis: '',
				// 处理情况
				dealRes: '',
				/* 扩展组件 下拉框 */
				// 故障类别
				range: [],
				value: '',
				// 备品需求
				// 退料类型
				isSpareType: [{
					text: '不需要',
					value: 0
				}, {
					text: '需要',
					value: 1
				}],
				rangeSpare: [],
				valueSpare: 0,
				//Assigned：已派工    Receive：已接收   StartRepair：维修中  RepairOK：维修完成
				Status: 'Assigned',
				//是否需要备件
				Ischoice: 0,
				//0：未申请  1：已申请  2：已完成 
				ChoiceState: 0,
				FaultType: 0,
				checkValue: 0,
				// 实际处理时长
				realDealTime: 0,
			}
		},
		onLoad(e) {
			this.id = e.id;
			this.queryRepairDetail(e.id);
			this.queryFaultList();
			this.querySparePartList();
			this.$store.commit('spareChecks/empty')

		},
		computed: {
			repairUser() {
				return this.$store.state.repairUser.repairUser.repairUserName
			},
			spareList() {
				return this.$store.state.spareChecks.spareChecks
			},
		},
		methods: {
			dealTimeChange(val){
				this.realDealTime = val
			},
			submitConfirm() {
				const app = this;
				app.req();
				if (app.ChoiceState == 1) {
					app.$error('配件申请中，无法完成维修')
				} else if (app.value == 0) {
					app.$error('请选择故障类型')
				} else if (app.analysis == '') {
					app.$error('请填写故障原因分析')
				} else if (app.dealRes == '') {
					app.$error('请填写维修处理情况')
				} else if (app.realDealTime <= 0) {
					app.$error('实际维修时间为0无法完成')
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要完成维修吗？', function(e) {
						if (e.index == 0) {
							app.reqConfirm()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要完成维修吗？',
						success: function(res) {
							if (res.confirm) {
								app.reqConfirm()
							} else if (res.cancel) {
								// 执行逻辑
							}
						}
					});
					// #endif
				}
			},
			submit() {
				const app = this;
				app.req();
			},
			
			req() {
				this.request({
					url: '/EquipRepairMain/UpdateDetailSave',
					method: 'PUT',
					data: {
						Id: this.repairDetail.RepairDetailId,
						FaultType: this.value || 0,
						RepairfaultReason: this.analysis || '',
						RepairProcessing: this.dealRes || '',
						RealityTime: Number(this.realDealTime) || 0
					},
					success: res => {
						//this.queryRepairDetail(this.id)
						this.$toast(res.msg)
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			reqConfirm() {
				this.request({
					url: '/EquipRepairMain/RepairOK?id=' + this.repairDetail.Id,
					method: 'PUT',
					// data: {
					// 	Id: this.repairDetail.Id,
					// },
					success: res => {
						this.$toast(res.msg)
						setTimeout(function() {
							uni.navigateBack({
								delta: 1, //返回层数，2则上上页
							})
						}, 2000);
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 
			cancel() {

			},
			// 备品完成
			confirmOfSpare() {
				this.request({
					url: '/EquipRepairMain/SparePlace?id=' + Number(this.id),
					method: 'PUT',
					success: res => {
						this.queryRepairDetail(this.id)
						this.$toast(res.msg)
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 备品申请
			applyOfSpare() {
				const app = this;
				if (!app.spareList || app.spareList.length == 0) {
					app.$error('请选择需要的备件物品')
					return;
				}
				var data = []
				app.spareList.forEach((current, index) => {
					data.push(current.Id);
				});

				app.request({
					url: '/EquipRepairMain/SpareApply?id=' + Number(this.id),
					method: 'PUT',
					data,
					success: res => {
						app.req();
					},
					error: res => {
						app.$error(res.msg)
					}
				})
			},
			// 查询备品列表
			querySparePartList() {
				this.request({
					url: '/EquipChoice/GetEquipChoice',
					method: 'GET',
					success: res => {
						console.log(res)
						let temp = {};
						for (let i = 0; i < res.response.length; i++) {
							if (res.response[i].Id != this.valueSpare) {
								temp = {
									text: res.response[i].ChoiceName,
									value: res.response[i].Id,
									disable: true
								};
							} else {
								temp = {
									text: res.response[i].ChoiceName,
									value: res.response[i].Id
								};
							}
							this.rangeSpare.push(temp);
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询故障类别
			queryFaultList() {
				this.request({
					url: '/EquipRepairMain/GetFaultList',
					method: 'GET',
					success: res => {
						this.range = [];
						for (let i = 0; i < res.response.length; i++) {
							let temp = {};
							if (res.response[i].Id != this.value) {
								temp = {
									text: res.response[i].ItemName,
									value: res.response[i].Id,
									disable: false
								}
							} else {
								temp = {
									text: res.response[i].ItemName,
									value: res.response[i].Id
								}
							}
							this.range.push(temp);
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			// 查询维修工单明细
			queryRepairDetail(id) {
				const app = this;
				this.request({
					url: '/EquipRepairMain/Get/' + id,
					method: 'GET',
					success: res => {
						this.repairDetail = res.response;
						app.Status = res.response.Status;
						app.checkValue = res.response.Ischoice ? 1 : 0;
						app.ChoiceState = res.response.ChoiceState;
						app.ChoiceState = res.response.ChoiceState;
						// if (app.ChoiceState==) {
						// 	app.isEdit = true;
						app.analysis = res.response.RepairfaultReason;
						app.value = res.response.FaultType;
						// 	app.valueSpare = app.repairDetail.ChoiceId;
						app.realDealTime = res.response.RealityTime
						app.dealRes = res.response.RepairProcessing;
						// 	app.queryFaultList();
						// 	app.querySparePartList();
						// }
						if (res.response.Ischoice) {
							app.GetEquipChoiceList();
						}
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			change(value) {

			},
			// 是否需要备品
			changeSpare(e) {

			},
			/* date picker */
			bindDateChange: function(e) {
				this.date = e.detail.value
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			StartReceive() {
				var self = this;
				//接收
				this.request({
					url: '/EquipRepairMain/StartReceive?id=' + this.repairDetail.Id,
					method: 'PUT',
					success: res => {
						this.$toast(res.msg)
						setTimeout(function() {
							self.queryRepairDetail(self.repairDetail.Id)
						}, 1500)
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			StartRepair() {
				//开始维修
				var self = this;
				this.request({
					url: '/EquipRepairMain/StartRepair?id=' + this.repairDetail.Id,
					method: 'PUT',
					success: res => {
						this.$toast(res.msg)
						setTimeout(function() {
							self.queryRepairDetail(self.repairDetail.Id)
						}, 1500)
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			ChoiceSpare() {
				uni.navigateTo({
					url: '../../prd/common/SpareList/SpareList'
				})
			},
			DelChoice(index) {
				this.spareList.splice(index, 1);
			},
			GetEquipChoiceList() {
				const app = this;
				this.request({
					url: '/EquipRepairMain/GetEquipChoiceList?id=' +  app.repairDetail.Id,
					method: 'GET',
					success: res => {
						this.$store.commit('spareChecks/empty')
						this.$store.commit('spareChecks/setSpareChecks',res.response)
					},
					error: res => {
						this.$error(res.msg)
					}
				})
			},
			
		}
	}
</script>
