<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="title">保养单号：</view>
				<input class="ds_center" v-model="maintenanceNo" disabled="true"/>
			</view>
			<view class="cu-form-group">
				<view class="title">设备编码：</view>
				<input class="ds_center" v-model="equipNo"  disabled="true"/>
			</view>
			<view class="cu-form-group">
				<view class="title">设备名称：</view>
				<input class="ds_center" v-model="equipDesc" disabled="true"/>
			</view>
			<view class="cu-form-group">
				<view class="title">计划保养时间：</view>
				<input class="ds_center" v-model="planMaintenanceDate" disabled="true"/>
			</view>
			<view class="cu-form-group margin-top">
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="list" color='#8f8f94' size="25" />
				<view class="title">保养项目</view>
			</view>
			<view class="uni-list-cell " hover-class="uni-list-cell-hover" v-for="(item,index) in maintenanceItem"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full " @click="getDetailInfo(item.Id,item.IsPassDetail)">
					<view class="content">
						<text class="cuIcon-title text-orange "></text>{{item.MaintanceContent}}
					</view>
					<view class="cu-form-group">
						<uni-tag size="small" text="完成" :inverted="true" type="success" v-show="item.IsPassDetail === 1" />
						<uni-tag size="small" text="未完成" :inverted="true" type="warning" v-show="item.IsPassDetail === 0"/>
						<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94' size="20" />
					</view>
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">保养结果：</view>
				<view class="#">
					<picker @change="bindPickerChange" :value="index" :range="type">
						<view class="#">{{type[index]}}</view>
					</picker>
				</view>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowdown" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="title">保养人：</view>
				<input style="text-align: center;" v-model="userName" @confirm="getRepairNo()" disabled="true"/>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="auth" color='#8f8f94' size="25" />
			</view>
			<view style="height: 100upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom">
				<uni-tag text="确认" type="success" @click="submit" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				maintenanceNo: '',
				equipNo: '',
				equipDesc: '',
				planMaintenanceDate: '',
				maintenanceItem: [],
				type:['不通过','通过'],
				index:1,
				userName:'',
				// 保养计划 ID
				Id:null
			}
		},
		onLoad(e) {
			console.log(e);
			this.Id = e.Id;
			this.equipNo = e.equipNo;
			this.equipDesc = e.equipDesc;
			this.userName = uni.getStorageSync('user');
			this.queryMaintenanceDetail();
		},
		methods: {
			// 查询保养计划明细
			queryMaintenanceDetail(){
				this.request({
					url: '/EquipMaintRecordMain/GetRecordMainDetail/' + this.Id,
					method: 'GET',
					success: res => {
						this.maintenanceNo = res.response.RecordNo 
						this.planMaintenanceDate = res.response.PlanTime
						this.maintenanceItem = res.response.StandardDetails;
					}
				})
			},// 获取任务详情
			getDetailInfo(id,IsPassDetail) {
				if(IsPassDetail == 1){
					uni.showToast({
						title:'已完成保养',
						icon: 'loading'
					})
				} else {
					uni.navigateTo({
						url: 'MaintenanceItemDetail?Id=' + id + '&equipNo=' + this.equipNo + '&maintenanceNo=' + this.maintenanceNo + '&equipDesc=' + this.equipDesc
					})
				}
			},
			/* picker */
			bindPickerChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			}
		}
	}
</script>

<style>

</style>
