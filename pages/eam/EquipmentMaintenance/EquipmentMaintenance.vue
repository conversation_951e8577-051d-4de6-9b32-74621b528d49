<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<!-- 			<view class="cu-form-group margin-top">
				<view class="title">设备ID：</view>
				<input v-model="equipId" disabled="true"/>
			</view> -->
			<view class="cu-form-group">
				<view class="title">设备编码：</view>
				<input v-model="equipNo" @confirm="getEquipNo()" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" @click="showScan" color='#8f8f94'
					size="25" />
			</view>
			<view class="cu-form-group">
				<view class="title">设备名称：</view>
				<input v-model="equipDesc" @confirm="getEquipId()" />
				<uni-icons @click="getEquipId()" class=" uni-panel-icon uni-icon alin_x_center" type="search"
					color='#8f8f94' size="25" />
			</view>
			<view class="margin-top"></view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in equipList"
				:key="index">
				<view class="cu-form-group solid-bottom panel-full " @click="getDetailInfo(item.Id)">
					<!-- <view style="width: 20%; ">
						<image style="width: 50px; height: 50px;" :src="item.image"></image>
					</view> -->
					<view style="width: 70%;">
						<view class="text-black cu-form-data-10">保养编码：{{item.MaintanceCode}}</view>
						<view class="text-black cu-form-data-10">保养计划：{{item.MaintancePlan}}</view>
						<view class="text-black cu-form-data-10">保养内容：{{item.MaintanceContent}}</view>
					</view>
					<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowright" color='#8f8f94'
						size="25" />
				</view>
			</view>
			<view style="height: 100upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom">
				<uni-tag text="查询" type="success" @click="getEquipMaintenance()" />
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				equipId: '',
				equipNo: '',
				equipDesc: '',
				equipList: []
			}
		},
		onShow() {
			uni.getStorage({
				key: 'equipInfo',
				success: o => {
					this.equipId = o.data.EquipmentId;
					this.equipNo = o.data.EquipmentCode;
					this.equipDesc = o.data.EquipmentName
				}
			})
		},
		onPullDownRefresh() {
			this.cancel()
			uni.stopPullDownRefresh()
		},
		methods: {
			getEquipId() {
				uni.removeStorage({
					key: 'equipInfo',
					success() {
						console.log("设备缓存清理成功！");
					}
				})
				uni.navigateTo({
					url: '../../eam/common/equipment/equipment?EquipmentName=' + this.equipDesc
				})
			},
			getEquipNo() {
				this.request({
					url: '/EquipMaintRecordMain/Get',
					method: 'GET',
					data: {
						equipmentCode: this.equipNo,
						inPageSize: 9999,
						inPageIndex: 1
					},
					success: res => {
						this.equipId = res.response.data[0].EquipmentId;
						this.equipNo = res.response.data[0].EquipmentCode;
						this.equipDesc = res.response.data[0].EquipmentName;
					}
				})
			},
			getEquipMaintenance() {
				this.request({
					url: '/EquipMaintPlanMain/GetEquipMaintPlanById/' + this.equipId,
					method: 'GET',
					success: res => {
						this.equipList = res.response;
					}
				})
			},
			// 初始化
			cancel() {
				this.equipId = '';
				this.equipNo = '';
				this.equipDesc = '';
				this.equipList = [];
			},
			// 获取报修任务详情
			getDetailInfo(id) {
				uni.navigateTo({
					url: 'MaintenanceDetail?Id=' + id + '&equipNo=' + this.equipNo + '&equipDesc=' + this.equipDesc
				})
			},
			showScan() {
				let self = this;
				// 允许从相机和相册扫码
				uni.scanCode({
					success: function(res) {
						self.equipNo = res.result;
						self.getEquipNo()
					}
				});

			}
		}
	}
</script>

<style>

</style>
