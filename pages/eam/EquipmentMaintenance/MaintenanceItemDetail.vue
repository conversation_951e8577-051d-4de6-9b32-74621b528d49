<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="title">保养单号：</view>
				<input v-model="repairNo" disabled="true"/>
			</view>
			<view class="cu-form-group">
				<view class="title">设备编码：</view>
				<input v-model="equipNo" disabled="true"/>
			</view>
			<view class="cu-form-group">
				<view class="title">设备名称：</view>
				<input v-model="equipDesc" disabled="true"/>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">保养项目：</view>
				<input v-model="maintenanceItem" disabled="true"/>
			</view>
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-orange "></text> 备注
				</view>
			</view>
			<view class="cu-form-group">
				<textarea v-model="note" placeholder="输入字体" />
			</view>
			<!-- 备件 -->
			<!-- <radio-group class="cu-form-group" @change="radioChange">
				<label class="cu-form-group" v-for="(item, index) in items" :key="item.value">
					<view>{{item.name}}&nbsp;&nbsp;&nbsp;</view>
					<view>
						<radio :value="item.value" :checked="index === current" />
					</view>
				</label>
			</radio-group>
			<view class="cu-form-group" v-show="current === 0">
				<view class="title">备件编号：</view>
				<input v-model="spareNo" />
			</view>
			<view class="cu-form-group" v-show="current === 0">
				<view class="title">备件名称：</view>
				<input v-model="spareDesc" />
			</view>
			<view class="cu-form-group" v-show="current === 0">
				<view class="title">备件数量：</view>
				<view class="detail-left">
					<uni-number-box :value="spareQty" @change="change" />
				</view>
			</view>
			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in reserve" :key="index">
				<view class="cu-form-group solid-bottom panel-full " >
					<view style="width: 20%; ">
						<image style="width: 50px; height: 50px;" :src="item.image"></image>
					</view>
					<view style="width: 70%;">
						<strong>
							<view class="text-black cu-form-data-10">{{item.reserveName}}</view>
						</strong>
						<view class="text-gray cu-form-data-10">{{item.reserveNo}}</view>
					</view>
					<view class="text-gray cu-form-data-10">{{item.qty}}</view>
				</view>
			</view> -->
			<!-- 上传图片 -->
			<view class="group">
				<view class="uni-list list-pd">
					<view class="uni-list-cell cell-pd">
						<view class="uni-uploader">
							<view class="uni-uploader-head">
								<view class="uni-uploader-title">点击可预览选好的图片</view>
								<view class="uni-uploader-info">{{imageList.length}}/1</view>
							</view>
							<view class="uni-uploader-body">
								<view class="uni-uploader__files">
									<block v-for="(image,index) in imageList" :key="index">
										<view class="uni-uploader__file">
											<image class="uni-uploader__img" :src="image" :data-src="image"
												@tap="previewImage"></image>
										</view>
									</block>
									<view class="uni-uploader__input-box">
										<view class="uni-uploader__input" @tap="chooseImage"></view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 结果 -->
			<view class="cu-form-group">
				<view class="title">保养结果：</view>
				<view class="#">
					<picker @change="bindPickerChange" :value="index" :range="type">
						<view class="#">{{type[index]}}</view>
					</picker>
				</view>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowdown" color='#8f8f94' size="25" />
			</view>
			<view style="height: 150upx;">
				<!-- 撑一下 -->
			</view>
			<view class="uni-fixed-bottom" v-show="index === 1">
				<uni-tag text="确认" type="success" @click="submit" />
			</view>
			<view class="uni-fixed-bottom cu-form-group" v-show="index === 0">
				<uni-tag text="设备报修" type="warning" @click="getEquipMaintenance()"/>
				<uni-tag text="确认" type="success" @click="submit" />
			</view>
		</view>
	</view>
</template>

<script>
	var sourceType = [
		['camera'],
		['album'],
		['camera', 'album']
	]
	var sizeType = [
		['compressed'],
		['original'],
		['compressed', 'original']
	]
	import permision from "@/common/js/permission.js"
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue';
	import UniNumberBox from '@/components/uni-ui/uni-number-box/uni-number-box.vue';
	import {
		pathToBase64,
		base64ToPath,
		base64toFile,
		parseBlob
	} from '@/js/image-tools.js';
	export default {
		components: {
			uniTag,
			UniIcons,
			UniNumberBox
		},
		data() {
			return {
				// 备件
				items: [{
						value: 'true',
						name: '需要备件'
					},
					{
						value: 'false',
						name: '不需要备件'
					}
				],
				current: 0,
				spareNo: '',
				spareDesc: '',
				spareQty: '',
				reserve: [],
				// 保养
				repairId: null,
				repairNo: '',
				equipNo: '',
				equipDesc: '',
				maintenanceItem: '',
				maintenanceBase: '',
				maintenanceMethod: '',
				userTools: '',
				note: '',
				type: ['不通过', '通过'],
				index: 1,
				/* 图片参数 */
				imageList: [],
				imageDataList: [],
				sourceTypeIndex: 2,
				sourceType: ['拍照', '相册', '拍照或相册'],
				sizeTypeIndex: 2,
				sizeType: ['压缩', '原图', '压缩或原图'],
				// 上传的图片参数
				ImageName: '',
				ImageAdress:'',
				// 明细表Id
				Id:null
			}
		},
		onLoad(e) {
			this.repairId = e.Id;
			this.repairNo = e.maintenanceNo;
			this.equipNo = e.equipNo;
			this.equipDesc = e.equipDesc;
			this.getDetailInfo(e.Id);
			console.log('重新加载');
		},
		onShow() {
						console.log('重新加载');
		},
		methods: {
			// 报修
			getEquipMaintenance(){
				
			},
			// 获取维修明细
			getDetailInfo(id) {
				this.request({
					url: '/EquipMaintRecordMain/GetRecordDetail/' + id,
					method: 'GET',
					success: res => {
						this.maintenanceItem = res.response[0].MaintanceContent;
						this.Id = res.response[0].Id;
					}
				})
			},
			
			/* 加减处理 */
			change(value) {
				this.spareQty = value;
				let reserve = {
					image: '',
					reserveNo: this.spareNo,
					reserveName: this.spareDesc,
					qty: this.numberValue
				}
				this.reserve.concat(reserve)
			},
			/* 图片 */
			sourceTypeChange: function(e) {
				this.sourceTypeIndex = e.target.value
			},
			sizeTypeChange: function(e) {
				this.sizeTypeIndex = e.target.value
			},
			countChange: function(e) {
				this.countIndex = e.target.value;
			},
			chooseImage: async function() {
				const that = this				
				// #ifdef APP-PLUS
				// TODO 选择相机或相册时 需要弹出actionsheet，目前无法获得是相机还是相册，在失败回调中处理
				if (this.sourceTypeIndex !== 2) {
					let status = await this.checkPermission();
					if (status !== 1) {
						return;
					}
				}
				// #endif
				if (this.imageList.length === 1) {
					let isContinue = await this.isFullImg();
					console.log("是否继续?", isContinue);
					if (!isContinue) {
						return;
					}
				}
				uni.chooseImage({
					sourceType: sourceType[this.sourceTypeIndex],
					sizeType: sizeType[this.sizeTypeIndex],
					count: this.imageList.length > 0 ? 1 - this.imageList.length : 1,
					success: (res) => {
						this.imageList = this.imageList.concat(res.tempFilePaths);
						const tempFilePaths = res.tempFilePaths;
						uni.uploadFile({
							url: 'http://**************:8091/api/EquipMaintRecordMain/UploadFiles', 
							filePath: tempFilePaths[0],
							name: 'files',
							success: function(uploadFileRes) {
								let res = JSON.parse(uploadFileRes.data);
								that.ImageName = res.ImageName;
								that.ImageAdress = res.ImageAdress;
								console.log(that.ImageAdress);
							}
						});
					},
					
					fail: (err) => {
						// #ifdef APP-PLUS
						if (err['code'] && err.code !== 0 && this.sourceTypeIndex === 2) {
							this.checkPermission(err.code);
						}
						// #endif
					}
				})
			},
			submit() {
				// console.log(this.ImageAdress);
				this.request({
					url: '/EquipMaintRecordMain/UpdateSingle',
					method: 'POST',
					data: {
						Id: this.Id,
						IsPassDetail: this.index,
						Remark: this.note,
						ImageName: this.ImageName,
						ImageAdress: this.ImageAdress
					},
					success: res => {
						console.log(res);
					}
				})
			},
			isFullImg: function() {
				return new Promise((res) => {
					uni.showModal({
						content: "已经有1张图片了,是否清空现有图片？",
						success: (e) => {
							if (e.confirm) {
								this.imageList = [];
								this.imageDataList = [];
								res(true);
							} else {
								res(false)
							}
						},
						fail: () => {
							res(false)
						}
					})
				})
			},
			previewImage: function(e) {
				var current = e.target.dataset.src
				uni.previewImage({
					current: current,
					urls: this.imageList
				})
			},
			// 上传图片
			uploadImg(img) {
				this.request({
					url: '/EquipMaintRecordMain/UploadFiles',
					method: 'POST',
					data: {
						files: img
					},
					success: res => {
						console.log(res);
					}
				});
			},
			/* radio */
			radioChange(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			/* picker */
			bindPickerChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			}
		}
	}
</script>

<style>

</style>
