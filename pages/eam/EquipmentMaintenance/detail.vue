<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group">
				<view class="groupS">
					<text>设备编号：{{EquipmentCode}}</text>
					<text>设备名称：{{EquipmentName}}</text>
					<text>类型：{{MaintanceType=='FirstMaintenance'?'一级保养':'二级保养'}} </text>
				</view>
				
			</view>
			<view class="cu-form-group">
				<view class="title">保养结果：</view>
				<view style="width: 50%;">
					<picker @change="bindPickerChange" :value="index" :range="sType" style="width: 100%;" >
						<view >{{sType[index]}}</view>
					</picker>
				</view>
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="arrowdown" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="action">
					<text class="text-blue ">保养项目</text>
				</view>
			</view>
			<!-- 			<view class="uni-list-cell" hover-class="uni-list-cell-hover" v-for="(item,index) in MaintCheckDetailList"
				:key="index">
				<view class="cu-form-group padding panel-full ">
					<view class="groupS">
						<view class="action">
							{{index+1}}<text class="cuIcon-title text-blue "></text>{{item.MaintanceContent}}
						</view>
						<view class="action">
							详细计划<text class="cuIcon-title text-blue "></text>{{item.MaintancePlan}}
						</view>
						保养结果<view class="uni-px-5">
							<uni-data-checkbox mode="tag" @change="change(item.Id)" :localdata="res"
								v-model="item.IsCheckDetail"></uni-data-checkbox>
						</view>
						备注<view class="uni-px-5">
							<input v-model="item.Remark"/> 
						</view>
					</view>
				</view>
			</view> -->

			<view class="uni-list-cell margin-top" hover-class="uni-list-cell-hover"
				v-for="(item,index) in MaintCheckDetailList" :key="index">
				<view class="cu-form-group padding panel-full ">
					<view style="width: 100%; ">
						<view class="text-black cu-form-data-10"><span
								style="color: black;font-weight:bold;">保养内容：</span>{{item.MaintanceContent}}</view>
						<view class="text-black cu-form-data-10"><span
								style="color: black;font-weight:bold;">保养类型：</span>{{item.MaintanceType=='FirstMaintenance'?'一级保养':'二级保养'}}
						</view>
						<view class="text-black cu-form-data-10"><span
								style="color: black;font-weight:bold;float:left;">保养结果：</span><span style="float: left">
								<uni-data-checkbox mode="tag" @change="change(item.Id)" :localdata="res"
									v-model="item.IsPassDetail">
								</uni-data-checkbox>
							</span>
						</view>
						<view style="clear: both;"></view>
						<view class="text-black cu-form-data-10"><span
								style="color: black;font-weight:bold;float:left;">保养备注：</span><span
								style="float: left;width: 75%;"> <input v-model="item.Remark"
									style="width: 100%;" /></span>
						</view>
						<view style="clear: both;"></view>
					</view>

				</view>
			</view>
			<view class="group">
				<view class="uni-list list-pd">
					<view class="uni-list-cell cell-pd">
						<view class="uni-uploader">
							<view class="uni-uploader-head">
								<view class="uni-uploader-title">点击可预览选好的图片</view>
								<view class="uni-uploader-info">{{imageList.length}}/1</view>
							</view>
							<view class="uni-uploader-body">
								<view class="uni-uploader__files">
									<block v-for="(image,index) in imageList" :key="index">
										<view class="uni-uploader__file">
											<image class="uni-uploader__img" :src="image" :data-src="image"
												@tap="previewImage"></image>
										</view>
									</block>
									<view class="uni-uploader__input-box">
										<view class="uni-uploader__input" @tap="chooseImage"></view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="cu-form-group margin-top">
				<uni-tag text="取消" type="warning" @click="cancel" />
				<uni-tag text="完成保养" type="success" @click="submit()" />
			</view>
		</view>
	</view>
</template>
<script>
	var sourceType = [
		['camera'],
		['album'],
		['camera', 'album']
	]
	var sizeType = [
		['compressed'],
		['original'],
		['compressed', 'original']
	]
	import permision from "@/common/js/permission.js"
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import {
		pathToBase64,
		base64ToPath,
		base64toFile,
		parseBlob
	} from '@/js/image-tools.js';
	export default {
		components: {
			uniTag
		},
		data() {
			return {
				DepartmentName: '',
				LineName: '',
				EquipId: '',
				EquipmentCode: '',
				EquipmentName: '',
				MaintanceType:'',
				// 点检类型
				type: null,
				// 点检 ID
				Id: null,
				// 单选数据源
				res: [{
					text: '不通过',
					value: 0
				}, {
					text: '通过',
					value: 1
				}],
				// 点检内容
				MaintCheckDetailList: [],
				// slList:[],
				// swiper
				sType: ['不通过', '通过'],
				index: 0,
				/* 图片参数 */
				imageList: [],
				imageDataList: [],
				sourceTypeIndex: 2,
				sourceType: ['拍照', '相册', '拍照或相册'],
				sizeTypeIndex: 2,
				sizeType: ['压缩', '原图', '压缩或原图'],
				// 上传的图片参数
				ImageName: '',
				ImageAdress:'',
				// 明细表Id
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		onLoad(e) {
			this.Id = e.Id;
			this.EquipmentCode = e.EquipmentCode;
			this.EquipmentName = e.EquipmentName;
			this.MaintanceType = e.MaintanceType;
			this.getSI();
		},
		methods: {
			change(e) {
				// alert(e);
				// for(let i = 0; i < this.MaintCheckDetailList.length; i++){
				// 	if(this.MaintCheckDetailList[i].Id === e){
				// 		this.MaintCheckDetailList[i].IsCheckDetail = value;
				// 	}
				// }
			},
			one() {
				let that = this;
				for (let i = 0; i < this.MaintCheckDetailList.length; i++) {
					that.MaintCheckDetailList[i].IsCheckDetail = 2;
				}
			},
			// 获取点检项目
			getSI() {
				this.request({
					url: '/EquipMaintRecordMain/GetDetail/' + this.Id,
					method: 'GET',
					success: res => {

						this.MaintCheckDetailList = res.response;
					}
				})
			},
			// 取消
			cancel() {
				setTimeout(function() {
					uni.navigateBack({
						delta: 1, //返回层数，2则上上页
					})
				}, 500);
			},
			submit() {
				let that = this;
				// #ifdef APP-PLUS
				let platform = uni.getSystemInfoSync().platform;
				let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
				plus.nativeUI.confirm('确认要提交吗？', function(e) {
					if (e.index == 0) {
						that.reqSI()
					} else {

					}
				}, {
					"title": '提示',
					"buttons": btns,
				});
				// #endif
				// #ifdef H5
				uni.showModal({
					title: '提示',
					content: '确认要提交吗？',
					success: function(res) {
						if (res.confirm) {
							that.reqSI()
						} else if (res.cancel) {
							// 执行逻辑
						}
					}
				});
				// #endif

			},
			// 点检请求
			reqSI() {
				//let isResult = this.sType[this.index] === '通过' ? true : false;
				this.request({
					url: '/EquipMaintRecordMain/Put',
					method: 'Put',
					data: {
						Id: this.Id,
						IsPass: this.index,
						detailDto2s: this.MaintCheckDetailList
					},
					success: res => {
						this.$toast(res.msg)
						setTimeout(function() {
							uni.navigateBack({
								delta: 1, //返回层数，2则上上页
							})
						}, 1500);
					},
					error: res => {
						uni.showModal({
							title: '提示',
							content: res.msg,
							confirmColor: '#ee6666', //确定字体颜色
							showCancel: false, //没有取消按钮的弹框
							buttonText: '确定',
							success: function(res) {
								if (res.confirm) {
									console.log('用户点击确定');
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						});
					}
				})
			},
			/* picker */
			bindPickerChange: function(e) {

				this.MaintCheckDetailList.forEach((current, index) => {
					current.IsPassDetail = e.detail.value;
				});
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			},			/* 图片 */
			sourceTypeChange: function(e) {
				this.sourceTypeIndex = e.target.value
			},
			sizeTypeChange: function(e) {
				this.sizeTypeIndex = e.target.value
			},
			countChange: function(e) {
				this.countIndex = e.target.value;
			},
			chooseImage: async function() {
				const that = this				
				// #ifdef APP-PLUS
				// TODO 选择相机或相册时 需要弹出actionsheet，目前无法获得是相机还是相册，在失败回调中处理
				if (this.sourceTypeIndex !== 2) {
					let status = await this.checkPermission();
					if (status !== 1) {
						return;
					}
				}
				// #endif
				// if (this.imageList.length === 1) {
				// 	let isContinue = await this.isFullImg();
				// 	console.log("是否继续?", isContinue);
				// 	if (!isContinue) {
				// 		return;
				// 	}
				// }
				uni.chooseImage({
					sourceType: sourceType[this.sourceTypeIndex],
					sizeType: sizeType[this.sizeTypeIndex],
					count: this.imageList.length > 0 ? 1 - this.imageList.length : 1,
					success: (res) => {
						this.imageList = this.imageList.concat(res.tempFilePaths);
						const tempFilePaths = res.tempFilePaths;
						uni.uploadFile({
							url: uni.getStorageSync('URL')+'/EquipMaintRecordMain/UploadFilesExtend?id='+this.Id+'&orgId='+uni.getStorageSync('orgId'), 
							filePath: tempFilePaths[0],
							name: 'file',
							success: function(uploadFileRes) {
								let res = JSON.parse(uploadFileRes.data);
								that.ImageName = res.ImageName;
								that.ImageAdress = res.ImageAdress;
							}
						});
					},
					
					fail: (err) => {
						// #ifdef APP-PLUS
						if (err['code'] && err.code !== 0 && this.sourceTypeIndex === 2) {
							this.checkPermission(err.code);
						}
						// #endif
					}
				})
			},
			// submit() {
			// 	// console.log(this.ImageAdress);
			// 	this.request({
			// 		url: '/EquipMaintRecordMain/UpdateSingle',
			// 		method: 'POST',
			// 		data: {
			// 			Id: this.Id,
			// 			IsPassDetail: this.index,
			// 			Remark: this.note,
			// 			ImageName: this.ImageName,
			// 			ImageAdress: this.ImageAdress
			// 		},
			// 		success: res => {
			// 			console.log(res);
			// 		}
			// 	})
			// },
			isFullImg: function() {
				return new Promise((res) => {
					uni.showModal({
						content: "已经有1张图片了,是否清空现有图片？",
						success: (e) => {
							if (e.confirm) {
								this.imageList = [];
								this.imageDataList = [];
								res(true);
							} else {
								res(false)
							}
						},
						fail: () => {
							res(false)
						}
					})
				})
			},
			previewImage: function(e) {
				var current = e.target.dataset.src
				uni.previewImage({
					current: current,
					urls: this.imageList
				})
			},
			// 上传图片
			uploadImg(img) {
				this.request({
					url: '/EquipMaintRecordMain/UploadFiles?id='+this.Id,
					method: 'POST',
					data: {
						file: img,
					},
					success: res => {
						console.log(res);
					}
				});
			},
		}
	}
</script>
<style>
	.groupS {
		background-color: #ffffff;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.uni-px-5 {
		padding-left: 10px;
		padding-right: 10px;
	}
</style>
