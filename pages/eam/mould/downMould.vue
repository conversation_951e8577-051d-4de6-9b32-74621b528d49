<template>
	<view class="uni-common-mt">
		<view class="uni-padding-wrap">
			<view class="cu-form-group margin-top">
				<view class="iconfont xj-erweima xj_form_icon"/>
				<view class="title">模具码</view>
				<input v-model="mouldCode" @confirm="queryMouldInfo()" placeholder="请手动扫描模具码" :focus="focusTwo" />
				<uni-icons class=" uni-panel-icon uni-icon alin_x_center" type="scan" color='#8f8f94' size="25" />
			</view>
			<view class="cu-form-group">
				<view class="iconfont xj-icon_caigoushuliang xj_form_icon"/>
				<view class="title"><text style="color: red;">*</text>冲压次数</view>
				<input type="number" v-model="ProcessQty" placeholder="请手动填写冲压次数" :focus="focusTwo" />
			</view>
			<view class="cu-form-group">
				<view>
					<view class="uni-ellipsis">设备名称：{{mouldInfo.EquipmentName}}</view>
					<view class="uni-ellipsis">设备型号：{{mouldInfo.EquipmentModel}}</view>
					
					<view class="uni-ellipsis">模具名称：{{mouldInfo.MouldName}}</view>
					<view class="uni-ellipsis">模具型号：{{mouldInfo.MouldType}}</view>
					<view class="uni-ellipsis">剩余寿命：{{mouldInfo.ResidualLife}}</view>
				</view>
			</view>
			
			<view class="uni-fixed-bottom xj_button_group margin-top">
				<view class="xj_button" style="width: 40%; color: black;" @click="cancel">取消</view>
				<view class="xj_button" style="width: 60%; background-color: #009598;" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniTag from '@/components/uni-ui/uni-tag/uni-tag.vue';
	import UniIcons from '@/components/uni-ui/uni-icons/uni-icons.vue'
	export default {
		components: {
			uniTag,
			UniIcons
		},
		data() {
			return {
				mouldCode: '',
				ProcessQty:'',
				mouldInfo: {},
				msg: '',
				// 焦点控制
				focusOne: true,
				focusTwo: false
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.cancel();
			uni.stopPullDownRefresh();
		},
		methods: {
			async SetProcessQty() {
				this.ProcessQty = await this.GetMouldProcessQty();
			},
			GetMouldProcessQty() {
				return new Promise((resolve, reject) => {
					this.request({
						url: '/MouldOperationRecord/GetMouldProcessQty',
						method: 'POST',
						data: {
							DeviceCode:this.mouldInfo.EquipmentCode,
							Startdate: this.mouldInfo.CreateTime,
						},
						success: res => {
							resolve(res.response[0].Result)
						},
						error: res => {
							reject(res.msg);
						}
					})
				})
			},
			
			// 查询设备冲压次数
			//  getMouldProcessQty() {
			// 	let that=this;
			// 	this.request({
			// 		url: '/MouldOperationRecord/GetMouldProcessQty',
			// 		method: 'POST',
			// 		data: {
			// 			DeviceCode:this.mouldInfo.EquipmentCode,
			// 			Startdate: this.mouldInfo.CreateTime,
			// 		},
			// 		success: res => {
			// 			that.ProcessQty = res.response[0].Result
			// 		},
			// 		error: res => {
						
			// 		}
			// 	})
			// },
			// 查询模具信息
			queryMouldInfo() {
				this.mouldInfo={};
				let that=this;
				if (!this.mouldCode) {
					this.$toast('未获取到模具码')
					return
				}
				this.request({
					url: '/MouldOperationRecord/GetMouldRecord',
					method: 'GET',
					data: {
						code: this.mouldCode,
					},
					success: res => {
						that.mouldInfo = res.response
						that.SetProcessQty();
					},
					error: res => {
						that.$error(res.msg)
						that.cancel();
					}
				})
				this.changeBlur();
			},
			// 提交真实数量
			submit() {
				let that = this;
				if (!this.mouldInfo||!this.mouldInfo.Id) {
					uni.showToast({
						title: '请扫模具码！',
						icon: 'error'
					})
				}else if(!this.ProcessQty){
					uni.showToast({
						title: '请输入冲压次数',
						icon: 'error'
					})
				} else {
					// #ifdef APP-PLUS
					let platform = uni.getSystemInfoSync().platform;
					let btns = platform == 'android' ? ["确认", "取消"] : ["取消", "确认"];
					plus.nativeUI.confirm('确认要下模吗？', function(e) {
						//0==确认，否则取消  
						if (e.index == 0) {
							that.req()
						} else {

						}
					}, {
						"title": '提示',
						"buttons": btns,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '确认要下模吗？',
						success: function(res) {
					  if (res.confirm) {
								that.req()
							} else if (res.cancel) {}
						}
					})
					// #endif
				}
			},
			// 请求
			req() {
				var data=this.mouldInfo;
				data.ProcessQty=this.ProcessQty;
				this.request({
					url: '/MouldOperationRecord/MouldCounterdie',
					method: 'Put',
					data,
					success: res => {
						uni.showToast({
							title: res.msg,
							icon: 'success'
						});
						this.cancel();
						this.changeBlur();
					},
					error: res => {
						this.$error(res.msg)
						this.cancel();
					}
				})
			},
			// 取消，初始化页面
			cancel() {
				this.mouldCode = '';
				this.mouldInfo = {};
				this.ProcessQty='';
				
			},
			// 焦点处理
			changeBlur() {

				let that = this;
				if (this.machineCode == '') {
					that.focusOne = true;
					that.focusTwo=false;
				} else if (this.mouldCode == '') {
					that.focusTwo = true;
					that.focusOne = false;
					
				}
			},
			// 
			reset() {
				this.machineCode = '';
				this.mouldCode = '';
			}
		}
	}
</script>

<style>

</style>
