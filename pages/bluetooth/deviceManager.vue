<template>
  <view class="container">
    <view class="header">
      <text class="title">蓝牙打印机管理</text>
    </view>
    
    <!-- 连接状态 -->
    <view class="status-card">
      <view class="status-info">
        <text class="status-label">连接状态:</text>
        <text :class="['status-text', connectedDevice ? 'connected' : 'disconnected']">
          {{ connectedDevice ? '已连接' : '未连接' }}
        </text>
      </view>
      <view v-if="connectedDevice" class="device-info">
        <text class="device-name">{{ connectedDevice.name || '蓝牙打印机' }}</text>
        <text class="device-address">{{ connectedDevice.address }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="btn btn-primary" 
        @click="searchDevices"
        :disabled="isSearching"
      >
        {{ isSearching ? '搜索中...' : '搜索设备' }}
      </button>
      
      <button 
        class="btn btn-danger" 
        @click="disconnectDevice"
        :disabled="!connectedDevice"
      >
        断开连接
      </button>
      
      <button 
        class="btn btn-success" 
        @click="testPrint"
        :disabled="!connectedDevice"
      >
        测试打印
      </button>
    </view>

    <!-- 设备列表 -->
    <view class="device-list" v-if="deviceList.length > 0">
      <view class="list-header">
        <text class="list-title">可用设备</text>
      </view>
      
      <view 
        class="device-item" 
        v-for="(device, index) in deviceList" 
        :key="index"
        @click="connectDevice(device)"
      >
        <view class="device-main">
          <text class="device-name">{{ device.name || '未知设备' }}</text>
          <text class="device-address">{{ device.address }}</text>
        </view>
        <view class="device-action">
          <text class="connect-text">点击连接</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="!isSearching && deviceList.length === 0">
      <text class="empty-text">未找到设备，请点击搜索设备</text>
    </view>
  </view>
</template>

<script>
import bluetoothPrintManager from '@/utils/bluetoothPrintManager.js'

export default {
  data() {
    return {
      connectedDevice: null,
      deviceList: [],
      isSearching: false
    }
  },
  
  onLoad() {
    // 初始化蓝牙打印SDK
    bluetoothPrintManager.initSDK()
    this.updateConnectionStatus()
  },
  
  onShow() {
    this.updateConnectionStatus()
  },
  
  methods: {
    // 更新连接状态
    updateConnectionStatus() {
      this.connectedDevice = bluetoothPrintManager.getConnectedDevice()
    },
    
    // 搜索蓝牙设备
    async searchDevices() {
      this.isSearching = true
      this.deviceList = []
      
      try {
        const devices = await bluetoothPrintManager.searchBluetoothDevices()
        this.deviceList = devices
        
        if (devices.length === 0) {
          uni.showToast({
            title: '未找到设备',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('搜索设备失败:', error)
        uni.showToast({
          title: error.message || '搜索失败',
          icon: 'error'
        })
      } finally {
        this.isSearching = false
      }
    },
    
    // 连接设备
    async connectDevice(device) {
      try {
        await bluetoothPrintManager.connectDevice(device)
        this.updateConnectionStatus()
        this.deviceList = [] // 连接成功后清空设备列表
      } catch (error) {
        console.error('连接设备失败:', error)
        uni.showToast({
          title: error.message || '连接失败',
          icon: 'error'
        })
      }
    },
    
    // 断开连接
    disconnectDevice() {
      bluetoothPrintManager.disconnectDevice()
      this.updateConnectionStatus()
    },
    
    // 测试打印
    async testPrint() {
      try {
        const testData = {
          orderNo: 'TEST001',
          materialCode: 'TEST-MAT-001',
          materialName: '测试产品',
          specification: '测试规格',
          goodQty: 10,
          badQty: 0,
          lineName: '测试产线',
          userName: '测试用户',
          departmentName: '测试部门',
          processName: '测试工序'
        }
        
        await bluetoothPrintManager.printProductionLabel(testData)
        
        uni.showToast({
          title: '测试打印成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('测试打印失败:', error)
        uni.showToast({
          title: error.message || '测试打印失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20upx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 30upx 0;
}

.title {
  font-size: 36upx;
  font-weight: bold;
  color: #333;
}

.status-card {
  background-color: white;
  border-radius: 12upx;
  padding: 30upx;
  margin-bottom: 30upx;
  box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.1);
}

.status-info {
  display: flex;
  align-items: center;
  margin-bottom: 20upx;
}

.status-label {
  font-size: 28upx;
  color: #666;
  margin-right: 20upx;
}

.status-text {
  font-size: 28upx;
  font-weight: bold;
}

.status-text.connected {
  color: #4CAF50;
}

.status-text.disconnected {
  color: #F44336;
}

.device-info {
  padding-left: 20upx;
  border-left: 4upx solid #007aff;
}

.device-name {
  display: block;
  font-size: 30upx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10upx;
}

.device-address {
  font-size: 24upx;
  color: #999;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20upx;
  margin-bottom: 30upx;
}

.btn {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  border-radius: 12upx;
  font-size: 30upx;
  font-weight: bold;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: white;
}

.btn:disabled {
  background: #ccc !important;
  color: #999 !important;
}

.device-list {
  background-color: white;
  border-radius: 12upx;
  overflow: hidden;
  box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.1);
}

.list-header {
  padding: 30upx;
  background-color: #f8f9fa;
  border-bottom: 1upx solid #eee;
}

.list-title {
  font-size: 28upx;
  font-weight: bold;
  color: #333;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 30upx;
  border-bottom: 1upx solid #eee;
}

.device-item:last-child {
  border-bottom: none;
}

.device-item:active {
  background-color: #f5f5f5;
}

.device-main {
  flex: 1;
}

.device-main .device-name {
  font-size: 28upx;
  color: #333;
  margin-bottom: 10upx;
}

.device-main .device-address {
  font-size: 24upx;
  color: #999;
}

.device-action {
  padding: 10upx 20upx;
  background-color: #007aff;
  border-radius: 20upx;
}

.connect-text {
  font-size: 24upx;
  color: white;
}

.empty-state {
  text-align: center;
  padding: 100upx 0;
}

.empty-text {
  font-size: 28upx;
  color: #999;
}

.device-list {
  background-color: white;
  border-radius: 12upx;
  overflow: hidden;
  box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.1);
}

.list-header {
  padding: 30upx;
  background-color: #f8f9fa;
  border-bottom: 1upx solid #eee;
}

.list-title {
  font-size: 28upx;
  font-weight: bold;
  color: #333;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 30upx;
  border-bottom: 1upx solid #eee;
}

.device-item:last-child {
  border-bottom: none;
}

.device-item:active {
  background-color: #f5f5f5;
}

.device-main {
  flex: 1;
}

.device-main .device-name {
  font-size: 28upx;
  color: #333;
  margin-bottom: 10upx;
}

.device-main .device-address {
  font-size: 24upx;
  color: #999;
}

.device-action {
  padding: 10upx 20upx;
  background-color: #007aff;
  border-radius: 20upx;
}

.connect-text {
  font-size: 24upx;
  color: white;
}

.empty-state {
  text-align: center;
  padding: 100upx 0;
}

.empty-text {
  font-size: 28upx;
  color: #999;
}
</style>
