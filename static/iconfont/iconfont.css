@font-face {
  font-family: "iconfont"; /* Project id 3650239 */
  src: url('@/static/iconfont/iconfont.woff2?t=1663233347525') format('woff2'),
       url('@/static/iconfont/iconfont.woff?t=1663233347525') format('woff'),
       url('@/static/iconfont/iconfont.ttf?t=1663233347525') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.xj-guizehegexingjiancha:before {
  content: "\e61c";
}

.xj-buliang:before {
  content: "\e6b7";
}

.xj-a-Processtooling:before {
  content: "\e64b";
}

.xj-lianjie:before {
  content: "\e600";
}

.xj-xiangmu:before {
  content: "\e691";
}

.xj-zuzhi:before {
  content: "\e684";
}

.xj-icon:before {
  content: "\e624";
}

.xj-erweima:before {
  content: "\e7ad";
}

.xj-type:before {
  content: "\e6c0";
}

.xj-bianmubianma:before {
  content: "\e607";
}

.xj-danyilaiyuan:before {
  content: "\e627";
}

.xj-mingcheng:before {
  content: "\e6ed";
}

.xj-zhuanchu:before {
  content: "\e642";
}

.xj-zhuanru:before {
  content: "\e643";
}

.xj-mingxi:before {
  content: "\e628";
}

.xj-cangku:before {
  content: "\eac9";
}

.xj-riqi_o:before {
  content: "\ebb1";
}

.xj-gongdan:before {
  content: "\ec37";
}

.xj-yanzhengmamima:before {
  content: "\e6ba";
}

.xj-icon_caigoushuliang:before {
  content: "\e6bb";
}

.xj-guigeguanli:before {
  content: "\e644";
}

.xj-tiaoma:before {
  content: "\e63e";
}

.xj-renyuan:before {
  content: "\e799";
}

.xj-beizhu:before {
  content: "\e60d";
}

.xj-chanxian:before {
  content: "\e653";
}

.xj-kuwei:before {
  content: "\e683";
}

.xj-bumen:before {
  content: "\e7cf";
}

.xj-konghuowei:before {
  content: "\e73c";
}

