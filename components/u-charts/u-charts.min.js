'use strict';var config={yAxisWidth:15,yAxisSplit:5,xAxisHeight:15,xAxisLineHeight:15,legendHeight:15,yAxisTitleWidth:15,padding:[10,10,10,10],pixelRatio:1,rotate:!1,columePadding:3,fontSize:13,dataPointShape:["circle","circle","circle","circle"],colors:["#1890ff","#2fc25b","#facc14","#f04864","#8543e0","#90ed7d"],pieChartLinePadding:15,pieChartTextPadding:5,xAxisTextPadding:3,titleColor:"#333333",titleFontSize:20,subtitleColor:"#999999",subtitleFontSize:15,toolTipPadding:3,toolTipBackground:"#000000",toolTipOpacity:.7,toolTipLineHeight:20,radarGridCount:3,radarLabelTextMargin:15,gaugeLabelTextMargin:15};let assign=Object.assign?Object.assign:function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t,i=Object(e),a=1;a<arguments.length;a++)if(t=arguments[a],null!=t)for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(i[o]=t[o]);return i};var util={toFixed:function(e,t){return t=t||2,this.isFloat(e)&&(e=e.toFixed(t)),e},isFloat:function(e){return 0!=e%1},approximatelyEqual:function(e,t){return 1e-10>Math.abs(e-t)},isSameSign:function(e,t){var i=Math.abs;return i(e)===e&&i(t)===t||i(e)!==e&&i(t)!==t},isSameXCoordinateArea:function(e,t){return this.isSameSign(e.x,t.x)},isCollision:function(e,t){e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height;var i=t.start.x>e.end.x||t.end.x<e.start.x||t.end.y>e.start.y||t.start.y<e.end.y;return!i}};function getH5Offset(t){return t.mp={changedTouches:[]},t.mp.changedTouches.push({x:t.offsetX,y:t.offsetY}),t}function hexToRgb(e,t){var i=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,a=e.replace(i,function(e,t,i,a){return t+t+i+i+a+a}),o=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a),n=parseInt(o[1],16),l=parseInt(o[2],16),r=parseInt(o[3],16);return"rgba("+n+","+l+","+r+","+t+")"}function findRange(e,t,i){if(isNaN(e))throw new Error("[uCharts] unvalid series data!");i=i||10,t=t?t:"upper";for(var a=1;1>i;)i*=10,a*=10;for(e="upper"===t?Math.ceil(e*a):Math.floor(e*a);0!=e%i;)"upper"===t?e++:e--;return e/a}function calCandleMA(e,t,i,a){let o=[];for(let n,l=0;l<e.length;l++){n={data:[],name:t[l],color:i[l]};for(let t=0,i=a.length;t<i;t++){if(t<e[l]){n.data.push(null);continue}let i=0;for(let o=0;o<e[l];o++)i+=a[t-o][1];n.data.push(+(i/e[l]).toFixed(3))}o.push(n)}return o}function calValidDistance(e,t,i,a,o){var n=o.width-o.area[1]-o.area[3],l=i.eachSpacing*(o.chartData.xAxisData.xAxisPoints.length-1),r=t;return 0<=t?(r=0,e.event.trigger("scrollLeft")):Math.abs(t)>=l-n&&(r=n-l,e.event.trigger("scrollRight")),r}function isInAngleRange(e,t,i){function a(e){for(;0>e;)e+=2*o;for(;e>2*o;)e-=2*o;return e}var o=Math.PI;return e=a(e),t=a(t),i=a(i),t>i&&(i+=2*o,e<t&&(e+=2*o)),e>=t&&e<=i}function calRotateTranslate(e,t,i){var a=e,o=i-t,n=a+(i-o-a)/1.4142135623730951;n*=-1;return{transX:n,transY:(i-o)*(1.4142135623730951-1)-(i-o-a)/1.4142135623730951}}function createCurveControlPoints(e,t){function i(e,t){return!!(e[t-1]&&e[t+1])&&(e[t].y>=Math.max(e[t-1].y,e[t+1].y)||e[t].y<=Math.min(e[t-1].y,e[t+1].y))}var o=.2,a=.2,n=null,l=null,r=null,s=null;if(1>t?(n=e[0].x+(e[1].x-e[0].x)*o,l=e[0].y+(e[1].y-e[0].y)*o):(n=e[t].x+(e[t+1].x-e[t-1].x)*o,l=e[t].y+(e[t+1].y-e[t-1].y)*o),t>e.length-3){var d=e.length-1;r=e[d].x-(e[d].x-e[d-1].x)*a,s=e[d].y-(e[d].y-e[d-1].y)*a}else r=e[t+1].x-(e[t+2].x-e[t].x)*a,s=e[t+1].y-(e[t+2].y-e[t].y)*a;return i(e,t+1)&&(s=e[t+1].y),i(e,t)&&(l=e[t].y),{ctrA:{x:n,y:l},ctrB:{x:r,y:s}}}function convertCoordinateOrigin(e,t,i){return{x:i.x+e,y:i.y-t}}function avoidCollision(e,t){if(t)for(;util.isCollision(e,t);)0<e.start.x?e.start.y--:0>e.start.x?e.start.y++:0<e.start.y?e.start.y++:e.start.y--;return e}function fillSeries(e,t,i){var a=0;return e.map(function(e){if(e.color||(e.color=i.colors[a],a=(a+1)%i.colors.length),e.index||(e.index=0),e.type||(e.type=t.type),"undefined"==typeof e.show&&(e.show=!0),e.type||(e.type=t.type),e.pointShape||(e.pointShape="circle"),!e.legendShape)switch(e.type){case"line":e.legendShape="line";break;case"column":e.legendShape="rect";break;case"area":e.legendShape="triangle";break;default:e.legendShape="circle";}return e})}function getDataRange(e,t){var i=0,a=t-e;return i=1e4<=a?1e3:1e3<=a?100:100<=a?10:10<=a?5:1<=a?1:.1<=a?.1:.01<=a?.01:.001<=a?.001:1e-4<=a?1e-4:1e-5<=a?1e-5:1e-6,{minRange:findRange(e,"lower",i),maxRange:findRange(t,"upper",i)}}function measureText(e){var t=1<arguments.length&&arguments[1]!==void 0?arguments[1]:config.fontSize;e=e+"";var e=e.split(""),a=0;for(let t,o=0;o<e.length;o++)t=e[o],a+=/[a-zA-Z]/.test(t)?7:/[0-9]/.test(t)?5.5:/\./.test(t)?2.7:/-/.test(t)?3.25:/[\u4e00-\u9fa5]/.test(t)?10:/\(|\)/.test(t)?3.73:/\s/.test(t)?2.5:/%/.test(t)?8:10;return a*t/10}function dataCombine(e){return e.reduce(function(e,t){return(e.data?e.data:e).concat(t.data)},[])}function dataCombineStack(e,t){for(var o=Array(t),a=0;a<o.length;a++)o[a]=0;for(var n=0;n<e.length;n++)for(var a=0;a<o.length;a++)o[a]+=e[n].data[a];return e.reduce(function(e,t){return(e.data?e.data:e).concat(t.data).concat(o)},[])}function getTouches(t,i,a){let e,o;return t.clientX?i.rotate?(o=i.height-t.clientX*i.pixelRatio,e=(t.pageY-a.currentTarget.offsetTop-i.height/i.pixelRatio/2*(i.pixelRatio-1))*i.pixelRatio):(e=t.clientX*i.pixelRatio,o=(t.pageY-a.currentTarget.offsetTop-i.height/i.pixelRatio/2*(i.pixelRatio-1))*i.pixelRatio):i.rotate?(o=i.height-t.x*i.pixelRatio,e=t.y*i.pixelRatio):(e=t.x*i.pixelRatio,o=t.y*i.pixelRatio),{x:e,y:o}}function getSeriesDataItem(e,t){var i=[];for(let a,o=0;o<e.length;o++)if(a=e[o],null!==a.data[t]&&"undefined"!=typeof a.data[t]&&a.show){let e={};e.color=a.color,e.type=a.type,e.style=a.style,e.pointShape=a.pointShape,e.disableLegend=a.disableLegend,e.name=a.name,e.show=a.show,e.data=a.format?a.format(a.data[t]):a.data[t],i.push(e)}return i}function getMaxTextListLength(e){var t=e.map(function(e){return measureText(e)});return Math.max.apply(null,t)}function getRadarCoordinateSeries(e){for(var t=Math.PI,a=[],o=0;o<e;o++)a.push(2*t/e*o);return a.map(function(e){return-1*e+t/2})}function getToolTipData(e,t,a,i){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{},n=e.map(function(e){return{text:o.format?o.format(e,i[a]):e.name+": "+e.data,color:e.color}}),l=[],r={x:0,y:0};for(let o,n=0;n<t.length;n++)o=t[n],"undefined"!=typeof o[a]&&null!==o[a]&&l.push(o[a]);for(let o,n=0;n<l.length;n++)o=l[n],r.x=Math.round(o.x),r.y+=o.y;return r.y/=l.length,{textList:n,offset:r}}function getMixToolTipData(e,t,a,i){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{},n=e.map(function(e){return{text:o.format?o.format(e,i[a]):e.name+": "+e.data,color:e.color,disableLegend:!!e.disableLegend}});n=n.filter(function(e){if(!0!==e.disableLegend)return e});var l=[],r={x:0,y:0};for(let o,n=0;n<t.length;n++)o=t[n],"undefined"!=typeof o[a]&&null!==o[a]&&l.push(o[a]);for(let o,n=0;n<l.length;n++)o=l[n],r.x=Math.round(o.x),r.y+=o.y;return r.y/=l.length,{textList:n,offset:r}}function getCandleToolTipData(e,t,a,o,i,n){6<arguments.length&&void 0!==arguments[6]?arguments[6]:{};let l=n.color.upFill,r=n.color.downFill,s=[l,l,r,l];var d=[];let h={text:i[o],color:null};d.push(h),t.map(function(t){0==o&&0>t.data[1]-t.data[0]?s[1]=r:(t.data[0]<e[o-1][1]&&(s[0]=r),t.data[1]<t.data[0]&&(s[1]=r),t.data[2]>e[o-1][1]&&(s[2]=l),t.data[3]<e[o-1][1]&&(s[3]=r));let i={text:"\u5F00\u76D8\uFF1A"+t.data[0],color:s[0]},a={text:"\u6536\u76D8\uFF1A"+t.data[1],color:s[1]},n={text:"\u6700\u4F4E\uFF1A"+t.data[2],color:s[2]},h={text:"\u6700\u9AD8\uFF1A"+t.data[3],color:s[3]};d.push(i,a,n,h)});var x=[],c={x:0,y:0};for(let l,r=0;r<a.length;r++)l=a[r],"undefined"!=typeof l[o]&&null!==l[o]&&x.push(l[o]);return c.x=Math.round(x[0][0].x),{textList:d,offset:c}}function filterSeries(e){let t=[];for(let a=0;a<e.length;a++)!0==e[a].show&&t.push(e[a]);return t}function findCurrentIndex(e,t,i,a){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:0,n=-1,l=0;return("line"==i.type||"area"==i.type)&&"justify"==i.xAxis.boundaryGap&&(l=i.chartData.eachSpacing/2),isInExactChartArea(e,i,a)&&t.forEach(function(t,i){e.x+o+l>t&&(n=i)}),n}function findLegendIndex(e,t){let i=-1;if(isInExactLegendArea(e,t.area)){let a=t.points,o=-1;for(let t,n=0,l=a.length;n<l;n++){t=a[n];for(let a=0;a<t.length;a++){o+=1;let n=t[a].area;if(e.x>n[0]&&e.x<n[2]&&e.y>n[1]&&e.y<n[3]){i=o;break}}}return i}return i}function isInExactLegendArea(e,t){return e.x>t.start.x&&e.x<t.end.x&&e.y>t.start.y&&e.y<t.end.y}function isInExactChartArea(e,t){return e.x<t.width-t.area[1]+10&&e.x>t.area[3]-10&&e.y>t.area[0]&&e.y<t.height-t.area[2]}function findRadarChartCurrentIndex(e,t,i){var a=Math.PI,o=2*a/i,n=-1;if(isInExactPieChartArea(e,t.center,t.radius)){var l=function(e){return 0>e&&(e+=2*a),e>2*a&&(e-=2*a),e},r=Math.atan2(t.center.y-e.y,e.x-t.center.x);r=-1*r,0>r&&(r+=2*a);var s=t.angleList.map(function(e){return e=l(-1*e),e});s.forEach(function(e,t){var i=l(e-o/2),s=l(e+o/2);s<i&&(s+=2*a),(r>=i&&r<=s||r+2*a>=i&&r+2*a<=s)&&(n=t)})}return n}function findFunnelChartCurrentIndex(e,t){for(var a,o=-1,n=0,l=t.series.length;n<l;n++)if(a=t.series[n],e.x>a.funnelArea[0]&&e.x<a.funnelArea[2]&&e.y>a.funnelArea[1]&&e.y<a.funnelArea[3]){o=n;break}return o}function findWordChartCurrentIndex(e,t){for(var a,o=-1,n=0,l=t.length;n<l;n++)if(a=t[n],e.x>a.area[0]&&e.x<a.area[2]&&e.y>a.area[1]&&e.y<a.area[3]){o=n;break}return o}function findMapChartCurrentIndex(e,t){for(var a,o=-1,n=t.chartData.mapData,l=t.series,r=pointToCoordinate(e.y,e.x,n.bounds,n.scale,n.xoffset,n.yoffset),s=[r.x,r.y],d=0,h=l.length;d<h;d++)if(a=l[d].geometry.coordinates,isPoiWithinPoly(s,a)){o=d;break}return o}function findPieChartCurrentIndex(e,t){var a=-1;if(isInExactPieChartArea(e,t.center,t.radius)){var o=Math.atan2(t.center.y-e.y,e.x-t.center.x);o=-o;for(var n,l=0,r=t.series.length;l<r;l++)if(n=t.series[l],isInAngleRange(o,n._start_,n._start_+2*n._proportion_*Math.PI)){a=l;break}}return a}function isInExactPieChartArea(e,t,i){var a=Math.pow;return a(e.x-t.x,2)+a(e.y-t.y,2)<=a(i,2)}function splitPoints(e){var t=[],i=[];return e.forEach(function(e){null===e?(i.length&&t.push(i),i=[]):i.push(e)}),i.length&&t.push(i),t}function calLegendData(e,t,i,a){var o=Math.max,n=Math.floor;let l={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===t.legend.show)return a.legendData=l,l;let r=t.legend.padding,s=t.legend.margin,d=t.legend.fontSize,h=15*t.pixelRatio,x=5*t.pixelRatio,c=o(t.legend.lineHeight*t.pixelRatio,d);if("top"==t.legend.position||"bottom"==t.legend.position){let a=[],n=0,p=[],g=[];for(let o=0;o<e.length;o++){let i=e[o],l=h+x+measureText(i.name||"undefined",d)+t.legend.itemGap;n+l>t.width-t.padding[1]-t.padding[3]?(a.push(g),p.push(n-t.legend.itemGap),n=l,g=[i]):(n+=l,g.push(i))}if(g.length){a.push(g),p.push(n-t.legend.itemGap),l.widthArr=p;let e=o.apply(null,p);switch(t.legend.float){case"left":l.area.start.x=t.padding[3],l.area.end.x=t.padding[3]+2*r;break;case"right":l.area.start.x=t.width-t.padding[1]-e-2*r,l.area.end.x=t.width-t.padding[1];break;default:l.area.start.x=(t.width-e)/2-r,l.area.end.x=(t.width+e)/2+r;}l.area.width=e+2*r,l.area.wholeWidth=e+2*r,l.area.height=a.length*c+2*r,l.area.wholeHeight=a.length*c+2*r+2*s,l.points=a}}else{let i=e.length,a=t.height-t.padding[0]-t.padding[2]-2*s-2*r,o=Math.min(n(a/c),i);switch(l.area.height=o*c+2*r,l.area.wholeHeight=o*c+2*r,t.legend.float){case"top":l.area.start.y=t.padding[0]+s,l.area.end.y=t.padding[0]+s+l.area.height;break;case"bottom":l.area.start.y=t.height-t.padding[2]-s-l.area.height,l.area.end.y=t.height-t.padding[2]-s;break;default:l.area.start.y=(t.height-l.area.height)/2,l.area.end.y=(t.height+l.area.height)/2;}let p=0==i%o?i/o:n(i/o+1),g=[];for(let t,a=0;a<p;a++)t=e.slice(a*o,a*o+o),g.push(t);if(l.points=g,g.length){for(let e=0;e<g.length;e++){let i=g[e],a=0;for(let e,o=0;o<i.length;o++)e=h+x+measureText(i[o].name||"undefined",d)+t.legend.itemGap,e>a&&(a=e);l.widthArr.push(a),l.heightArr.push(i.length*c+2*r)}let e=0;for(let t=0;t<l.widthArr.length;t++)e+=l.widthArr[t];l.area.width=e-t.legend.itemGap+2*r,l.area.wholeWidth=l.area.width+r}}switch(t.legend.position){case"top":l.area.start.y=t.padding[0]+s,l.area.end.y=t.padding[0]+s+l.area.height;break;case"bottom":l.area.start.y=t.height-t.padding[2]-l.area.height-s,l.area.end.y=t.height-t.padding[2]-s;break;case"left":l.area.start.x=t.padding[3],l.area.end.x=t.padding[3]+l.area.width;break;case"right":l.area.start.x=t.width-t.padding[1]-l.area.width,l.area.end.x=t.width-t.padding[1];}return a.legendData=l,l}function calCategoriesData(e,t,i,a){var o={angle:0,xAxisHeight:i.xAxisHeight},n=e.map(function(e){return measureText(e)}),l=Math.max.apply(this,n);return!0==t.xAxis.rotateLabel&&l+2*i.xAxisTextPadding>a&&(o.angle=45*Math.PI/180,o.xAxisHeight=2*i.xAxisTextPadding+l*Math.sin(o.angle)),o}function getRadarDataPoints(e,t,i,a,o){var n=Math.max,l=5<arguments.length&&void 0!==arguments[5]?arguments[5]:1,r=o.extra.radar||{};r.max=r.max||0;var s=n(r.max,n.apply(null,dataCombine(a))),d=[];for(let n=0;n<a.length;n++){let o=a[n],r={};r.color=o.color,r.data=[],o.data.forEach(function(a,o){let n={};n.angle=e[o],n.proportion=a/s,n.position=convertCoordinateOrigin(i*n.proportion*l*Math.cos(n.angle),i*n.proportion*l*Math.sin(n.angle),t),r.data.push(n)}),d.push(r)}return d}function getPieDataPoints(e,t){var a=2<arguments.length&&arguments[2]!==void 0?arguments[2]:1,o=0,n=0;for(let a,n=0;n<e.length;n++)a=e[n],a.data=null===a.data?0:a.data,o+=a.data;for(let n,l=0;l<e.length;l++)n=e[l],n.data=null===n.data?0:n.data,n._proportion_=0===o?1/e.length*a:n.data/o*a,n._radius_=t;for(let a,o=0;o<e.length;o++)a=e[o],a._start_=n,n+=2*a._proportion_*Math.PI;return e}function getFunnelDataPoints(e,t){var a=2<arguments.length&&arguments[2]!==void 0?arguments[2]:1;e=e.sort(function(e,t){return parseInt(t.data)-parseInt(e.data)});for(let o=0;o<e.length;o++)e[o].radius=e[o].data/e[0].data*t*a,e[o]._proportion_=e[o].data/e[0].data;return e.reverse()}function getRoseDataPoints(e,t,a,o){var n=4<arguments.length&&arguments[4]!==void 0?arguments[4]:1,l=0,r=0,s=[];for(let n,r=0;r<e.length;r++)n=e[r],n.data=null===n.data?0:n.data,l+=n.data,s.push(n.data);var d=Math.min.apply(null,s),h=Math.max.apply(null,s);for(let r,s=0;s<e.length;s++)r=e[s],r.data=null===r.data?0:r.data,0===l||"area"==t?(r._proportion_=r.data/l*n,r._rose_proportion_=1/e.length*n):(r._proportion_=r.data/l*n,r._rose_proportion_=r.data/l*n),r._radius_=a+(o-a)*((r.data-d)/(h-d));for(let n,l=0;l<e.length;l++)n=e[l],n._start_=r,r+=2*n._rose_proportion_*Math.PI;return e}function getArcbarDataPoints(e,t){var a=2<arguments.length&&arguments[2]!==void 0?arguments[2]:1;1==a&&(a=.999999);for(let o,n=0;n<e.length;n++){o=e[n],o.data=null===o.data?0:o.data;let i;i="circle"==t.type?2:t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle,o._proportion_=i*o.data*a+t.startAngle,2<=o._proportion_&&(o._proportion_%=2)}return e}function getGaugeAxisPoints(e,t,a){let o=t;for(let n=0;n<e.length;n++)e[n].value=null===e[n].value?0:e[n].value,e[n]._startAngle_=o,e[n]._endAngle_=(t-a+1)*e[n].value+t,2<=e[n]._endAngle_&&(e[n]._endAngle_%=2),o=e[n]._endAngle_;return e}function getGaugeDataPoints(e,t,a){let o=3<arguments.length&&arguments[3]!==void 0?arguments[3]:1;for(let n,l=0;l<e.length;l++){if(n=e[l],n.data=null===n.data?0:n.data,"auto"==a.pointer.color){for(let e=0;e<t.length;e++)if(n.data<=t[e].value){n.color=t[e].color;break}}else n.color=a.pointer.color;let i=a.startAngle-a.endAngle+1;n._endAngle_=i*n.data+a.startAngle,n._oldAngle_=a.oldAngle,a.oldAngle<a.endAngle&&(n._oldAngle_+=2),n._proportion_=n.data>=a.oldData?(n._endAngle_-n._oldAngle_)*o+a.oldAngle:n._oldAngle_-(n._oldAngle_-n._endAngle_)*o,2<=n._proportion_&&(n._proportion_%=2)}return e}function getPieTextMaxLength(e){e=getPieDataPoints(e);let t=0;for(let a=0;a<e.length;a++){let i=e[a],o=i.format?i.format(+i._proportion_.toFixed(2)):util.toFixed(100*i._proportion_)+"%";t=Math.max(t,measureText(o))}return t}function fixColumeData(e,t,i,a,o,n){return e.map(function(e){return null===e?null:(e.width=Math.ceil((t-2*o.columePadding)/i),n.extra.column&&n.extra.column.width&&0<+n.extra.column.width&&(e.width=Math.min(e.width,+n.extra.column.width)),0>=e.width&&(e.width=1),e.x+=(a+.5-i/2)*e.width,e)})}function fixColumeMeterData(e,t,i,a,o,n,l){return e.map(function(e){return null===e?null:(e.width=Math.ceil((t-2*o.columePadding)/2),n.extra.column&&n.extra.column.width&&0<+n.extra.column.width&&(e.width=Math.min(e.width,+n.extra.column.width)),0<a&&(e.width-=2*l),e)})}function fixColumeStackData(e,t,i,a,o,n){return e.map(function(e){return null===e?null:(e.width=Math.ceil((t-2*o.columePadding)/2),n.extra.column&&n.extra.column.width&&0<+n.extra.column.width&&(e.width=Math.min(e.width,+n.extra.column.width)),e)})}function getXAxisPoints(e,t){var i=t.width-t.area[1]-t.area[3],a=t.enableScroll?Math.min(t.xAxis.itemCount,e.length):e.length;("line"==t.type||"area"==t.type)&&1<a&&"justify"==t.xAxis.boundaryGap&&(a-=1);var o=i/a,n=[],l=t.area[3],r=t.width-t.area[1];return e.forEach(function(e,t){n.push(l+t*o)}),"justify"!==t.xAxis.boundaryGap&&(!0===t.enableScroll?n.push(l+e.length*o):n.push(r)),{xAxisPoints:n,startX:l,endX:r,eachSpacing:o}}function getCandleDataPoints(e,t,i,a,o,n){var l=Math.round,r=7<arguments.length&&void 0!==arguments[7]?arguments[7]:1,s=[],d=n.height-n.area[0]-n.area[2];return e.forEach(function(e,h){if(null===e)s.push(null);else{var x=[];e.forEach(function(e){var s={x:a[h]+l(o/2)},c=e.value||e,p=d*(c-t)/(i-t);p*=r,s.y=n.height-l(p)-n.area[2],x.push(s)}),s.push(x)}}),s}function getDataPoints(e,t,i,a,o,n){var l=Math.round,r=7<arguments.length&&void 0!==arguments[7]?arguments[7]:1,s="center";("line"==n.type||"area"==n.type)&&(s=n.xAxis.boundaryGap);var d=[],h=n.height-n.area[0]-n.area[2];return e.forEach(function(e,x){if(null===e)d.push(null);else{var c={};c.color=e.color,c.x=a[x],"center"==s&&(c.x+=l(o/2));var p=e;"object"==typeof e&&null!=e&&(p=e.value);var g=h*(p-t)/(i-t);g*=r,c.y=n.height-l(g)-n.area[2],d.push(c)}}),d}function getStackDataPoints(e,t,i,a,o,n,l,r,s){var d=Math.round,h=9<arguments.length&&void 0!==arguments[9]?arguments[9]:1,x=[],c=n.height-n.area[0]-n.area[2];return e.forEach(function(e,l){if(null===e)x.push(null);else{var p={color:e.color,x:a[l]+d(o/2)};if(0<r){var g=0;for(let e=0;e<=r;e++)g+=s[e].data[l];var y=g-e,f=c*(g-t)/(i-t),u=c*(y-t)/(i-t)}else var g=e,f=c*(g-t)/(i-t),u=0;var m=u;f*=h,m*=h,p.y=n.height-d(f)-n.area[2],p.y0=n.height-d(m)-n.area[2],x.push(p)}}),x}function getYAxisTextList(e,t,a,o){var n,l=Math.min,r=Math.max,s=4<arguments.length&&void 0!==arguments[4]?arguments[4]:-1;n="stack"==o?dataCombineStack(e,t.categories.length):dataCombine(e);var d=[];n=n.filter(function(e){return"object"==typeof e&&null!==e?e.constructor==Array?null!==e:null!==e.value:null!==e}),n.map(function(e){"object"==typeof e?e.constructor==Array?e.map(function(e){d.push(e)}):d.push(e.value):d.push(e)});var h=0,x=0;if(0<d.length&&(h=l.apply(this,d),x=r.apply(this,d)),-1<s?("number"==typeof t.yAxis.data[s].min&&(h=l(t.yAxis.data[s].min,h)),"number"==typeof t.yAxis.data[s].max&&(x=r(t.yAxis.data[s].max,x))):("number"==typeof t.yAxis.min&&(h=l(t.yAxis.min,h)),"number"==typeof t.yAxis.max&&(x=r(t.yAxis.max,x))),h===x){var c=x||10;x+=c}for(var p=getDataRange(h,x),g=p.minRange,y=p.maxRange,f=[],u=(y-g)/a.yAxisSplit,m=0;m<=a.yAxisSplit;m++)f.push(g+u*m);return f.reverse()}function calYAxisData(e,t,a){var o=Math.max,n=assign({},{type:""},t.extra.column),l=t.yAxis.data.length,r=Array(l);if(0<l){for(let t=0;t<l;t++){r[t]=[];for(let i=0;i<e.length;i++)e[i].index==t&&r[t].push(e[i])}var s=Array(l),d=Array(l),h=Array(l);for(let e,x=0;x<l;x++){e=t.yAxis.data[x],!0==t.yAxis.disabled&&(e.disabled=!0),s[x]=getYAxisTextList(r[x],t,a,n.type,x);let i=e.fontSize||a.fontSize;h[x]={position:e.position?e.position:"left",width:0},d[x]=s[x].map(function(t){return t=util.toFixed(t,6),t=e.format?e.format(+t):t,h[x].width=o(h[x].width,measureText(t,i)+5),t});let l=e.calibration?4*t.pixelRatio:0;h[x].width+=l+3*t.pixelRatio,!0===e.disabled&&(h[x].width=0)}}else{var s=[,],d=[,],h=[,];s[0]=getYAxisTextList(e,t,a,n.type),h[0]={position:"left",width:0};var i=t.yAxis.fontSize||a.fontSize;d[0]=s[0].map(function(e){return e=util.toFixed(e,6),e=t.yAxis.format?t.yAxis.format(+e):e,h[0].width=o(h[0].width,measureText(e,i)+5),e}),h[0].width+=3*t.pixelRatio,!0===t.yAxis.disabled?(h[0]={position:"left",width:0},t.yAxis.data[0]={disabled:!0}):t.yAxis.data[0]={disabled:!1,position:"left",max:t.yAxis.max,min:t.yAxis.min,format:t.yAxis.format}}return{rangesFormat:d,ranges:s,yAxisWidth:h}}function calTooltipYAxisData(e,t,a){let o=[].concat(a.chartData.yAxisData.ranges),n=a.height-a.area[0]-a.area[2],l=a.area[0],r=[];for(let s=0;s<o.length;s++){let t=o[s].shift(),i=o[s].pop(),d=t-(t-i)*(e-l)/n;d=a.yAxis.data[s].format?a.yAxis.data[s].format(+d):d.toFixed(0),r.push(d+"")}return r}function calMarkLineData(e,t){let a,o,n=t.height-t.area[0]-t.area[2];for(let l=0;l<e.length;l++){e[l].yAxisIndex=e[l].yAxisIndex?e[l].yAxisIndex:0;let i=[].concat(t.chartData.yAxisData.ranges[e[l].yAxisIndex]);a=i.pop(),o=i.shift();let r=n*(e[l].value-a)/(o-a);e[l].y=t.height-Math.round(r)-t.area[2]}return e}function contextRotate(e,t){var i=Math.PI;!0===t.rotateLock?!0!==t._rotate_&&(e.translate(t.height,0),e.rotate(90*i/180),t._rotate_=!0):(e.translate(t.height,0),e.rotate(90*i/180))}function drawPointShape(e,t,i,a,o){a.beginPath(),a.setStrokeStyle("#ffffff"),a.setLineWidth(1*o.pixelRatio),a.setFillStyle(t),"diamond"===i?e.forEach(function(e){null!==e&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y),a.lineTo(e.x,e.y****),a.lineTo(e.x****,e.y),a.lineTo(e.x,e.y-4.5))}):"circle"===i?e.forEach(function(e){null!==e&&(a.moveTo(e.x*****o.pixelRatio,e.y),a.arc(e.x,e.y,4*o.pixelRatio,0,2*Math.PI,!1))}):"rect"===i?e.forEach(function(e){null!==e&&(a.moveTo(e.x-3.5,e.y-3.5),a.rect(e.x-3.5,e.y-3.5,7,7))}):"triangle"==i&&e.forEach(function(e){null!==e&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y****),a.lineTo(e.x****,e.y****),a.lineTo(e.x,e.y-4.5))}),a.closePath(),a.fill(),a.stroke()}function drawRingTitle(e,t,i,a){var o=e.title.fontSize||t.titleFontSize,n=e.subtitle.fontSize||t.subtitleFontSize,l=e.title.name||"",r=e.subtitle.name||"",s=e.title.color||t.titleColor,d=e.subtitle.color||t.subtitleColor,h=l?o:0,x=r?n:0,c=5;if(r){var p=measureText(r,n),g=a.x-p/2+(e.subtitle.offsetX||0),y=a.y+n/2+(e.subtitle.offsetY||0);l&&(y+=(h+c)/2),i.beginPath(),i.setFontSize(n),i.setFillStyle(d),i.fillText(r,g,y),i.closePath(),i.stroke()}if(l){var f=measureText(l,o),u=a.x-f/2+(e.title.offsetX||0),m=a.y+o/2+(e.title.offsetY||0);r&&(m-=(x+c)/2),i.beginPath(),i.setFontSize(o),i.setFillStyle(s),i.fillText(l,u,m),i.closePath(),i.stroke()}}function drawPointText(e,t,i,a){var o=t.data;e.forEach(function(e,n){if(null!==e){a.beginPath(),a.setFontSize(t.textSize||i.fontSize),a.setFillStyle(t.textColor||"#666666");var l=o[n];"object"==typeof o[n]&&null!==o[n]&&(l=o[n].value);var r=t.format?t.format(l):l;a.fillText(r+"",e.x-measureText(r,t.textSize||i.fontSize)/2,e.y-2),a.closePath(),a.stroke()}})}function drawGaugeLabel(e,t,i,a,o,n){var l=Math.PI;t-=e.width/2+o.gaugeLabelTextMargin;let r=e.startAngle-e.endAngle+1,s=r/e.splitLine.splitNumber,d=e.endNumber-e.startNumber,h=d/e.splitLine.splitNumber,x=e.startAngle,c=e.startNumber;for(let r=0;r<e.splitLine.splitNumber+1;r++){var p={x:t*Math.cos(x*l),y:t*Math.sin(x*l)},g=e.labelFormat?e.labelFormat(c):c;p.x+=i.x-measureText(g)/2,p.y+=i.y;var y=p.x,f=p.y;n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(e.labelColor||"#666666"),n.fillText(g,y,f+o.fontSize/2),n.closePath(),n.stroke(),x+=s,2<=x&&(x%=2),c+=h}}function drawRadarLabel(e,t,i,a,o,n){var l=a.extra.radar||{};t+=o.radarLabelTextMargin,e.forEach(function(e,r){var s={x:t*Math.cos(e),y:t*Math.sin(e)},d=convertCoordinateOrigin(s.x,s.y,i),h=d.x,x=d.y;util.approximatelyEqual(s.x,0)?h-=measureText(a.categories[r]||"")/2:0>s.x&&(h-=measureText(a.categories[r]||"")),n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(l.labelColor||"#666666"),n.fillText(a.categories[r]||"",h,x+o.fontSize/2),n.closePath(),n.stroke()})}function drawPieText(e,t,a,o,i,n){var l=Math.cos,r=Math.sin,s=Math.min,d=Math.max,h=Math.PI,x=a.pieChartLinePadding,c=[],p=null,g=e.map(function(e){var t=e.format?e.format(+e._proportion_.toFixed(2)):util.toFixed(100*e._proportion_.toFixed(4))+"%";e._rose_proportion_&&(e._proportion_=e._rose_proportion_);var i=2*h-(e._start_+2*h*e._proportion_/2),a=e.color,o=e._radius_;return{arc:i,text:t,color:a,radius:o,textColor:e.textColor,textSize:e.textSize}});for(let h=0;h<g.length;h++){let e=g[h],t=l(e.arc)*(e.radius+x),i=r(e.arc)*(e.radius+x),o=l(e.arc)*e.radius,n=r(e.arc)*e.radius,y=0<=t?t+a.pieChartTextPadding:t-a.pieChartTextPadding,f=i,u=measureText(e.text),m=f;p&&util.isSameXCoordinateArea(p.start,{x:y})&&(0<y?m=s(f,p.start.y):0>t?m=d(f,p.start.y):0<f?m=d(f,p.start.y):m=s(f,p.start.y)),0>y&&(y-=u);let S={lineStart:{x:o,y:n},lineEnd:{x:t,y:i},start:{x:y,y:m},width:u,height:a.fontSize,text:e.text,color:e.color,textColor:e.textColor,textSize:e.textSize};p=avoidCollision(S,p),c.push(p)}for(let l=0;l<c.length;l++){let e=c[l],i=convertCoordinateOrigin(e.lineStart.x,e.lineStart.y,n),r=convertCoordinateOrigin(e.lineEnd.x,e.lineEnd.y,n),s=convertCoordinateOrigin(e.start.x,e.start.y,n);o.setLineWidth(1*t.pixelRatio),o.setFontSize(a.fontSize),o.beginPath(),o.setStrokeStyle(e.color),o.setFillStyle(e.color),o.moveTo(i.x,i.y);let d=0>e.start.x?s.x+e.width:s.x,x=0>e.start.x?s.x-5:s.x+5;o.quadraticCurveTo(r.x,r.y,d,s.y),o.moveTo(i.x,i.y),o.stroke(),o.closePath(),o.beginPath(),o.moveTo(s.x+e.width,s.y),o.arc(d,s.y,2,0,2*h),o.closePath(),o.fill(),o.beginPath(),o.setFontSize(e.textSize||a.fontSize),o.setFillStyle(e.textColor||"#666666"),o.fillText(e.text,x,s.y+3),o.closePath(),o.stroke(),o.closePath()}}function drawToolTipSplitLine(e,t,i,a){var o=t.extra.tooltip||{};o.gridType=null==o.gridType?"solid":o.gridType,o.dashLength=null==o.dashLength?4:o.dashLength;var n=t.area[0],l=t.height-t.area[2];if("dash"==o.gridType&&a.setLineDash([o.dashLength,o.dashLength]),a.setStrokeStyle(o.gridColor||"#cccccc"),a.setLineWidth(1*t.pixelRatio),a.beginPath(),a.moveTo(e,n),a.lineTo(e,l),a.stroke(),a.setLineDash([]),o.xAxisLabel){let n=t.categories[t.tooltip.index];a.setFontSize(i.fontSize);let r=measureText(n,i.fontSize),s=e-.5*r,d=l;a.beginPath(),a.setFillStyle(hexToRgb(o.labelBgColor||i.toolTipBackground,o.labelBgOpacity||i.toolTipOpacity)),a.setStrokeStyle(o.labelBgColor||i.toolTipBackground),a.setLineWidth(1*t.pixelRatio),a.rect(s-i.toolTipPadding,d,r+2*i.toolTipPadding,i.fontSize+2*i.toolTipPadding),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(o.labelFontColor||i.fontColor),a.fillText(n+"",s,d+i.toolTipPadding+i.fontSize),a.closePath(),a.stroke()}}function drawMarkLine(e,t,a){let o=assign({},{type:"solid",dashLength:4,data:[]},e.extra.markLine),n=e.area[3],l=e.width-e.padding[1],r=calMarkLineData(o.data,e);for(let s,d=0;d<r.length;d++)if(s=assign({},{lineColor:"#DE4A42",showLabel:!1,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,yAxisIndex:0},r[d]),"dash"==o.type&&a.setLineDash([o.dashLength,o.dashLength]),a.setStrokeStyle(s.lineColor),a.setLineWidth(1*e.pixelRatio),a.beginPath(),a.moveTo(n,s.y),a.lineTo(l,s.y),a.stroke(),a.setLineDash([]),s.showLabel){let i=e.yAxis.format?e.yAxis.format(+s.value):s.value;a.setFontSize(t.fontSize);let o=measureText(i,t.fontSize),n=e.padding[3]+t.yAxisTitleWidth-t.toolTipPadding,l=Math.max(e.area[3],o+2*t.toolTipPadding),r=l-n,d=s.y;a.setFillStyle(hexToRgb(s.labelBgColor,s.labelBgOpacity)),a.setStrokeStyle(s.labelBgColor),a.setLineWidth(1*e.pixelRatio),a.beginPath(),a.rect(n,d-.5*t.fontSize-t.toolTipPadding,r,t.fontSize+2*t.toolTipPadding),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(t.fontSize),a.setFillStyle(s.labelFontColor),a.fillText(i+"",n+(r-o)/2,d+.5*t.fontSize),a.stroke()}}function drawToolTipHorizentalLine(e,t,a,i){var o=Math.max,n=assign({},{gridType:"solid",dashLength:4},e.extra.tooltip),l=e.area[3],r=e.width-e.padding[1];if("dash"==n.gridType&&a.setLineDash([n.dashLength,n.dashLength]),a.setStrokeStyle(n.gridColor||"#cccccc"),a.setLineWidth(1*e.pixelRatio),a.beginPath(),a.moveTo(l,e.tooltip.offset.y),a.lineTo(r,e.tooltip.offset.y),a.stroke(),a.setLineDash([]),n.yAxisLabel){let l=calTooltipYAxisData(e.tooltip.offset.y,e.series,e,t,i),r=e.chartData.yAxisData.yAxisWidth,s=e.area[3],d=e.width-e.area[1];for(let h=0;h<l.length;h++){a.setFontSize(t.fontSize);let i,x,c,p=measureText(l[h],t.fontSize);"left"==r[h].position?(i=s-r[h].width,x=o(i,i+p+2*t.toolTipPadding)):(i=d,x=o(i+r[h].width,i+p+2*t.toolTipPadding)),c=x-i;let g=i+(c-p)/2,y=e.tooltip.offset.y;a.beginPath(),a.setFillStyle(hexToRgb(n.labelBgColor||t.toolTipBackground,n.labelBgOpacity||t.toolTipOpacity)),a.setStrokeStyle(n.labelBgColor||t.toolTipBackground),a.setLineWidth(1*e.pixelRatio),a.rect(i,y-.5*t.fontSize-t.toolTipPadding,c,t.fontSize+2*t.toolTipPadding),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(t.fontSize),a.setFillStyle(n.labelFontColor||t.fontColor),a.fillText(l[h],g,y+.5*t.fontSize),a.closePath(),a.stroke(),"left"==r[h].position?s-=r[h].width+e.yAxis.padding:d+=r[h].width+e.yAxis.padding}}}function drawToolTipSplitArea(e,t,i,a,o){var n=assign({},{activeBgColor:"#000000",activeBgOpacity:.08},t.extra.tooltip),l=t.area[0],r=t.height-t.area[2];a.beginPath(),a.setFillStyle(hexToRgb(n.activeBgColor,n.activeBgOpacity)),a.rect(e-o/2,l,o,r-l),a.closePath(),a.fill()}function drawToolTip(e,t,i,a,o){var n=Math.round,l=assign({},{showBox:!0,bgColor:"#000000",bgOpacity:.7,fontColor:"#FFFFFF"},i.extra.tooltip),r=4*i.pixelRatio,s=5*i.pixelRatio,d=8*i.pixelRatio,h=!1;("line"==i.type||"area"==i.type||"candle"==i.type||"mix"==i.type)&&drawToolTipSplitLine(i.tooltip.offset.x,i,a,o),t=assign({x:0,y:0},t),t.y-=8*i.pixelRatio;var x=e.map(function(e){return measureText(e.text,a.fontSize)}),c=r+s+4*a.toolTipPadding+Math.max.apply(null,x),p=2*a.toolTipPadding+e.length*a.toolTipLineHeight;!1==l.showBox||(t.x-Math.abs(i._scrollDistance_)+d+c>i.width&&(h=!0),p+t.y>i.height&&(t.y=i.height-p),o.beginPath(),o.setFillStyle(hexToRgb(l.bgColor||a.toolTipBackground,l.bgOpacity||a.toolTipOpacity)),h?(o.moveTo(t.x,t.y+10*i.pixelRatio),o.lineTo(t.x-d,t.y+10*i.pixelRatio-5*i.pixelRatio),o.lineTo(t.x-d,t.y),o.lineTo(t.x-d-n(c),t.y),o.lineTo(t.x-d-n(c),t.y+p),o.lineTo(t.x-d,t.y+p),o.lineTo(t.x-d,t.y+10*i.pixelRatio+5*i.pixelRatio),o.lineTo(t.x,t.y+10*i.pixelRatio)):(o.moveTo(t.x,t.y+10*i.pixelRatio),o.lineTo(t.x+d,t.y+10*i.pixelRatio-5*i.pixelRatio),o.lineTo(t.x+d,t.y),o.lineTo(t.x+d+n(c),t.y),o.lineTo(t.x+d+n(c),t.y+p),o.lineTo(t.x+d,t.y+p),o.lineTo(t.x+d,t.y+10*i.pixelRatio+5*i.pixelRatio),o.lineTo(t.x,t.y+10*i.pixelRatio)),o.closePath(),o.fill(),e.forEach(function(e,i){if(null!==e.color){o.beginPath(),o.setFillStyle(e.color);var n=t.x+d+2*a.toolTipPadding,l=t.y+(a.toolTipLineHeight-a.fontSize)/2+a.toolTipLineHeight*i+a.toolTipPadding+1;h&&(n=t.x-c-d+2*a.toolTipPadding),o.fillRect(n,l,r,a.fontSize),o.closePath()}}),e.forEach(function(e,i){var n=t.x+d+2*a.toolTipPadding+r+s;h&&(n=t.x-c-d+2*a.toolTipPadding+ +r+s);var x=t.y+(a.toolTipLineHeight-a.fontSize)/2+a.toolTipLineHeight*i+a.toolTipPadding;o.beginPath(),o.setFontSize(a.fontSize),o.setFillStyle(l.fontColor),o.fillText(e.text,n,x+a.fontSize),o.closePath(),o.stroke()}))}function drawYAxisTitle(e,t,i,a){var o=i.xAxisHeight+(t.height-i.xAxisHeight-measureText(e))/2;a.save(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(t.yAxis.titleFontColor||"#333333"),a.translate(0,t.height),a.rotate(-90*Math.PI/180),a.fillText(e,o,t.padding[3]+.5*i.fontSize),a.closePath(),a.stroke(),a.restore()}function drawColumnDataPoints(e,t,i,a){let o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=t.chartData.xAxisData,l=n.xAxisPoints,r=n.eachSpacing,s=assign({},{type:"group",width:r/2,meter:{border:4,fillColor:"#FFFFFF"}},t.extra.column),d=[];return a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===o&&drawToolTipSplitArea(t.tooltip.offset.x,t,i,a,r),e.forEach(function(n,h){let x,c,p;x=[].concat(t.chartData.yAxisData.ranges[n.index]),c=x.pop(),p=x.shift();var g=n.data;switch(s.type){case"group":var y=getDataPoints(g,c,p,l,r,t,i,o),f=getStackDataPoints(g,c,p,l,r,t,i,h,e,o);d.push(f),y=fixColumeData(y,r,e.length,h,i,t),y.forEach(function(e){if(null!==e){a.beginPath(),a.setStrokeStyle(e.color||n.color),a.setLineWidth(1),a.setFillStyle(e.color||n.color);var i=e.x-e.width/2,o=t.height-e.y-t.area[2];a.moveTo(i-1,e.y),a.lineTo(i+e.width-2,e.y),a.lineTo(i+e.width-2,t.height-t.area[2]),a.lineTo(i,t.height-t.area[2]),a.lineTo(i,e.y),a.closePath(),a.stroke(),a.fill()}});break;case"stack":var y=getStackDataPoints(g,c,p,l,r,t,i,h,e,o);d.push(y),y=fixColumeStackData(y,r,e.length,h,i,t,e),y.forEach(function(e){if(null!==e){a.beginPath(),a.setFillStyle(e.color||n.color);var i=e.x-e.width/2+1,o=t.height-e.y-t.area[2],l=t.height-e.y0-t.area[2];0<h&&(o-=l),a.moveTo(i,e.y),a.fillRect(i,e.y,e.width-2,o),a.closePath(),a.fill()}});break;case"meter":var y=getDataPoints(g,c,p,l,r,t,i,o);d.push(y),y=fixColumeMeterData(y,r,e.length,h,i,t,s.meter.border),0==h?y.forEach(function(e){if(null!==e){a.beginPath(),a.setFillStyle(s.meter.fillColor);var i=e.x-e.width/2,o=t.height-e.y-t.area[2];a.moveTo(i,e.y),a.fillRect(i,e.y,e.width,o),a.closePath(),a.fill(),0<s.meter.border&&(a.beginPath(),a.setStrokeStyle(n.color),a.setLineWidth(s.meter.border*t.pixelRatio),a.moveTo(i+.5*s.meter.border,e.y+o),a.lineTo(i+.5*s.meter.border,e.y+.5*s.meter.border),a.lineTo(i+e.width-.5*s.meter.border,e.y+.5*s.meter.border),a.lineTo(i+e.width-.5*s.meter.border,e.y+o),a.stroke())}}):y.forEach(function(e){if(null!==e){a.beginPath(),a.setFillStyle(e.color||n.color);var i=e.x-e.width/2,o=t.height-e.y-t.area[2];a.moveTo(i,e.y),a.fillRect(i,e.y,e.width,o),a.closePath(),a.fill()}});}}),!1!==t.dataLabel&&1===o&&e.forEach(function(n,d){let h,x,c;h=[].concat(t.chartData.yAxisData.ranges[n.index]),x=h.pop(),c=h.shift();var p=n.data;switch(s.type){case"group":var g=getDataPoints(p,x,c,l,r,t,i,o);g=fixColumeData(g,r,e.length,d,i,t),drawPointText(g,n,i,a);break;case"stack":var g=getStackDataPoints(p,x,c,l,r,t,i,d,e,o);drawPointText(g,n,i,a);break;case"meter":var g=getDataPoints(p,x,c,l,r,t,i,o);drawPointText(g,n,i,a);}}),a.restore(),{xAxisPoints:l,calPoints:d,eachSpacing:r}}function drawCandleDataPoints(e,t,i,a,o){var n=5<arguments.length&&void 0!==arguments[5]?arguments[5]:1,l=assign({},{color:{},average:{}},i.extra.candle);l.color=assign({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},l.color),l.average=assign({},{show:!1,name:[],day:[],color:a.colors},l.average),i.extra.candle=l;let r=i.chartData.xAxisData,s=r.xAxisPoints,d=r.eachSpacing,h=[];return o.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&o.translate(i._scrollDistance_,0),l.average.show&&t.forEach(function(e){let t,l,r;t=[].concat(i.chartData.yAxisData.ranges[e.index]),l=t.pop(),r=t.shift();var h=e.data,x=getDataPoints(h,l,r,s,d,i,a,n),c=splitPoints(x);c.forEach(function(t){o.beginPath(),o.setStrokeStyle(e.color),o.setLineWidth(1),1===t.length?(o.moveTo(t[0].x,t[0].y),o.arc(t[0].x,t[0].y,1,0,2*Math.PI)):(o.moveTo(t[0].x,t[0].y),t.forEach(function(e,i){if(0<i){var a=createCurveControlPoints(t,i-1);o.bezierCurveTo(a.ctrA.x,a.ctrA.y,a.ctrB.x,a.ctrB.y,e.x,e.y)}}),o.moveTo(t[0].x,t[0].y)),o.closePath(),o.stroke()})}),e.forEach(function(e){let t,r,x;t=[].concat(i.chartData.yAxisData.ranges[e.index]),r=t.pop(),x=t.shift();var c=e.data,p=getCandleDataPoints(c,r,x,s,d,i,a,n);h.push(p);var g=splitPoints(p);g=g[0],g.forEach(function(e,t){o.beginPath(),0<c[t][1]-c[t][0]?(o.setStrokeStyle(l.color.upLine),o.setFillStyle(l.color.upFill),o.setLineWidth(1*i.pixelRatio),o.moveTo(e[3].x,e[3].y),o.lineTo(e[1].x,e[1].y),o.lineTo(e[1].x-d/4,e[1].y),o.lineTo(e[0].x-d/4,e[0].y),o.lineTo(e[0].x,e[0].y),o.lineTo(e[2].x,e[2].y),o.lineTo(e[0].x,e[0].y),o.lineTo(e[0].x+d/4,e[0].y),o.lineTo(e[1].x+d/4,e[1].y),o.lineTo(e[1].x,e[1].y),o.moveTo(e[3].x,e[3].y)):(o.setStrokeStyle(l.color.downLine),o.setFillStyle(l.color.downFill),o.setLineWidth(1*i.pixelRatio),o.moveTo(e[3].x,e[3].y),o.lineTo(e[0].x,e[0].y),o.lineTo(e[0].x-d/4,e[0].y),o.lineTo(e[1].x-d/4,e[1].y),o.lineTo(e[1].x,e[1].y),o.lineTo(e[2].x,e[2].y),o.lineTo(e[1].x,e[1].y),o.lineTo(e[1].x+d/4,e[1].y),o.lineTo(e[0].x+d/4,e[0].y),o.lineTo(e[0].x,e[0].y),o.moveTo(e[3].x,e[3].y)),o.closePath(),o.fill(),o.stroke()})}),o.restore(),{xAxisPoints:s,calPoints:h,eachSpacing:d}}function drawAreaDataPoints(e,t,i,a){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=assign({},{type:"straight",opacity:.2,addLine:!1,width:2},t.extra.area);let l=t.chartData.xAxisData,r=l.xAxisPoints,s=l.eachSpacing,d=t.height-t.area[2],h=[];return a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),e.forEach(function(e,l){let x,c,p;x=[].concat(t.chartData.yAxisData.ranges[e.index]),c=x.pop(),p=x.shift();let g=e.data,y=getDataPoints(g,c,p,r,s,t,i,o);h.push(y);let f=splitPoints(y);for(let o,r=0;r<f.length;r++){if(o=f[r],a.beginPath(),a.setStrokeStyle(hexToRgb(e.color,n.opacity)),a.setFillStyle(hexToRgb(e.color,n.opacity)),a.setLineWidth(n.width*t.pixelRatio),1<o.length){let e=o[0],t=o[o.length-1];a.moveTo(e.x,e.y),"curve"===n.type?o.forEach(function(e,t){if(0<t){let i=createCurveControlPoints(o,t-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,e.x,e.y)}}):o.forEach(function(e,t){0<t&&a.lineTo(e.x,e.y)}),a.lineTo(t.x,d),a.lineTo(e.x,d),a.lineTo(e.x,e.y)}else{let e=o[0];a.moveTo(e.x-s/2,e.y),a.lineTo(e.x+s/2,e.y),a.lineTo(e.x+s/2,d),a.lineTo(e.x-s/2,d),a.moveTo(e.x-s/2,e.y)}if(a.closePath(),a.fill(),n.addLine){if("dash"==e.lineType){let i=e.dashLength?e.dashLength:8;i*=t.pixelRatio,a.setLineDash([i,i])}a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(n.width*t.pixelRatio),1===o.length?(a.moveTo(o[0].x,o[0].y),a.arc(o[0].x,o[0].y,1,0,2*Math.PI)):(a.moveTo(o[0].x,o[0].y),"curve"===n.type?o.forEach(function(e,t){if(0<t){let i=createCurveControlPoints(o,t-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,e.x,e.y)}}):o.forEach(function(e,t){0<t&&a.lineTo(e.x,e.y)}),a.moveTo(o[0].x,o[0].y)),a.stroke(),a.setLineDash([])}}if(!1!==t.dataPointShape){var u=i.dataPointShape[l%i.dataPointShape.length];drawPointShape(y,e.color,u,a,t)}}),!1!==t.dataLabel&&1===o&&e.forEach(function(e){let n,l,d;n=[].concat(t.chartData.yAxisData.ranges[e.index]),l=n.pop(),d=n.shift();var h=e.data,x=getDataPoints(h,l,d,r,s,t,i,o);drawPointText(x,e,i,a)}),a.restore(),{xAxisPoints:r,calPoints:h,eachSpacing:s}}function drawLineDataPoints(e,t,i,a){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=assign({},{type:"straight",width:2},t.extra.line);n.width*=t.pixelRatio;let l=t.chartData.xAxisData,r=l.xAxisPoints,s=l.eachSpacing;var d=[];return a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),e.forEach(function(e,l){let h,x,c;h=[].concat(t.chartData.yAxisData.ranges[e.index]),x=h.pop(),c=h.shift();var p=e.data,g=getDataPoints(p,x,c,r,s,t,i,o);d.push(g);var y=splitPoints(g);if("dash"==e.lineType){let i=e.dashLength?e.dashLength:8;i*=t.pixelRatio,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(n.width),y.forEach(function(e){1===e.length?(a.moveTo(e[0].x,e[0].y),a.arc(e[0].x,e[0].y,1,0,2*Math.PI)):(a.moveTo(e[0].x,e[0].y),"curve"===n.type?e.forEach(function(t,i){if(0<i){var o=createCurveControlPoints(e,i-1);a.bezierCurveTo(o.ctrA.x,o.ctrA.y,o.ctrB.x,o.ctrB.y,t.x,t.y)}}):e.forEach(function(e,t){0<t&&a.lineTo(e.x,e.y)}),a.moveTo(e[0].x,e[0].y))}),a.stroke(),a.setLineDash([]),!1!==t.dataPointShape){var f=i.dataPointShape[l%i.dataPointShape.length];drawPointShape(g,e.color,f,a,t)}}),!1!==t.dataLabel&&1===o&&e.forEach(function(e){let n,l,d;n=[].concat(t.chartData.yAxisData.ranges[e.index]),l=n.pop(),d=n.shift();var h=e.data,x=getDataPoints(h,l,d,r,s,t,i,o);drawPointText(x,e,i,a)}),a.restore(),{xAxisPoints:r,calPoints:d,eachSpacing:s}}function drawMixDataPoints(e,t,i,a){var o=Math.PI;let n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,l=t.chartData.xAxisData,r=l.xAxisPoints,s=l.eachSpacing,d=t.height-t.area[2],h=[];var x=0,c=0;if(e.forEach(function(e){"column"==e.type&&(c+=1)}),a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),e.forEach(function(e,l){let p,g,y;p=[].concat(t.chartData.yAxisData.ranges[e.index]),g=p.pop(),y=p.shift();var f=e.data,u=getDataPoints(f,g,y,r,s,t,i,n);if(h.push(u),"column"==e.type&&(u=fixColumeData(u,s,c,x,i,t),u.forEach(function(i){if(null!==i){a.beginPath(),a.setStrokeStyle(i.color||e.color),a.setLineWidth(1),a.setFillStyle(i.color||e.color);var o=i.x-i.width/2,n=t.height-i.y-t.area[2];a.moveTo(o,i.y),a.moveTo(o-1,i.y),a.lineTo(o+i.width-2,i.y),a.lineTo(o+i.width-2,t.height-t.area[2]),a.lineTo(o,t.height-t.area[2]),a.lineTo(o,i.y),a.closePath(),a.stroke(),a.fill(),a.closePath(),a.fill()}}),x+=1),"area"==e.type){let o=splitPoints(u);for(let n,l=0;l<o.length;l++){if(n=o[l],a.beginPath(),a.setStrokeStyle(e.color),a.setFillStyle(hexToRgb(e.color,.2)),a.setLineWidth(2*t.pixelRatio),1<n.length){var m=n[0];let t=n[n.length-1];a.moveTo(m.x,m.y),"curve"===e.style?n.forEach(function(e,t){if(0<t){var i=createCurveControlPoints(n,t-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,e.x,e.y)}}):n.forEach(function(e,t){0<t&&a.lineTo(e.x,e.y)}),a.lineTo(t.x,d),a.lineTo(m.x,d),a.lineTo(m.x,m.y)}else{let e=n[0];a.moveTo(e.x-s/2,e.y),a.lineTo(e.x+s/2,e.y),a.lineTo(e.x+s/2,d),a.lineTo(e.x-s/2,d),a.moveTo(e.x-s/2,e.y)}a.closePath(),a.fill()}}if("line"==e.type){var S=splitPoints(u);S.forEach(function(i){if("dash"==e.lineType){let i=e.dashLength?e.dashLength:8;i*=t.pixelRatio,a.setLineDash([i,i])}a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(2*t.pixelRatio),1===i.length?(a.moveTo(i[0].x,i[0].y),a.arc(i[0].x,i[0].y,1,0,2*o)):(a.moveTo(i[0].x,i[0].y),"curve"==e.style?i.forEach(function(e,t){if(0<t){var o=createCurveControlPoints(i,t-1);a.bezierCurveTo(o.ctrA.x,o.ctrA.y,o.ctrB.x,o.ctrB.y,e.x,e.y)}}):i.forEach(function(e,t){0<t&&a.lineTo(e.x,e.y)}),a.moveTo(i[0].x,i[0].y)),a.stroke(),a.setLineDash([])})}if("point"==e.type&&u.forEach(function(i){i&&(a.beginPath(),a.setFillStyle(e.color),a.setStrokeStyle("#FFFFFF"),a.setLineWidth(1*t.pixelRatio),a.moveTo(i.x*****t.pixelRatio,i.y),a.arc(i.x,i.y,4*t.pixelRatio,0,2*o),a.closePath(),a.fill(),a.stroke())}),!0==e.addPoint&&"column"!==e.type){var T=i.dataPointShape[l%i.dataPointShape.length];drawPointShape(u,e.color,T,a,t)}}),!1!==t.dataLabel&&1===n){var x=0;e.forEach(function(e){let o,l,d;o=[].concat(t.chartData.yAxisData.ranges[e.index]),l=o.pop(),d=o.shift();var h=e.data,p=getDataPoints(h,l,d,r,s,t,i,n);"column"===e.type?(p=fixColumeData(p,s,c,x,i,t),drawPointText(p,e,i,a),x+=1):drawPointText(p,e,i,a)})}return a.restore(),{xAxisPoints:r,calPoints:h,eachSpacing:s}}function drawToolTipBridge(e,t,i,a,o,n){var l=e.extra.tooltip||{};l.horizentalLine&&e.tooltip&&1===a&&("line"==e.type||"area"==e.type||"column"==e.type||"candle"==e.type||"mix"==e.type)&&drawToolTipHorizentalLine(e,t,i,o,n),i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&i.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===a&&drawToolTip(e.tooltip.textList,e.tooltip.offset,e,t,i,o,n),i.restore()}function drawXAxis(e,t,i,a){var o=Math.ceil;let n=t.chartData.xAxisData,l=n.xAxisPoints,r=n.startX,s=n.endX,d=n.eachSpacing;var h="center";("line"==t.type||"area"==t.type)&&(h=t.xAxis.boundaryGap);var x=t.height-t.area[2],c=t.area[0];if(t.enableScroll&&t.xAxis.scrollShow){var p=t.height-t.area[2]+i.xAxisHeight,g=s-r,y=d*(l.length-1),f=0;t._scrollDistance_&&(f=-t._scrollDistance_*g/y),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*t.pixelRatio),a.setStrokeStyle(t.xAxis.scrollBackgroundColor||"#EFEBEF"),a.moveTo(r,p),a.lineTo(s,p),a.stroke(),a.closePath(),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*t.pixelRatio),a.setStrokeStyle(t.xAxis.scrollColor||"#A6A6A6"),a.moveTo(r+f,p),a.lineTo(r+f+g*g/y,p),a.stroke(),a.closePath(),a.setLineCap("butt")}if(a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&a.translate(t._scrollDistance_,0),!0===t.xAxis.calibration&&(a.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*t.pixelRatio),l.forEach(function(e,i){0<i&&(a.beginPath(),a.moveTo(e-d/2,x),a.lineTo(e-d/2,x+3*t.pixelRatio),a.closePath(),a.stroke())})),!0!==t.xAxis.disableGrid&&(a.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*t.pixelRatio),"dash"==t.xAxis.gridType&&a.setLineDash([t.xAxis.dashLength,t.xAxis.dashLength]),t.xAxis.gridEval=t.xAxis.gridEval||1,l.forEach(function(e,i){0==i%t.xAxis.gridEval&&(a.beginPath(),a.moveTo(e,x),a.lineTo(e,c),a.stroke())}),a.setLineDash([])),!0!==t.xAxis.disabled){let n=e.length;t.xAxis.labelCount&&(n=t.xAxis.itemCount?o(e.length/t.xAxis.itemCount*t.xAxis.labelCount):t.xAxis.labelCount,n-=1);let r=o(e.length/n),s=[],c=e.length;for(let t=0;t<c;t++)0==t%r?s.push(e[t]):s.push("");s[c-1]=e[c-1];var u=t.xAxis.fontSize||i.fontSize;0===i._xAxisTextAngle_?s.forEach(function(e,o){var n=-measureText(e,u)/2;"center"==h&&(n+=d/2);var r=0;t.xAxis.scrollShow&&(r=6*t.pixelRatio),a.beginPath(),a.setFontSize(u),a.setFillStyle(t.xAxis.fontColor||"#666666"),a.fillText(e,l[o]+n,x+u+(i.xAxisHeight-r-u)/2),a.closePath(),a.stroke()}):s.forEach(function(e,o){a.save(),a.beginPath(),a.setFontSize(u),a.setFillStyle(t.xAxis.fontColor||"#666666");var n=measureText(e),r=-n;"center"==h&&(r+=d/2);var s=calRotateTranslate(l[o]+d/2,x+u/2+5,t.height),c=s.transX+15,p=s.transY;a.rotate(-1*i._xAxisTextAngle_),a.translate(c,p),a.fillText(e,l[o]+r,x+u+5),a.closePath(),a.stroke(),a.restore()})}a.restore(),t.xAxis.axisLine&&(a.beginPath(),a.setStrokeStyle(t.xAxis.axisLineColor),a.setLineWidth(1*t.pixelRatio),a.moveTo(r,t.height-t.area[2]),a.lineTo(s,t.height-t.area[2]),a.stroke())}function drawYAxisGrid(e,t,a,i){if(!0===t.yAxis.disableGrid)return;let o=t.height-t.area[0]-t.area[2],n=o/a.yAxisSplit,l=t.area[3],r=t.chartData.xAxisData.xAxisPoints,s=t.chartData.xAxisData.eachSpacing,d=s*(r.length-1),h=[];for(let o=0;o<a.yAxisSplit+1;o++)h.push(t.height-t.area[2]-n*o);i.save(),t._scrollDistance_&&0!==t._scrollDistance_&&i.translate(t._scrollDistance_,0),"dash"==t.yAxis.gridType&&i.setLineDash([t.yAxis.dashLength,t.yAxis.dashLength]),i.setStrokeStyle(t.yAxis.gridColor),i.setLineWidth(1*t.pixelRatio),h.forEach(function(e){i.beginPath(),i.moveTo(l,e),i.lineTo(l+d,e),i.stroke()}),i.setLineDash([]),i.restore()}function drawYAxis(e,t,a,o){if(!0===t.yAxis.disabled)return;var i=t.height-t.area[0]-t.area[2],n=i/a.yAxisSplit,l=t.area[3],r=t.width-t.area[1],s=t.height-t.area[2],d=s+a.xAxisHeight;t.xAxis.scrollShow&&(d-=3*t.pixelRatio),t.xAxis.rotateLabel&&(d=t.height-t.area[2]+3),o.beginPath(),o.setFillStyle(t.background||"#ffffff"),0>t._scrollDistance_&&o.fillRect(0,0,l,d),!0==t.enableScroll&&o.fillRect(r,0,t.width,d),o.closePath(),o.stroke();var h=[];for(let l=0;l<=a.yAxisSplit;l++)h.push(t.area[0]+n*l);let x=t.area[3],c=t.width-t.area[1];for(let n,l=0;l<t.yAxis.data.length;l++)if(n=t.yAxis.data[l],!0!==n.disabled){let e=t.chartData.yAxisData.rangesFormat[l],i=n.fontSize||a.fontSize,r=t.chartData.yAxisData.yAxisWidth[l];if(e.forEach(function(e,a){var l=h[a]?h[a]:s;o.beginPath(),o.setFontSize(i),o.setLineWidth(1*t.pixelRatio),o.setStrokeStyle(n.axisLineColor||"#cccccc"),o.setFillStyle(n.fontColor||"#666666"),"left"==r.position?(o.fillText(e+"",x-r.width,l+i/2),!0==n.calibration&&(o.moveTo(x,l),o.lineTo(x-3*t.pixelRatio,l))):(o.fillText(e+"",c+4*t.pixelRatio,l+i/2),!0==n.calibration&&(o.moveTo(c,l),o.lineTo(c+3*t.pixelRatio,l))),o.closePath(),o.stroke()}),!1!==n.axisLine&&(o.beginPath(),o.setStrokeStyle(n.axisLineColor||"#cccccc"),o.setLineWidth(1*t.pixelRatio),"left"==r.position?(o.moveTo(x,t.height-t.area[2]),o.lineTo(x,t.area[0])):(o.moveTo(c,t.height-t.area[2]),o.lineTo(c,t.area[0])),o.stroke()),t.yAxis.showTitle){let e=n.titleFontSize||a.fontSize,i=n.title;o.beginPath(),o.setFontSize(e),o.setFillStyle(n.titleFontColor||"#666666"),"left"==r.position?o.fillText(i,x-measureText(i,e)/2,t.area[0]-10*t.pixelRatio):o.fillText(i,c-measureText(i,e)/2,t.area[0]-10*t.pixelRatio),o.closePath(),o.stroke()}"left"==r.position?x-=r.width+t.yAxis.padding:c+=r.width+t.yAxis.padding}}function drawLegend(e,t,i,a,o){if(!1===t.legend.show)return;let n=o.legendData,l=n.points,r=n.area,s=t.legend.padding,d=t.legend.fontSize,h=15*t.pixelRatio,x=5*t.pixelRatio,c=t.legend.itemGap,p=Math.max(t.legend.lineHeight*t.pixelRatio,d);a.beginPath(),a.setLineWidth(t.legend.borderWidth),a.setStrokeStyle(t.legend.borderColor),a.setFillStyle(t.legend.backgroundColor),a.moveTo(r.start.x,r.start.y),a.rect(r.start.x,r.start.y,r.width,r.height),a.closePath(),a.fill(),a.stroke(),l.forEach(function(e,o){let l=0,g=0;l=n.widthArr[o],g=n.heightArr[o];let y=0,f=0;"top"==t.legend.position||"bottom"==t.legend.position?(y=r.start.x+(r.width-l)/2,f=r.start.y+s+o*p):(l=0==o?0:n.widthArr[o-1],y=r.start.x+s+l,f=r.start.y+s+(r.height-g)/2),a.setFontSize(i.fontSize);for(let n,l=0;l<e.length;l++){switch(n=e[l],n.area=[0,0,0,0],n.area[0]=y,n.area[1]=f,n.area[3]=f+p,a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(n.show?n.color:t.legend.hiddenColor),a.setFillStyle(n.show?n.color:t.legend.hiddenColor),n.legendShape){case"line":a.moveTo(y,f+.5*p-2*t.pixelRatio),a.fillRect(y,f+.5*p-2*t.pixelRatio,15*t.pixelRatio,4*t.pixelRatio);break;case"triangle":a.moveTo(y+7.5*t.pixelRatio,f+.5*p-5*t.pixelRatio),a.lineTo(y+2.5*t.pixelRatio,f+.5*p+5*t.pixelRatio),a.lineTo(y+12.5*t.pixelRatio,f+.5*p+5*t.pixelRatio),a.lineTo(y+7.5*t.pixelRatio,f+.5*p-5*t.pixelRatio);break;case"diamond":a.moveTo(y+7.5*t.pixelRatio,f+.5*p-5*t.pixelRatio),a.lineTo(y+2.5*t.pixelRatio,f+.5*p),a.lineTo(y+7.5*t.pixelRatio,f+.5*p+5*t.pixelRatio),a.lineTo(y+12.5*t.pixelRatio,f+.5*p),a.lineTo(y+7.5*t.pixelRatio,f+.5*p-5*t.pixelRatio);break;case"circle":a.moveTo(y+7.5*t.pixelRatio,f+.5*p),a.arc(y+7.5*t.pixelRatio,f+.5*p,5*t.pixelRatio,0,2*Math.PI);break;case"rect":a.moveTo(y,f+.5*p-5*t.pixelRatio),a.fillRect(y,f+.5*p-5*t.pixelRatio,15*t.pixelRatio,10*t.pixelRatio);break;default:a.moveTo(y,f+.5*p-5*t.pixelRatio),a.fillRect(y,f+.5*p-5*t.pixelRatio,15*t.pixelRatio,10*t.pixelRatio);}a.closePath(),a.fill(),a.stroke(),y+=h+x;a.beginPath(),a.setFontSize(d),a.setFillStyle(n.show?t.legend.fontColor:t.legend.hiddenColor),a.fillText(n.name,y,f+(.5*p+.5*d-2)),a.closePath(),a.stroke(),"top"==t.legend.position||"bottom"==t.legend.position?(y+=measureText(n.name,d)+c,n.area[2]=y):(n.area[2]=y+measureText(n.name,d)+c,y-=h+x,f+=p)}})}function drawPieDataPoints(e,t,a,o){var n=Math.PI,l=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,r=assign({},{activeOpacity:.5,activeRadius:10*t.pixelRatio,offsetAngle:0,labelWidth:15*t.pixelRatio,ringWidth:0,border:!1,borderWidth:2,borderColor:"#FFFFFF"},t.extra.pie),s={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2};0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius);var d=Math.min((t.width-t.area[1]-t.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(t.height-t.area[0]-t.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);e=getPieDataPoints(e,d,l);var h=r.activeRadius;if(e=e.map(function(e){return e._start_+=r.offsetAngle*n/180,e}),e.forEach(function(e,i){t.tooltip&&t.tooltip.index==i&&(o.beginPath(),o.setFillStyle(hexToRgb(e.color,t.extra.pie.activeOpacity||.5)),o.moveTo(s.x,s.y),o.arc(s.x,s.y,e._radius_+h,e._start_,e._start_+2*e._proportion_*n),o.closePath(),o.fill()),o.beginPath(),o.setLineWidth(r.borderWidth*t.pixelRatio),o.lineJoin="round",o.setStrokeStyle(r.borderColor),o.setFillStyle(e.color),o.moveTo(s.x,s.y),o.arc(s.x,s.y,e._radius_,e._start_,e._start_+2*e._proportion_*n),o.closePath(),o.fill(),!0==r.border&&o.stroke()}),"ring"===t.type){var x=.6*d;"number"==typeof t.extra.pie.ringWidth&&0<t.extra.pie.ringWidth&&(x=Math.max(0,d-t.extra.pie.ringWidth)),o.beginPath(),o.setFillStyle(t.background||"#ffffff"),o.moveTo(s.x,s.y),o.arc(s.x,s.y,x,0,2*n),o.closePath(),o.fill()}if(!1!==t.dataLabel&&1===l){for(var c=!1,p=0,g=e.length;p<g;p++)if(0<e[p].data){c=!0;break}c&&drawPieText(e,t,a,o,d,s)}return 1===l&&"ring"===t.type&&drawRingTitle(t,a,o,s),{center:s,radius:d,series:e}}function drawRoseDataPoints(e,t,a,o){var n=Math.PI,l=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,r=assign({},{type:"area",activeOpacity:.5,activeRadius:10*t.pixelRatio,offsetAngle:0,labelWidth:15*t.pixelRatio,border:!1,borderWidth:2,borderColor:"#FFFFFF"},t.extra.rose);0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius);var s={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2},d=Math.min((t.width-t.area[1]-t.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(t.height-t.area[0]-t.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding),h=r.minRadius||.5*d;e=getRoseDataPoints(e,r.type,h,d,l);var x=r.activeRadius;if(e=e.map(function(e){return e._start_+=(r.offsetAngle||0)*n/180,e}),e.forEach(function(e,i){t.tooltip&&t.tooltip.index==i&&(o.beginPath(),o.setFillStyle(hexToRgb(e.color,r.activeOpacity||.5)),o.moveTo(s.x,s.y),o.arc(s.x,s.y,x+e._radius_,e._start_,e._start_+2*e._rose_proportion_*n),o.closePath(),o.fill()),o.beginPath(),o.setLineWidth(r.borderWidth*t.pixelRatio),o.lineJoin="round",o.setStrokeStyle(r.borderColor),o.setFillStyle(e.color),o.moveTo(s.x,s.y),o.arc(s.x,s.y,e._radius_,e._start_,e._start_+2*e._rose_proportion_*n),o.closePath(),o.fill(),!0==r.border&&o.stroke()}),!1!==t.dataLabel&&1===l){for(var c=!1,p=0,g=e.length;p<g;p++)if(0<e[p].data){c=!0;break}c&&drawPieText(e,t,a,o,d,s)}return{center:s,radius:d,series:e}}function drawArcbarDataPoints(e,t,i,a){var o=Math.PI,n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,l=assign({},{startAngle:.75,endAngle:.25,type:"default",width:12*t.pixelRatio},t.extra.arcbar);e=getArcbarDataPoints(e,l,n);var r={x:t.width/2,y:t.height/2},s=Math.min(r.x,r.y);s-=5*t.pixelRatio,s-=l.width/2,a.setLineWidth(l.width),a.setStrokeStyle(l.backgroundColor||"#E9E9E9"),a.setLineCap("round"),a.beginPath(),"default"==l.type?a.arc(r.x,r.y,s,l.startAngle*o,l.endAngle*o,!1):a.arc(r.x,r.y,s,0,2*o,!1),a.stroke();for(let n,d=0;d<e.length;d++)n=e[d],a.setLineWidth(l.width),a.setStrokeStyle(n.color),a.setLineCap("round"),a.beginPath(),a.arc(r.x,r.y,s,l.startAngle*o,n._proportion_*o,!1),a.stroke();return drawRingTitle(t,i,a,r),{center:r,radius:s,series:e}}function drawGaugeDataPoints(e,t,a,i,o){var n=Math.PI,l=5<arguments.length&&void 0!==arguments[5]?arguments[5]:1,r=assign({},{type:"default",startAngle:.75,endAngle:.25,width:15,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},a.extra.gauge);null==r.oldAngle&&(r.oldAngle=r.startAngle),null==r.oldData&&(r.oldData=0),e=getGaugeAxisPoints(e,r.startAngle,r.endAngle);var s={x:a.width/2,y:a.height/2},d=Math.min(s.x,s.y);d-=5*a.pixelRatio,d-=r.width/2;var h=d-r.width,x=0;if("progress"==r.type){var c=d-3*r.width;o.beginPath();let e=o.createLinearGradient(s.x,s.y-c,s.x,s.y+c);e.addColorStop("0",hexToRgb(t[0].color,.3)),e.addColorStop("1.0",hexToRgb("#FFFFFF",.1)),o.setFillStyle(e),o.arc(s.x,s.y,c,0,2*n,!1),o.fill(),o.setLineWidth(r.width),o.setStrokeStyle(hexToRgb(t[0].color,.3)),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,h,r.startAngle*n,r.endAngle*n,!1),o.stroke(),x=r.startAngle-r.endAngle+1;let i=x/r.splitLine.splitNumber,p=x/r.splitLine.splitNumber/r.splitLine.childNumber,g=-d-.5*r.width-r.splitLine.fixRadius,y=-d-r.width-r.splitLine.fixRadius+r.splitLine.width;o.save(),o.translate(s.x,s.y),o.rotate((r.startAngle-1)*n);let f=r.splitLine.splitNumber*r.splitLine.childNumber+1,u=t[0].data*l;for(let e=0;e<f;e++)o.beginPath(),u>e/f?o.setStrokeStyle(hexToRgb(t[0].color,1)):o.setStrokeStyle(hexToRgb(t[0].color,.3)),o.setLineWidth(3*a.pixelRatio),o.moveTo(g,0),o.lineTo(y,0),o.stroke(),o.rotate(p*n);o.restore(),t=getArcbarDataPoints(t,r,l),o.setLineWidth(r.width);let m=o.createLinearGradient(s.x-h,s.y,s.x+h,s.y);m.addColorStop("0",hexToRgb(t[0].color,.2)),m.addColorStop("1.0",hexToRgb(t[0].color,1)),o.setStrokeStyle(m),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,h,r.startAngle*n,t[0]._proportion_*n,!1),o.stroke();let S=d-2.5*r.width;o.save(),o.translate(s.x,s.y),o.rotate((t[0]._proportion_-1)*n),o.beginPath(),o.setLineWidth(r.width/3);let T=o.createLinearGradient(0,.6*-S,0,.6*S);T.addColorStop("0",hexToRgb("#FFFFFF",0)),T.addColorStop("0.5",hexToRgb(t[0].color,1)),T.addColorStop("1.0",hexToRgb("#FFFFFF",0)),o.setStrokeStyle(T),o.arc(0,0,S,.85*n,1.15*n,!1),o.stroke(),o.beginPath(),o.setLineWidth(1),o.setStrokeStyle(t[0].color),o.setFillStyle(t[0].color),o.moveTo(-S-r.width/3/2,-4),o.lineTo(-S-r.width/3/2-4,0),o.lineTo(-S-r.width/3/2,4),o.lineTo(-S-r.width/3/2,-4),o.stroke(),o.fill(),o.restore()}else{o.setLineWidth(r.width),o.setLineCap("butt");for(let t,a=0;a<e.length;a++)t=e[a],o.beginPath(),o.setStrokeStyle(t.color),o.arc(s.x,s.y,d,t._startAngle_*n,t._endAngle_*n,!1),o.stroke();o.save(),x=r.startAngle-r.endAngle+1;let c=x/r.splitLine.splitNumber,p=x/r.splitLine.splitNumber/r.splitLine.childNumber,g=-d-.5*r.width-r.splitLine.fixRadius,y=-d-.5*r.width-r.splitLine.fixRadius+r.splitLine.width,f=-d-.5*r.width-r.splitLine.fixRadius+r.splitLine.childWidth;o.translate(s.x,s.y),o.rotate((r.startAngle-1)*n);for(let e=0;e<r.splitLine.splitNumber+1;e++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(2*a.pixelRatio),o.moveTo(g,0),o.lineTo(y,0),o.stroke(),o.rotate(c*n);o.restore(),o.save(),o.translate(s.x,s.y),o.rotate((r.startAngle-1)*n);for(let e=0;e<r.splitLine.splitNumber*r.splitLine.childNumber+1;e++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(1*a.pixelRatio),o.moveTo(g,0),o.lineTo(f,0),o.stroke(),o.rotate(p*n);o.restore(),t=getGaugeDataPoints(t,e,r,l);for(let e,a=0;a<t.length;a++)e=t[a],o.save(),o.translate(s.x,s.y),o.rotate((e._proportion_-1)*n),o.beginPath(),o.setFillStyle(e.color),o.moveTo(r.pointer.width,0),o.lineTo(0,-r.pointer.width/2),o.lineTo(-h,0),o.lineTo(0,r.pointer.width/2),o.lineTo(r.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,r.pointer.width/6,0,2*n,!1),o.fill(),o.restore();!1!==a.dataLabel&&drawGaugeLabel(r,d,s,a,i,o)}return drawRingTitle(a,i,o,s),1===l&&"gauge"===a.type&&(a.extra.gauge.oldAngle=t[0]._proportion_,a.extra.gauge.oldData=t[0].data),{center:s,radius:d,innerRadius:h,categories:e,totalAngle:x}}function drawRadarDataPoints(e,t,a,o){var n=Math.cos,l=Math.sin,r=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,s=assign({},{gridColor:"#cccccc",labelColor:"#666666",opacity:.2},t.extra.radar),d=getRadarCoordinateSeries(t.categories.length),h={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2},x=Math.min(h.x-(getMaxTextListLength(t.categories)+a.radarLabelTextMargin),h.y-a.radarLabelTextMargin);x-=t.padding[1],o.beginPath(),o.setLineWidth(1*t.pixelRatio),o.setStrokeStyle(s.gridColor),d.forEach(function(e){var t=convertCoordinateOrigin(x*n(e),x*l(e),h);o.moveTo(h.x,h.y),o.lineTo(t.x,t.y)}),o.stroke(),o.closePath();for(var c=function(e){var i={};o.beginPath(),o.setLineWidth(1*t.pixelRatio),o.setStrokeStyle(s.gridColor),d.forEach(function(t,r){var s=convertCoordinateOrigin(x/a.radarGridCount*e*n(t),x/a.radarGridCount*e*l(t),h);0===r?(i=s,o.moveTo(s.x,s.y)):o.lineTo(s.x,s.y)}),o.lineTo(i.x,i.y),o.stroke(),o.closePath()},p=1;p<=a.radarGridCount;p++)c(p);var g=getRadarDataPoints(d,h,x,e,t,r);return g.forEach(function(e,i){if(o.beginPath(),o.setFillStyle(hexToRgb(e.color,s.opacity)),e.data.forEach(function(e,t){0===t?o.moveTo(e.position.x,e.position.y):o.lineTo(e.position.x,e.position.y)}),o.closePath(),o.fill(),!1!==t.dataPointShape){var n=a.dataPointShape[i%a.dataPointShape.length],l=e.data.map(function(e){return e.position});drawPointShape(l,e.color,n,o,t)}}),drawRadarLabel(d,x,h,t,a,o),{center:h,radius:x,angleList:d}}function normalInt(e,t,a){a=0==a?1:a;for(var o=[],n=0;n<a;n++)o[n]=Math.random();return Math.floor(o.reduce(function(e,t){return e+t})/a*(t-e))+e}function collisionNew(e,t,a,o){var n=!1;for(let l=0;l<t.length;l++)if(t[l].area)if(!(e[3]<t[l].area[1]||e[0]>t[l].area[2]||e[1]>t[l].area[3]||e[2]<t[l].area[0])){n=!0;break}else if(0>e[0]||0>e[1]||e[2]>a||e[3]>o){n=!0;break}else n=!1;return n}function getBoundingBox(e){var t,a={};a.xMin=180,a.xMax=0,a.yMin=90,a.yMax=0;for(var o,n=0;n<e.length;n++){o=e[n].geometry.coordinates;for(var l=0;l<o.length;l++){t=o[l],1==t.length&&(t=t[0]);for(var r=0;r<t.length;r++){var s=t[r][0],d=t[r][1],h={x:s,y:d};a.xMin=a.xMin<h.x?a.xMin:h.x,a.xMax=a.xMax>h.x?a.xMax:h.x,a.yMin=a.yMin<h.y?a.yMin:h.y,a.yMax=a.yMax>h.y?a.yMax:h.y}}}return a}function coordinateToPoint(e,t,i,a,o,n){return{x:(t-i.xMin)*a+o,y:(i.yMax-e)*a+n}}function pointToCoordinate(e,t,i,a,o,n){return{x:(t-o)/a+i.xMin,y:i.yMax-(e-n)/a}}function isRayIntersectsSegment(e,t,i){if(t[1]==i[1])return!1;if(t[1]>e[1]&&i[1]>e[1])return!1;if(t[1]<e[1]&&i[1]<e[1])return!1;if(t[1]==e[1]&&i[1]>e[1])return!1;if(i[1]==e[1]&&t[1]>e[1])return!1;if(t[0]<e[0]&&i[1]<e[1])return!1;let a=i[0]-(i[0]-t[0])*(i[1]-e[1])/(i[1]-t[1]);return!(a<e[0])}function isPoiWithinPoly(e,t){let i=0;for(let a,o=0;o<t.length;o++){a=t[o][0],1==t.length&&(a=t[o][0]);for(let t=0;t<a.length-1;t++){let o=a[t],n=a[t+1];isRayIntersectsSegment(e,o,n)&&(i+=1)}}return!(1!=i%2)}function drawMapDataPoints(e,t,a,o){var n,l,r=Math.abs,s=assign({},{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},t.extra.map),d=e,h=getBoundingBox(d),x=t.width/r(h.xMax-h.xMin),c=t.height/r(h.yMax-h.yMin),p=x<c?x:c,g=t.width/2-r(h.xMax-h.xMin)/2*p,y=t.height/2-r(h.yMax-h.yMin)/2*p;o.beginPath(),o.clearRect(0,0,t.width,t.height),o.setFillStyle(t.background||"#FFFFFF"),o.rect(0,0,t.width,t.height),o.fill();for(var f=0;f<d.length;f++){o.beginPath(),o.setLineWidth(s.borderWidth*t.pixelRatio),o.setStrokeStyle(s.borderColor),o.setFillStyle(hexToRgb(e[f].color,s.fillOpacity)),t.tooltip&&t.tooltip.index==f&&(o.setStrokeStyle(s.activeBorderColor),o.setFillStyle(hexToRgb(s.activeFillColor,s.activeFillOpacity)));for(var u=d[f].geometry.coordinates,m=0;m<u.length;m++){n=u[m],1==n.length&&(n=n[0]);for(var S=0;S<n.length;S++)l=coordinateToPoint(n[S][1],n[S][0],h,p,g,y),0==S?(o.beginPath(),o.moveTo(l.x,l.y)):o.lineTo(l.x,l.y);o.fill(),!0==s.border&&o.stroke()}if(!0==t.dataLabel){var T=d[f].properties.centroid;if(T){l=coordinateToPoint(T[1],T[0],h,p,g,y);let e=d[f].textSize||a.fontSize,t=d[f].properties.name;o.beginPath(),o.setFontSize(e),o.setFillStyle(d[f].textColor||"#666666"),o.fillText(t,l.x-measureText(t,e)/2,l.y+e/2),o.closePath(),o.stroke()}}}t.chartData.mapData={bounds:h,scale:p,xoffset:g,yoffset:y},drawToolTipBridge(t,a,o,1),o.draw()}function getWordCloudPoint(e,t){let a=e.series.sort(function(e,t){return parseInt(t.textSize)-parseInt(e.textSize)});switch(t){case"normal":for(let t=0;t<a.length;t++){let i,o,n,l=a[t].name,r=a[t].textSize,s=measureText(l,r),d=0;for(;;){d++,i=normalInt(-e.width/2,e.width/2,5)-s/2,o=normalInt(-e.height/2,e.height/2,5)+r/2,n=[i-5+e.width/2,o-5-r+e.height/2,i+s+5+e.width/2,o+5+e.height/2];let t=collisionNew(n,a,e.width,e.height);if(!t)break;if(1e3==d){n=[-100,-100,-100,-100];break}}a[t].area=n}break;case"vertical":function o(){return!!(.7<Math.random())};for(let t=0;t<a.length;t++){let i,n,l,r,s=a[t].name,d=a[t].textSize,h=measureText(s,d),x=o(),c=0;for(;;){c++;let t;if(x?(i=normalInt(-e.width/2,e.width/2,5)-h/2,n=normalInt(-e.height/2,e.height/2,5)+d/2,l=[n-5-h+e.width/2,-i-5+e.height/2,n+5+e.width/2,-i+d+5+e.height/2],r=[e.width-(e.width/2-e.height/2)-(-i+d+5+e.height/2)-5,e.height/2-e.width/2+(n-5-h+e.width/2)-5,e.width-(e.width/2-e.height/2)-(-i+d+5+e.height/2)+d,e.height/2-e.width/2+(n-5-h+e.width/2)+h+5],t=collisionNew(r,a,e.height,e.width)):(i=normalInt(-e.width/2,e.width/2,5)-h/2,n=normalInt(-e.height/2,e.height/2,5)+d/2,l=[i-5+e.width/2,n-5-d+e.height/2,i+h+5+e.width/2,n+5+e.height/2],t=collisionNew(l,a,e.width,e.height)),!t)break;if(1e3==c){l=[-1e3,-1e3,-1e3,-1e3];break}}x?(a[t].area=r,a[t].areav=l):a[t].area=l,a[t].rotate=x};}return a}function drawWordCloudDataPoints(e,t,i,a){let o=4<arguments.length&&arguments[4]!==void 0?arguments[4]:1,n=assign({},{type:"normal",autoColors:!0},t.extra.word);a.beginPath(),a.setFillStyle(t.background||"#FFFFFF"),a.rect(0,0,t.width,t.height),a.fill(),a.save();let l=t.chartData.wordCloudData;a.translate(t.width/2,t.height/2);for(let n=0;n<l.length;n++){a.save(),l[n].rotate&&a.rotate(90*Math.PI/180);let e=l[n].name,i=l[n].textSize,r=measureText(e,i);a.beginPath(),a.setStrokeStyle(l[n].color),a.setFillStyle(l[n].color),a.setFontSize(i),l[n].rotate?0<l[n].areav[0]&&(t.tooltip?t.tooltip.index==n?a.strokeText(e,(l[n].areav[0]+5-t.width/2)*o-r*(1-o)/2,(l[n].areav[1]+5+i-t.height/2)*o):a.fillText(e,(l[n].areav[0]+5-t.width/2)*o-r*(1-o)/2,(l[n].areav[1]+5+i-t.height/2)*o):a.fillText(e,(l[n].areav[0]+5-t.width/2)*o-r*(1-o)/2,(l[n].areav[1]+5+i-t.height/2)*o)):0<l[n].area[0]&&(t.tooltip?t.tooltip.index==n?a.strokeText(e,(l[n].area[0]+5-t.width/2)*o-r*(1-o)/2,(l[n].area[1]+5+i-t.height/2)*o):a.fillText(e,(l[n].area[0]+5-t.width/2)*o-r*(1-o)/2,(l[n].area[1]+5+i-t.height/2)*o):a.fillText(e,(l[n].area[0]+5-t.width/2)*o-r*(1-o)/2,(l[n].area[1]+5+i-t.height/2)*o)),a.stroke(),a.restore()}a.restore()}function drawFunnelDataPoints(e,t,i,a){let o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=assign({},{activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"},t.extra.funnel),l=(t.height-t.area[0]-t.area[2])/e.length,r={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.height-t.area[2]},s=n.activeWidth,d=Math.min((t.width-t.area[1]-t.area[3])/2-s,(t.height-t.area[0]-t.area[2])/2-s);e=getFunnelDataPoints(e,d,o),a.save(),a.translate(r.x,r.y);for(let o=0;o<e.length;o++)0==o?(t.tooltip&&t.tooltip.index==o&&(a.beginPath(),a.setFillStyle(hexToRgb(e[o].color,n.activeOpacity)),a.moveTo(-s,0),a.lineTo(-e[o].radius-s,-l),a.lineTo(e[o].radius+s,-l),a.lineTo(s,0),a.lineTo(-s,0),a.closePath(),a.fill()),e[o].funnelArea=[r.x-e[o].radius,r.y-l,r.x+e[o].radius,r.y],a.beginPath(),a.setLineWidth(n.borderWidth*t.pixelRatio),a.setStrokeStyle(n.borderColor),a.setFillStyle(hexToRgb(e[o].color,n.fillOpacity)),a.moveTo(0,0),a.lineTo(-e[o].radius,-l),a.lineTo(e[o].radius,-l),a.lineTo(0,0),a.closePath(),a.fill(),!0==n.border&&a.stroke()):(t.tooltip&&t.tooltip.index==o&&(a.beginPath(),a.setFillStyle(hexToRgb(e[o].color,n.activeOpacity)),a.moveTo(0,0),a.lineTo(-e[o-1].radius-s,0),a.lineTo(-e[o].radius-s,-l),a.lineTo(e[o].radius+s,-l),a.lineTo(e[o-1].radius+s,0),a.lineTo(0,0),a.closePath(),a.fill()),e[o].funnelArea=[r.x-e[o].radius,r.y-l*(o+1),r.x+e[o].radius,r.y-l*o],a.beginPath(),a.setLineWidth(n.borderWidth*t.pixelRatio),a.setStrokeStyle(n.borderColor),a.setFillStyle(hexToRgb(e[o].color,n.fillOpacity)),a.moveTo(0,0),a.lineTo(-e[o-1].radius,0),a.lineTo(-e[o].radius,-l),a.lineTo(e[o].radius,-l),a.lineTo(e[o-1].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),!0==n.border&&a.stroke()),a.translate(0,-l);return a.restore(),!1!==t.dataLabel&&1===o&&drawFunnelText(e,t,a,l,n.labelAlign,s,r),{center:r,radius:d,series:e}}function drawFunnelText(e,t,a,o,n,l,r){var s=Math.PI;for(let d=0;d<e.length;d++){let i,h,x,c,p=e[d],g=p.format?p.format(+p._proportion_.toFixed(2)):util.toFixed(100*p._proportion_)+"%";"right"==n?(i=0==d?(p.funnelArea[2]+r.x)/2:(p.funnelArea[2]+e[d-1].funnelArea[2])/2,h=i+2*l,x=p.funnelArea[1]+o/2,c=p.textSize||t.fontSize,a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(p.color),a.setFillStyle(p.color),a.beginPath(),a.moveTo(i,x),a.lineTo(h,x),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(h,x),a.arc(h,x,2,0,2*s),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(c),a.setFillStyle(p.textColor||"#666666"),a.fillText(g,h+5,x+c/2-2),a.closePath(),a.stroke(),a.closePath()):(i=0==d?(p.funnelArea[0]+r.x)/2:(p.funnelArea[0]+e[d-1].funnelArea[0])/2,h=i-2*l,x=p.funnelArea[1]+o/2,c=p.textSize||t.fontSize,a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(p.color),a.setFillStyle(p.color),a.beginPath(),a.moveTo(i,x),a.lineTo(h,x),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(h,x),a.arc(h,x,2,0,2*s),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(c),a.setFillStyle(p.textColor||"#666666"),a.fillText(g,h-5-measureText(g),x+c/2-2),a.closePath(),a.stroke(),a.closePath())}}function drawCanvas(e,t){t.draw()}var Timing={easeIn:function(e){return Math.pow(e,3)},easeOut:function(e){return Math.pow(e-1,3)+1},easeInOut:function(e){var t=Math.pow;return 1>(e/=.5)?.5*t(e,3):.5*(t(e-2,3)+2)},linear:function(e){return e}};function Animation(e){this.isStop=!1,e.duration="undefined"==typeof e.duration?1e3:e.duration,e.timing=e.timing||"linear";var t=function(){return"undefined"==typeof setTimeout?"undefined"==typeof requestAnimationFrame?function(e){e(null)}:requestAnimationFrame:function(e,t){setTimeout(function(){var t=+new Date;e(t)},t)}}(),i=null,a=function(o){if(null===o||!0===this.isStop)return e.onProcess&&e.onProcess(1),void(e.onAnimationFinish&&e.onAnimationFinish());if(null===i&&(i=o),o-i<e.duration){var n=(o-i)/e.duration,l=Timing[e.timing];n=l(n),e.onProcess&&e.onProcess(n),t(a,17)}else e.onProcess&&e.onProcess(1),e.onAnimationFinish&&e.onAnimationFinish()};a=a.bind(this),t(a,17)}Animation.prototype.stop=function(){this.isStop=!0};function drawCharts(e,t,a,i){var o=this,n=t.series,l=t.categories;n=fillSeries(n,t,a);var r=t.animation?t.duration:0;this.animationInstance&&this.animationInstance.stop();var s=null;if("candle"==e){let e=assign({},t.extra.candle.average);e.show?(s=calCandleMA(e.day,e.name,e.color,n[0].data),s=fillSeries(s,t,a),t.seriesMA=s):t.seriesMA?s=t.seriesMA=fillSeries(t.seriesMA,t,a):s=n}else s=n;t._series_=n=filterSeries(n),t.area=[,,,,];for(let o=0;4>o;o++)t.area[o]=t.padding[o];var d=calLegendData(s,t,a,t.chartData),h=d.area.wholeHeight,x=d.area.wholeWidth;switch(t.legend.position){case"top":t.area[0]+=h;break;case"bottom":t.area[2]+=h;break;case"left":t.area[3]+=x;break;case"right":t.area[1]+=x;}let c={},p=0;if("line"===t.type||"column"===t.type||"area"===t.type||"mix"===t.type||"candle"===t.type){if(c=calYAxisData(n,t,a),p=c.yAxisWidth,t.yAxis.showTitle){let e=0;for(let o=0;o<t.yAxis.data.length;o++)e=Math.max(e,t.yAxis.data[o].titleFontSize?t.yAxis.data[o].titleFontSize:a.fontSize);t.area[0]+=(e+6)*t.pixelRatio}let e=0,o=0;for(let a=0;a<p.length;a++)"left"==p[a].position?(t.area[3]+=0<o?p[a].width+t.yAxis.padding:p[a].width,o+=1):(t.area[1]+=0<e?p[a].width+t.yAxis.padding:p[a].width,e+=1)}else a.yAxisWidth=p;if(t.chartData.yAxisData=c,t.categories&&t.categories.length){t.chartData.xAxisData=getXAxisPoints(t.categories,t,a);let e=calCategoriesData(t.categories,t,a,t.chartData.xAxisData.eachSpacing),i=e.xAxisHeight,o=e.angle;a.xAxisHeight=i,a._xAxisTextAngle_=o,t.area[2]+=i,t.chartData.categoriesData=e}if(t.enableScroll&&"right"==t.xAxis.scrollAlign&&void 0===t._scrollDistance_){let e=0,i=t.chartData.xAxisData.xAxisPoints,a=t.chartData.xAxisData.startX,n=t.chartData.xAxisData.endX,l=t.chartData.xAxisData.eachSpacing,r=l*(i.length-1);e=n-a-r,o.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0},t._scrollDistance_=e}switch(("pie"===e||"ring"===e||"rose"===e)&&(a._pieTextMaxLength_=!1===t.dataLabel?0:getPieTextMaxLength(s)),e){case"word":let d=assign({},{type:"normal",autoColors:!0},t.extra.word);(!0==t.updateData||null==t.updateData)&&(t.chartData.wordCloudData=getWordCloudPoint(t,d.type)),this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),drawWordCloudDataPoints(n,t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"map":i.clearRect(0,0,t.width,t.height),drawMapDataPoints(n,t,a,i);break;case"funnel":this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),t.chartData.funnelData=drawFunnelDataPoints(n,t,a,i,e),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"line":this.animationInstance=new Animation({timing:"easeIn",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),drawYAxisGrid(l,t,a,i),drawXAxis(l,t,a,i);var o=drawLineDataPoints(n,t,a,i,e),r=o.xAxisPoints,s=o.calPoints,d=o.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=s,t.chartData.eachSpacing=d,drawYAxis(n,t,a,i),!1!==t.enableMarkLine&&1===e&&drawMarkLine(t,a,i),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e,d,r),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"mix":this.animationInstance=new Animation({timing:"easeIn",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),drawYAxisGrid(l,t,a,i),drawXAxis(l,t,a,i);var o=drawMixDataPoints(n,t,a,i,e),r=o.xAxisPoints,s=o.calPoints,d=o.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=s,t.chartData.eachSpacing=d,drawYAxis(n,t,a,i),!1!==t.enableMarkLine&&1===e&&drawMarkLine(t,a,i),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e,d,r),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"column":this.animationInstance=new Animation({timing:"easeIn",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),drawYAxisGrid(l,t,a,i),drawXAxis(l,t,a,i);var o=drawColumnDataPoints(n,t,a,i,e),r=o.xAxisPoints,s=o.calPoints,d=o.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=s,t.chartData.eachSpacing=d,drawYAxis(n,t,a,i),!1!==t.enableMarkLine&&1===e&&drawMarkLine(t,a,i),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e,d,r),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"area":this.animationInstance=new Animation({timing:"easeIn",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),drawYAxisGrid(l,t,a,i),drawXAxis(l,t,a,i);var o=drawAreaDataPoints(n,t,a,i,e),r=o.xAxisPoints,s=o.calPoints,d=o.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=s,t.chartData.eachSpacing=d,drawYAxis(n,t,a,i),!1!==t.enableMarkLine&&1===e&&drawMarkLine(t,a,i),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e,d,r),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"ring":case"pie":this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),t.chartData.pieData=drawPieDataPoints(n,t,a,i,e),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"rose":this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),t.chartData.pieData=drawRoseDataPoints(n,t,a,i,e),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"radar":this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),t.chartData.radarData=drawRadarDataPoints(n,t,a,i,e),drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),t.chartData.arcbarData=drawArcbarDataPoints(n,t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new Animation({timing:"easeInOut",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),t.chartData.gaugeData=drawGaugeDataPoints(l,n,t,a,i,e),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"candle":this.animationInstance=new Animation({timing:"easeIn",duration:r,onProcess:function(e){i.clearRect(0,0,t.width,t.height),t.rotate&&contextRotate(i,t),drawYAxisGrid(l,t,a,i),drawXAxis(l,t,a,i);var o=drawCandleDataPoints(n,s,t,a,i,e),r=o.xAxisPoints,d=o.calPoints,h=o.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=d,t.chartData.eachSpacing=h,drawYAxis(n,t,a,i),!1!==t.enableMarkLine&&1===e&&drawMarkLine(t,a,i),s?drawLegend(s,t,a,i,t.chartData):drawLegend(t.series,t,a,i,t.chartData),drawToolTipBridge(t,a,i,e,h,r),drawCanvas(t,i)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});}}function Event(){this.events={}}Event.prototype.addEventListener=function(e,t){this.events[e]=this.events[e]||[],this.events[e].push(t)},Event.prototype.trigger=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var a=t[0],o=t.slice(1);!this.events[a]||this.events[a].forEach(function(e){try{e.apply(null,o)}catch(t){console.error(t)}})};var Charts=function(e){e.pixelRatio=e.pixelRatio?e.pixelRatio:1,e.fontSize=e.fontSize?e.fontSize*e.pixelRatio:13*e.pixelRatio,e.title=assign({},e.title),e.subtitle=assign({},e.subtitle),e.duration=e.duration?e.duration:1e3,e.yAxis=assign({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,splitNumber:5,gridType:"solid",dashLength:4*e.pixelRatio,gridColor:"#cccccc",padding:10,fontColor:"#666666"},e.yAxis),e.yAxis.dashLength*=e.pixelRatio,e.yAxis.padding*=e.pixelRatio,e.xAxis=assign({},{rotateLabel:!1,type:"calibration",gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc"},e.xAxis),e.xAxis.dashLength*=e.pixelRatio,e.legend=assign({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:e.fontSize,lineHeight:e.fontSize,fontColor:"#333333",format:{},hiddenColor:"#CECECE"},e.legend),e.legend.borderWidth*=e.pixelRatio,e.legend.itemGap*=e.pixelRatio,e.legend.padding*=e.pixelRatio,e.legend.margin*=e.pixelRatio,e.extra=assign({},e.extra),e.rotate=!!e.rotate,e.animation=!!e.animation;let t=JSON.parse(JSON.stringify(config));if(t.colors=e.colors?e.colors:t.colors,t.yAxisTitleWidth=!0!==e.yAxis.disabled&&e.yAxis.title?t.yAxisTitleWidth:0,("pie"==e.type||"ring"==e.type)&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.pie.labelWidth*e.pixelRatio||t.pieChartLinePadding*e.pixelRatio),"rose"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.rose.labelWidth*e.pixelRatio||t.pieChartLinePadding*e.pixelRatio),t.pieChartTextPadding=!1===e.dataLabel?0:t.pieChartTextPadding*e.pixelRatio,t.yAxisSplit=e.yAxis.splitNumber?e.yAxis.splitNumber:config.yAxisSplit,t.rotate=e.rotate,e.rotate){let t=e.width,i=e.height;e.width=i,e.height=t}e.padding=e.padding?e.padding:t.padding;for(let t=0;4>t;t++)e.padding[t]*=e.pixelRatio;t.yAxisWidth=config.yAxisWidth*e.pixelRatio,t.xAxisHeight=config.xAxisHeight*e.pixelRatio,e.enableScroll&&e.xAxis.scrollShow&&(t.xAxisHeight+=6*e.pixelRatio),t.xAxisLineHeight=config.xAxisLineHeight*e.pixelRatio,t.fontSize=e.fontSize,t.titleFontSize=config.titleFontSize*e.pixelRatio,t.subtitleFontSize=config.subtitleFontSize*e.pixelRatio,t.toolTipPadding=config.toolTipPadding*e.pixelRatio,t.toolTipLineHeight=config.toolTipLineHeight*e.pixelRatio,t.columePadding=config.columePadding*e.pixelRatio,e.$this=e.$this?e.$this:this,this.context=uni.createCanvasContext(e.canvasId,e.$this),e.chartData={},this.event=new Event,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=e,this.config=t,drawCharts.call(this,e.type,e,t,this.context)};Charts.prototype.updateData=function(){let e=0<arguments.length&&arguments[0]!==void 0?arguments[0]:{};this.opts=assign({},this.opts,e),this.opts.updateData=!0;let t=e.scrollPosition||"current";switch(t){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":let e=calYAxisData(this.opts.series,this.opts,this.config),i=e.yAxisWidth;this.config.yAxisWidth=i;let a=0,o=getXAxisPoints(this.opts.categories,this.opts,this.config),n=o.xAxisPoints,l=o.startX,r=o.endX,s=o.eachSpacing,d=s*(n.length-1);a=r-l-d,this.scrollOption={currentOffset:a,startTouchX:a,distance:0,lastMoveTime:0},this.opts._scrollDistance_=a;}drawCharts.call(this,this.opts.type,this.opts,this.config,this.context)},Charts.prototype.zoom=function(){var e=Math.round,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0!==this.opts.enableScroll)return void console.log("\u8BF7\u542F\u7528\u6EDA\u52A8\u6761\u540E\u4F7F\u7528\uFF01");let i=e(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+e(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;let a=calYAxisData(this.opts.series,this.opts,this.config),o=a.yAxisWidth;this.config.yAxisWidth=o;let n=0,l=getXAxisPoints(this.opts.categories,this.opts,this.config),r=l.xAxisPoints,s=l.startX,d=l.endX,h=l.eachSpacing,x=d-s,c=x-h*(r.length-1);n=x/2-h*i,0<n&&(n=0),n<c&&(n=c),this.scrollOption={currentOffset:n,startTouchX:n,distance:0,lastMoveTime:0},this.opts._scrollDistance_=n,drawCharts.call(this,this.opts.type,this.opts,this.config,this.context)},Charts.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},Charts.prototype.addEventListener=function(e,t){this.event.addEventListener(e,t)},Charts.prototype.getCurrentDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){let i=getTouches(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type?findPieChartCurrentIndex({x:i.x,y:i.y},this.opts.chartData.pieData):"radar"===this.opts.type?findRadarChartCurrentIndex({x:i.x,y:i.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?findFunnelChartCurrentIndex({x:i.x,y:i.y},this.opts.chartData.funnelData):"map"===this.opts.type?findMapChartCurrentIndex({x:i.x,y:i.y},this.opts):"word"===this.opts.type?findWordChartCurrentIndex({x:i.x,y:i.y},this.opts.chartData.wordCloudData):findCurrentIndex({x:i.x,y:i.y},this.opts.chartData.xAxisPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},Charts.prototype.getLegendDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){let i=getTouches(e,this.opts,t);return findLegendIndex({x:i.x,y:i.y},this.opts.chartData.legendData)}return-1},Charts.prototype.touchLegend=function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=null;if(i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],i){var a=getTouches(i,this.opts,t),o=this.getLegendDataIndex(t);0<=o&&(this.opts.series[o].show=!this.opts.series[o].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,drawCharts.call(this,this.opts.type,this.opts,this.config,this.context))}},Charts.prototype.showToolTip=function(t){var e=1<arguments.length&&arguments[1]!==void 0?arguments[1]:{},i=null;i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],i||console.log("touchError");var a=getTouches(i,this.opts,t),o=this.scrollOption.currentOffset,n=assign({},this.opts,{_scrollDistance_:o,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type){var l=this.getCurrentDataIndex(t);if(-1<l){var r=getSeriesDataItem(this.opts.series,l);if(0!==r.length){var s=getToolTipData(r,this.opts.chartData.calPoints,l,this.opts.categories,e),d=s.textList,h=s.offset;h.y=a.y,n.tooltip={textList:d,offset:h,option:e,index:l}}}drawCharts.call(this,n.type,n,this.config,this.context)}if("mix"===this.opts.type){var l=this.getCurrentDataIndex(t);if(-1<l){var o=this.scrollOption.currentOffset,n=assign({},this.opts,{_scrollDistance_:o,animation:!1}),r=getSeriesDataItem(this.opts.series,l);if(0!==r.length){var x=getMixToolTipData(r,this.opts.chartData.calPoints,l,this.opts.categories,e),d=x.textList,h=x.offset;h.y=a.y,n.tooltip={textList:d,offset:h,option:e,index:l}}}drawCharts.call(this,n.type,n,this.config,this.context)}if("candle"===this.opts.type){var l=this.getCurrentDataIndex(t);if(-1<l){var o=this.scrollOption.currentOffset,n=assign({},this.opts,{_scrollDistance_:o,animation:!1}),r=getSeriesDataItem(this.opts.series,l);if(0!==r.length){var s=getCandleToolTipData(this.opts.series[0].data,r,this.opts.chartData.calPoints,l,this.opts.categories,this.opts.extra.candle,e),d=s.textList,h=s.offset;h.y=a.y,n.tooltip={textList:d,offset:h,option:e,index:l}}}drawCharts.call(this,n.type,n,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){var l=this.getCurrentDataIndex(t);if(-1<l){var o=this.scrollOption.currentOffset,n=assign({},this.opts,{_scrollDistance_:o,animation:!1}),r=this.opts._series_[l],d=[{text:e.format?e.format(r):r.name+": "+r.data,color:r.color}],h={x:a.x,y:a.y};n.tooltip={textList:d,offset:h,option:e,index:l}}drawCharts.call(this,n.type,n,this.config,this.context)}if("map"===this.opts.type||"word"===this.opts.type){var l=this.getCurrentDataIndex(t);if(-1<l){var o=this.scrollOption.currentOffset,n=assign({},this.opts,{_scrollDistance_:o,animation:!1}),r=this.opts._series_[l],d=[{text:e.format?e.format(r):r.properties.name,color:r.color}],h={x:a.x,y:a.y};n.tooltip={textList:d,offset:h,option:e,index:l}}n.updateData=!1,drawCharts.call(this,n.type,n,this.config,this.context)}if("radar"===this.opts.type){var l=this.getCurrentDataIndex(t);if(-1<l){var o=this.scrollOption.currentOffset,n=assign({},this.opts,{_scrollDistance_:o,animation:!1}),r=getSeriesDataItem(this.opts.series,l);if(0!==r.length){var d=r.map(function(t){return{text:e.format?e.format(t):t.name+": "+t.data,color:t.color}}),h={x:a.x,y:a.y};n.tooltip={textList:d,offset:h,option:e,index:l}}}drawCharts.call(this,n.type,n,this.config,this.context)}},Charts.prototype.translate=function(e){this.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0};let t=assign({},this.opts,{_scrollDistance_:e,animation:!1});drawCharts.call(this,this.opts.type,t,this.config,this.context)},Charts.prototype.scrollStart=function(t){var e=null;e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0];var i=getTouches(e,this.opts,t);e&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=i.x)},Charts.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());let e=this.opts.extra.touchMoveLimit||20,i=Date.now(),a=i-this.scrollOption.lastMoveTime;if(!(a<Math.floor(1e3/e))){this.scrollOption.lastMoveTime=i;var o=null;if(o=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],o&&!0===this.opts.enableScroll){var n,l=getTouches(o,this.opts,t);n=l.x-this.scrollOption.startTouchX;var r=this.scrollOption.currentOffset,s=calValidDistance(this,r+n,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=n=s-r;var d=assign({},this.opts,{_scrollDistance_:r+n,animation:!1});return drawCharts.call(this,d.type,d,this.config,this.context),r+n}}},Charts.prototype.scrollEnd=function(){if(!0===this.opts.enableScroll){var e=this.scrollOption,t=e.currentOffset,i=e.distance;this.scrollOption.currentOffset=t+i,this.scrollOption.distance=0}},"object"==typeof module&&"object"==typeof module.exports&&(module.exports=Charts);