import Vue from 'vue'
import App from './App'
import {
	request,
	syncRequest
} from './js/request.js'
import {
	showToast,
	showSuccess,
	showError,
	clearStore
} from './js/uniapp.js'

import audio from './js/audio/index.js'
import store from './store/index.js'

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
	store,
	...App
})


const currentVersion = {
	// id: 24
	id: 32
}

Vue.prototype.$current = currentVersion
Vue.prototype.request = request
Vue.prototype.syncRequest = syncRequest
Vue.prototype.$store = store
Vue.prototype.$success = showSuccess
Vue.prototype.$error = showError
Vue.prototype.$toast = showToast
Vue.prototype.$audio = audio
Vue.prototype.$clearStore = clearStore
app.$mount()
