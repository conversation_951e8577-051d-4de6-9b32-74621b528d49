// 1
import Vue from 'vue'
import Vuex from 'vuex'
// 2
import {
	material,
	wo,
	line,
	department,
	storage,
	returnMaterial,
	radios,
	repairUser,
	superWO,
	createWO,
	createWOMM,
	spareChecks,
	returnOrder
} from './modules'
// 3
// import getters from './getters'
// 4
Vue.use(Vuex)
// 5

const store = new Vuex.Store({
	modules: {
		material,
		wo,
		line,
		department,
		storage,
		returnMaterial,
		radios,
		repairUser,
		superWO,
		createWO,
		createWOMM,
		spareChecks,
		returnOrder
	}
})

export default store
