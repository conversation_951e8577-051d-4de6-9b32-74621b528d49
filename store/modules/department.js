// 车间模块
const department = {

	namespaced: true,

	state: {
		department: {
			departmentId: null,
			departmentCode: '',
			departmentName: ''
		}
	},
	mutations: {
		setDepartment(state, values) {
			state.department.departmentId = values.departmentId;
			state.department.departmentCode = values.departmentCode;
			state.department.departmentName = values.departmentName;
		},
		empty(state){
			state.department.departmentId = null;
			state.department.departmentCode = '';
			state.department.departmentName = '';
		}
	}

}

export default department
