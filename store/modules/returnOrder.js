//
const returnOrder = {
	namespaced: true,
	state: {
		returnOrder: []
	},
	mutations: {
		setReturnOrder(state, values) {
			state.returnOrder = values
		},
		empty(state) {
			state.returnOrder = [];
		},
		pushReturnOrder(state, value) {
			state.returnOrder.push(value)
		},
		remove(state, value) {
			let index = state.returnOrder.findIndex(t => t.orderId == value.orderId)
			if (index > -1) {
				state.returnOrder.splice(index, 1)
			}
		}
	},
	actions: {
		setReturnOrder(state, values) {
			state.returnOrder = values
		},
		pushReturnOrder(state, value) {
			state.returnOrder.push(value)
		}
	}
}
export default returnOrder