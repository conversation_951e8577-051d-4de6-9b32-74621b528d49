// 仓库模块
const storage = {

	namespaced: true,

	state: {
		storage: {
			storageId: null,
			storageCode: '',
			storageName: ''
		}
	},
	mutations: {
		setStorage(state, values) {
			state.storage.storageId = values.storageId;
			state.storage.storageCode = values.storageCode;
			state.storage.storageName = values.storageName;
			console.log(state.storage);
		},
		empty(state){
			state.storage.storageId = null;
			state.storage.storageCode = '';
			state.storage.storageName = '';
		}
	}

}

export default storage
