// 手工工单创建
const createWO = {

	namespaced: true,

	state: {
		createWO: {
			sourceWO: '',
			materialId: null,
			materialCode: '',
			materialName: '',
			specification: '',
			planQty: null,
			lineId: null,
			departmentId: null,
		}
	},
	mutations: {
		setPlanQty(state, values) {
			state.createWO.planQty = values.planQty;
		},
		setCreateWO(state, values) {
			state.createWO.sourceWO = values.sourceWO;
			state.createWO.materialId = values.materialId;
			state.createWO.materialCode = values.materialCode;
			state.createWO.materialName = values.materialName;
			state.createWO.specification = values.specification;
			state.createWO.planQty = values.planQty;
			state.createWO.lineId = values.lineId;
			state.createWO.departmentId = values.departmentId;
		},
		empty(state) {
			state.createWO.sourceWO = ''
			state.createWO.materialId = null
			state.createWO.materialCode = ''
			state.createWO.materialName = ''
			state.createWO.specification = ''
			state.createWO.planQty = null
			state.createWO.lineId = null
			state.createWO.departmentId = null
		}
	}

}

export default createWO
