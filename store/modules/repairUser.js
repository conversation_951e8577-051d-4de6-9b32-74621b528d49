const repairUser = {

	namespaced: true,

	state: {
		repairUser: {
			repairUserId: null,
			repairUserCode: '',
			repairUserName: ''
		}
	},
	mutations: {
		setRepairUser(state, values) {
			state.repairUser.repairUserId = values.repairUserId;
			state.repairUser.repairUserCode = values.repairUserCode;
			state.repairUser.repairUserName = values.repairUserName;
		},
		empty(state){
			state.repairUser.repairUserId = null;
			state.repairUser.repairUserCode = '';
			state.repairUser.repairUserName = '';
		}
	}

}

export default repairUser