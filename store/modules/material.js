// 物料模块
const material = {

	namespaced: true,

	state: {
		material: {
			materialId: null,
			materialCode: '',
			materialName: '',
			specification: ''
		}
	},
	mutations: {
		setMaterial(state, values) {
			state.material.materialId = values.materialId;
			state.material.materialCode = values.materialCode;
			state.material.materialName = values.materialName;
			state.material.specification = values.specification;
		},
		empty(state){
			state.material.materialId = null;
			state.material.materialCode = '';
			state.material.materialName = '';
			state.material.specification = '';
		}
	}

}

export default material
