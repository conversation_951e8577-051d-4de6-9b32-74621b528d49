// 物料模块
const superWO = {

	namespaced: true,

	state: {
		wo: {
			Id: null,
			WO: '',
			WOStatus: null,
			RedoMaterialCode: '',
			Status: null,
			RedoType: null
		}
	},
	mutations: {
		setWO(state, values){
			state.wo.Id = values.Id;
			state.wo.WO = values.WO;
			state.wo.WOStatus = values.WOStatus;
			state.wo.RedoMaterialCode = values.RedoMaterialCode;
			state.wo.Status = values.Status;
			state.wo.RedoType = values.RedoType;
		},
		empty(state){
			state.wo = {
				Id: null,
				WO: '',
				WOStatus: null,
				RedoMaterialCode: '',
				Status: null,
				RedoType: null
			}
		}
	}

}

export default superWO
