// 物料模块
const wo = {

	namespaced: true,

	state: {
		wo: {
			wo: '',
			woName: '',
			Id: null,
			Specification: '',
			MaterialId: '',
			MaterialCode: '',
			MaterialName: '',
			ShipId: null
		}
	},
	mutations: {
		setWO(state, values){
			state.wo.wo = values.wo;
			state.wo.woName = values.woName;
			state.wo.Id = values.Id;
			state.wo.Specification = values.Specification;
			state.wo.MaterialId = values.MaterialId;
			state.wo.MaterialCode = values.MaterialCode;
			state.wo.MaterialName = values.MaterialName;
			state.wo.ShipId = values.ShipId;
		},
		empty(state){
			state.wo = {
				wo: '',
				woName: '',
				Id: null,
				Specification: '',
				MaterialId: '',
				MaterialCode: '',
				MaterialName: '',
				ShipId: null
			}
		}
	}

}

export default wo
