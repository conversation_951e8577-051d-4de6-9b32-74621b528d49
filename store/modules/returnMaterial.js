// 退料对象
const returnMaterial = {
	namespaced: true,
	state: {
		returnMaterial: {}
	},
	mutations: {
		setReturnMaterial(state, values) {
			let arr = Object.keys(state.returnMaterial);
			if( arr.length == 0){
				state.returnMaterial = values
			} else {
				state.returnMaterial.materials = state.returnMaterial.materials.concat(values.materials)
			}
			
		},
		empty(state) {
			state.returnMaterial = {};
		},
		removeReturnMaterialElement(state, values){
			if(typeof(state.returnMaterial) == 'undefined'){
				console.log('空对象无法移除');
			}else{
				const materials = state.returnMaterial.materials;
				if(typeof materials != 'undefined'){
					for(let i = 0; i < materials.length; i++){
						if(materials[i].WorkOrderCode == values.WO){
							state.returnMaterial.materials.splice(i, 1);
						}
					}
				}
			}
		},
	},
	actions: {
		removeElement({state, commit}, values){
			commit('removeReturnMaterialElement', values)
		},
		promiseSetReturnMaterial({state, commit}, values){
			commit('setReturnMaterial', values)
		}
	}
}
export default returnMaterial
