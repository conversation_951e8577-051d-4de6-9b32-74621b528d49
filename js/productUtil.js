/* 数组去重 */
export function uniqueS(arr) {
  return Array.from(new Set(arr))
}
/* 公共方法 */
export function isNull(res) {
	if (res === null) {
		uni.showToast({
			title: '未查询到数据',
			icon: 'error',
			duration: 1000
		})
		return true;
	} else {
		return false;
	}
}
// 获取当前时间
export function getTime(){
	var date = new Date(),
	year = date.getFullYear(),
	month = date.getMonth() + 1,
	day = date.getDate(),
	hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours(),
	minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(),
	second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
	month >= 1 && month <= 9 ? (month = "0" + month) : "";
	day >= 0 && day <= 9 ? (day = "0" + day) : "";
	var timer = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
	return timer;
}

// 深拷贝
export const deepCopy = function(source) {
	let _source = JSON.stringify(source)
	return JSON.parse(_source)
}

export const deepCopyPromise = function(source) {
	let _source = JSON.stringify(source)
	return new Promise(resolve => {
		resolve(JSON.parse(_source))
	}) 
}

/* 半成品杂发、杂收 */
// 判断是否重复
export function isRepeat(carrierId, carrierMatinfo) {
	if (carrierMatinfo.length === 0) {
		return true
	}
	for (let i = 0; i < carrierMatinfo.length; i++) {
		for (let j = 0; j < carrierMatinfo[i].Details.length; j++) {
			if (carrierId === carrierMatinfo[i].Details[j].CarrierId) {
				return false
			}
		}
	}
	return true
}

// 根据 料号、名称、规格、仓库 将数据划分
export function dataDivision(carrierMatinfo, newCarrier, type) {	
	let newCarrierMatinfo = deepCopy(carrierMatinfo);
	if (carrierMatinfo.length === 0) {
		newCarrierMatinfo = zfPushData(newCarrierMatinfo, newCarrier);
		return new Promise((resolve, reject) => {
			resolve(newCarrierMatinfo)
		})
	}
	for (let i = 0; i < carrierMatinfo.length; i++) {
		if (
			carrierMatinfo[i].MaterialCode == newCarrier.MaterialCode &&
			carrierMatinfo[i].MaterialName == newCarrier.MaterialName &&
			carrierMatinfo[i].Specification == newCarrier.Specification &&
			carrierMatinfo[i].StorageId == newCarrier.StorageId &&
			carrierMatinfo[i].OtherIssueCode == newCarrier.OtherIssueCode &&
			carrierMatinfo[i].BenefitOrgId == newCarrier.BenefitOrgId &&
			carrierMatinfo[i].OrderType == newCarrier.OrderType &&
			carrierMatinfo[i].DepartmentId == newCarrier.DepartmentId &&
			carrierMatinfo[i].StorageId == newCarrier.StorageId &&
			carrierMatinfo[i].Item == newCarrier.Item &&
			carrierMatinfo[i].Type == newCarrier.Type
		) {
			let temp = {
				// 为展示获取
				CarrierCode: newCarrier.CarrierCode,
				// 必要字段
				CarrierId: newCarrier.CarrierId,
				WOId: newCarrier.WOId,
				MaterialId: newCarrier.MaterialId,
				LotNo: newCarrier.LotNo,
				SupplierId: newCarrier.SupplierId,
				Qty: newCarrier.Qty,
				Reason: newCarrier.Reason,
				OrgId: newCarrier.OrgId,
				UnitId: newCarrier.UnitId
			}
			newCarrierMatinfo[i].Details.push(temp);
		} else {
			newCarrierMatinfo = zfPushData(newCarrierMatinfo, newCarrier, type)
		}
	}
	return new Promise((resolve, reject) => {
		resolve(newCarrierMatinfo)
	})
}

function zfPushData(newCarrierMatinfo, newCarrier, type) {
	let temp = {
		// 展示字段
		CarrierCode: newCarrier.CarrierCode,
		MaterialCode: newCarrier.MaterialCode,
		MaterialName: newCarrier.MaterialName,
		Specification: newCarrier.Specification,
		StorageName: newCarrier.StorageName,
		DepartmentName: newCarrier.DepartmentName,
		// 纸质单号
		OtherIssueCode: newCarrier.OtherIssueCode,
		// 收益组织 ID
		BenefitOrgId: newCarrier.BenefitOrgId,
		// U9 单据类型
		OrderType: newCarrier.OrderType,
		// 部门 ID
		DepartmentId: newCarrier.DepartmentId,
		// 仓库 ID
		StorageId: newCarrier.StorageId,
		// 收益项目
		Item: newCarrier.Item,
		// 备注
		Remark: newCarrier.Remark,
		// 杂发 1 杂收 2
		Type: newCarrier.Type,
		// 登录组织 ID
		OrgId: newCarrier.OrgId,
		Details: [{
			// 为展示获取
			CarrierCode: newCarrier.CarrierCode,
			// 必要字段
			CarrierId: newCarrier.CarrierId,
			WOId: newCarrier.WOId,
			MaterialId: newCarrier.MaterialId,
			MaterialCode:newCarrier.MaterialCode,
			MaterialName:newCarrier.MaterialName,
			Specification:newCarrier.Specification,
			LotNo: newCarrier.LotNo,
			SupplierId: newCarrier.SupplierId,
			Qty: newCarrier.Qty,
			Reason: newCarrier.Remark,
			OrgId: newCarrier.OrgId,
			UnitId: newCarrier.UnitId
		}]
	}
	newCarrierMatinfo.push(temp);
	return newCarrierMatinfo;
}

// 半成品调拨
export function dbPushData(newCarrierMatinfo, newCarrier) {
	this.carrierMatinfo = {
		WOId: res.response.WOId,
		OutStorgeId: res.response.StorageId,
		InStorgeId: this.storageId,
		Remark: this.Remark,
		OrgId: this.OrgId,
		Details: []
	}
	let temp = {
		CarrierId: res.response.CarrierId,
		CarrierCode: res.response.CarrierCode,
		MaterialId: res.response.MaterialId,
		AllotQty: res.response.Qty,
		OrgId: this.OrgId
	};
}
