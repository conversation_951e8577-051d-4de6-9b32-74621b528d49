
import permision from "@/common/js/permission.js"
var   readResult = '';




export default {
	
	getScanData: function(){
			return readResult
		},
	async scanCode(paramter) {
		let status = await this.checkPermission();
		if (status !== 1) {
		    return;
		}
		var that = this
		uni.scanCode({
			success: (res) => {

				return  typeof paramter.success == 'function' && paramter.success(res.result);
			},
			fail: (err) => {
				
				uni.getSetting({
					success: (res) => {
						let authStatus = res.authSetting['scope.camera'];
						if (!authStatus) {
							uni.showModal({
								title: '授权失败',
								content: 'Hello uni-app需要使用您的相机，请在设置界面打开相关权限',
								success: (res) => {
									if (res.confirm) {
										uni.openSetting()
									}
								}
							})
						}
					}
				})
			
			}
		});
	}
	,
	async checkPermission(code) {
		let status = permision.isIOS ? await permision.requestIOS('camera') :
			await permision.requestAndroid('android.permission.CAMERA');
	
		if (status === null || status === 1) {
			status = 1;
		} else {
			uni.showModal({
				content: "需要相机权限",
				confirmText: "设置",
				success: function(res) {
					if (res.confirm) {
						permision.gotoAppSetting();
					}
				}
			})
		}
		return status;
	}
	
}