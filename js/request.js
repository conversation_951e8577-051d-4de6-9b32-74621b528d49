
/* 同步请求 */
export const request = (paramter) => {
	let path = uni.getStorageSync('URL') == '' ? 'http://192.168.1.155:8091/api' : uni.getStorageSync('URL')
	
	//let path = uni.getStorageSync('URL') == '' ? 'http://192.168.1.231:9291/api' : uni.getStorageSync('URL')

	if (!paramter.url) {
		throw new Error("url not null.");
	}
	let tokenstr = uni.getStorageSync("token");
	if (tokenstr == null || tokenstr == 'undefind') {
		return
	}
	paramter.url = path + paramter.url;

	uni.showLoading({
		title: "加载中...",
		mask: true
	});

	uni.request({
		url: paramter.url,
		header: paramter.header || {
			'Authorization': 'Bearer ' + tokenstr,
			'content-type': 'application/json',
			'Access-Control-Allow-Origin': '*'
		},
		dataType: "json",
		method: paramter.method || "POST",
		data: paramter.data || {},
		timeout:30000,  //180000
		fail:res=> {
			uni.hideLoading();
			return uni.showToast({
				icon: "loading",
				title: res.errMsg
			});
		},
		success: res => {
			uni.hideLoading();
			if (res.statusCode == 401) {
				uni.showModal({
					title: '提示',
					content: res.data.msg,
					confirmColor: '#ee6666', //确定字体颜色
					showCancel: false, //没有取消按钮的弹框
					buttonText: '确定',
					success: function(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: '../../../user/login'
							})
						}
					}
				});
				return
			} else if ((res.data.status === 200 || res.statusCode === 200) && res.data.success ===
				true) {
				return typeof paramter.success == 'function' && paramter.success(res.data);
			} else if (res.data.success === false) {
				return typeof paramter.error == 'function' && paramter.error(res.data);
			} else if (res.success === false) {
				return typeof paramter.error == 'function' && paramter.error(res);
			} else if (res.response.success === false) {
				return typeof paramter.error == 'function' && paramter.error(res.response);
			} else {
				uni.showToast({
					title: '请求失败',
					icon: 'error'
				});
			}
		}
	});
}

/* 同步请求 */
export const syncRequest = (paramter) => {
	let path = uni.getStorageSync('URL') == '' ? 'http://192.168.1.155:8091/api' : uni.getStorageSync('URL')
	//let path = uni.getStorageSync('URL') == '' ? 'http://192.168.1.231:9291/api' : uni.getStorageSync('URL')

	return new Promise(((resolve, reject) => {
		if (!paramter.url) {
			throw new Error("url not null.");
		}

		let tokenstr = uni.getStorageSync("token");
		if (tokenstr == null || tokenstr == 'undefind') {
			return
		}
		paramter.url = path + paramter.url;

		uni.showLoading({
			title: "加载中...",
			mask: true
		});

		uni.request({
			url: paramter.url,
			header: paramter.header || {
				'Authorization': 'Bearer ' + tokenstr,
				'content-type': 'application/json',
				'Access-Control-Allow-Origin': '*'
			},
			dataType: "json",
			method: paramter.method || "POST",
			data: paramter.data || {},
			fail() {
				uni.hideLoading();
				return uni.showToast({
					icon: "loading",
					title: "网络请求异常"
				});
			},
			success: res => {
				uni.hideLoading();
				// 登录失效
				if (res.statusCode == 401) {
					uni.showModal({
						title: '提示',
						content: res.data.msg,
						confirmColor: '#ee6666', //确定字体颜色
						showCancel: false, //没有取消按钮的弹框
						buttonText: '确定',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '../../../user/login'
								})
							}
						}
					});
					return
				} else if ((res.data.status === 200 || res.statusCode === 200) && res.data
					.success === true) {
					resolve(res.data);
					return typeof paramter.success == 'function' && paramter.success(res
						.data);
				} else if (res.data.success === false) {
					resolve(res.data);
					return typeof paramter.error == 'function' && paramter.error(res.data);
				} else if (res.success === false) {
					resolve(res);
					return typeof paramter.error == 'function' && paramter.error(res);
				} else {
					uni.showToast({
						title: '请求失败',
						icon: 'error'
					});
					resolve(res.data);
					return typeof paramter.error == 'function' && paramter.error(res.data);
				}
			},
			error: res => {
				console.log("请求结果：" + res);
			}
		});
	}))
}
