// import store from '../store'
// import * as util from './util'
// import { paginate } from '@/common/constant'

/**
 * 获取当前运行的终端(App H5 小程序)
 * https://uniapp.dcloud.io/platform
 */
export const getPlatform = () => {
	// #ifdef APP-PLUS
	const platform = 'App'
	// #endif
	// #ifdef APP-PLUS-NVUE
	const platform = 'App'
	// #endif
	// #ifdef H5
	const platform = 'H5'
	// #endif
	// #ifdef MP-WEIXIN
	const platform = 'MP-WEIXIN'
	// #endif
	// #ifdef MP-ALIPAY
	const platform = 'MP-ALIPAY'
	// #endif
	// #ifdef MP-BAIDU
	const platform = 'MP-BAIDU'
	// #endif
	// #ifdef MP-TOUTIAO
	const platform = 'MP-TOUTIAO'
	// #endif
	// #ifdef MP-QQ
	const platform = 'MP-QQ'
	// #endif
	// #ifdef MP-360
	const platform = 'MP-360'
	// #endif
	return platform
}

/**
 * 显示成功提示框
 */
export const showSuccess = (msg, callback) => {
	uni.showToast({
		title: msg,
		icon: 'success',
		mask: true,
		duration: 1500,
		success() {
			callback && callback()
		}
	})
}

/**
 * 显示失败提示框
 */
export const showError = (msg, callback) => {
	uni.showModal({
		title: '提示',
		content: msg,
		showCancel: false,
		confirmColor: '#ee6666',
		success(res) {
			callback && callback()
		}
	})
}

/**
 * 显示纯文字提示框
 */
export const showToast = msg => {
	uni.showToast({
		title: msg,
		icon: 'none'
	})
}

/**
 * 验证是否已登录
 */
export const checkLogin = () => {
	// return !!store.getters.userId
}

/**
 * 加载更多列表数据
 * @param {Object} resList 新列表数据
 * @param {Object} oldList 旧列表数据
 * @param {int} pageNo 当前页码
 */
export const getEmptyPaginateObj = () => {
	// return util.cloneObj(paginate)
}

/**
 * 加载更多列表数据
 * @param {Object} resList 新列表数据
 * @param {Object} oldList 旧列表数据
 * @param {int} pageNo 当前页码
 */
export const getMoreListData = (resList, oldList, pageNo) => {
	// 如果是第一页需手动制空列表
	// if (pageNo == 1) oldList.data = []
	// 合并新数据
	// return oldList.data.concat(resList.data)
}

/**
 * 清除所有的 store
 */
export const clearStore = (app) => {
	app.$store.commit('returnOrder/empty')
	app.$store.commit('createWOMM/empty')
	app.$store.commit('createWO/empty')
	app.$store.commit('wo/empty')
	app.$store.commit('department/empty')
	app.$store.commit('line/empty')
	app.$store.commit('material/empty')
	app.$store.commit('radios/emptyRadioRerurnType')
	app.$store.commit('repairUser/empty')
	app.$store.commit('returnMaterial/empty')
	app.$store.commit('storage/empty')
	app.$store.commit('superWO/empty')
	app.$store.commit('spareChecks/empty')
	
}
