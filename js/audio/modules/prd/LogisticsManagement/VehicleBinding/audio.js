// 失败提示音
const error = function() {
	var music = null;
	music = uni.createInnerAudioContext(); //创建播放器对象
	music.src = '/static/audio/prd/LogisticsManagement/VehicleBinding/fail.mp3';
	music.play(); //执行播放
	music.onEnded(() => {
		//播放结束
		music = null;
	});
}

// 成功提示音
const success = function() {
	var music = null;
	music = uni.createInnerAudioContext(); //创建播放器对象
	music.src = "/static/audio/prd/LogisticsManagement/VehicleBinding/success.mp3";
	music.play(); //执行播放
	music.onEnded(() => {
		//播放结束
		music = null;
	});
}

const VehicleBinding = {
	error,
	success
}

export default VehicleBinding
