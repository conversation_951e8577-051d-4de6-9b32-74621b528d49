import common from '@/js/audio/modules/common/common.js'
import AdjustmentofCarrierMaterial from '@/js/audio/modules/prd/LogisticsManagement/AdjustmentofCarrierMaterial/audio.js'
import AllocationOfSemiFinishedProducts from '@/js/audio/modules/prd/LogisticsManagement/AllocationOfSemi-finishedProducts/audio.js'
import MaterialMovingFrame from '@/js/audio/modules/prd/LogisticsManagement/MaterialMovingFrame/audio.js'
import materialReturn from '@/js/audio/modules/prd/LogisticsManagement/materialReturn/audio.js'
import materialShelf from '@/js/audio/modules/prd/LogisticsManagement/materialShelf/audio.js'
import MaterialTransfer from '@/js/audio/modules/prd/LogisticsManagement/MaterialTransfer/audio.js'
import ProductionAndDelivery from '@/js/audio/modules/prd/LogisticsManagement/ProductionAndDelivery/audio.js'
import SemiFinishedProductsStorage from '@/js/audio/modules/prd/LogisticsManagement/Semi-finishedProductsStorage/audio.js'
import SemiFinishMiscellaneousCollection from '@/js/audio/modules/prd/LogisticsManagement/Semi-FinishMiscellaneousCollection/audio.js'
import SemiFinishMiscellaneousSend from '@/js/audio/modules/prd/LogisticsManagement/Semi-FinishMiscellaneousSend/audio.js'
import VehicleBinding from '@/js/audio/modules/prd/LogisticsManagement/VehicleBinding/audio.js'

export {
	common,
	AdjustmentofCarrierMaterial,
	AllocationOfSemiFinishedProducts,
	MaterialMovingFrame,
	materialReturn,
	materialShelf,
	MaterialTransfer,
	ProductionAndDelivery,
	SemiFinishedProductsStorage,
	SemiFinishMiscellaneousCollection,
	SemiFinishMiscellaneousSend,
	VehicleBinding,
}
