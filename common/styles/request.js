let path = "http://localhost:8090"; //地址

export const request = (paramter) => {
	if (!paramter.url) {
		throw new Error("url not null.");
	}
	paramter.url = path + paramter.url;
	uni.showLoading({
		title: "加载中...",
		mask: true
	});
	uni.request({
		url: paramter.url,
		header: paramter.header || {
			'content-type': 'application/x-www-form-urlencoded',
			'Access-Control-Allow-Origin': '*'
		},
		dataType: "json",
		method: paramter.method || "POST",
		data: paramter.data || {},
		timeout: 180000,
		fail: res => {
			uni.hideLoading();
			return uni.showToast({
				icon: "loading",
				title: res.errMsg
			});
		},
		success: res => {
			uni.hideLoading();
			// 返回失败的直接提示信息好了 或者无需任何的处理直接弹出信息
			if (res.data.flag) {
				paramter.success(res.data);
			} else {
				uni.showToast({
					title: res.data.msg,
					icon: "none"
				})
				paramter.error(res.date);
			}

		}
	});
}