.xj_base_color {
	background-color: #009598;
}

.xj_button {
	height: 100upx;
	line-height: 100upx;
	text-align: center;
	color: #FFFFFF;
	background-color: #FFFFFF
}

.xj_button:active {
	color: white;
	background: gray;
	opacity: 0.5;
}

.xj_button_group {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.xj_form_icon {
	width: 40upx;
	height: 90upx;
	line-height: 90upx;
	font-size: 34upx;
	color: #009598;
	text-align: center;
	font-family: texticons;
	margin-right: 20upx;
}

.ds_background_color {
	background-color: #FFFFFF;
}

.ds_center {
	text-align: center;
}

.ds_null {
	text-align: center;
	font-size: 50upx;
}

.blue {
	background: #ff8282 !important;
}

.ds_row {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: space-around;
}

/* 固定容器在底部不动 */
.uni-fixed-bottom {
	width: 95%;
	bottom: 10upx;
	position: fixed;
	transform: translateZ(0);
}

/* 自定义 swiper */
.ullist {
	display: flex;
	justify-content: space-around;
	font-size: 30rpx;
	color: $uni-text-color;
	padding-top: 20rpx;
	padding-bottom: 20rpx;
	background-color: #fff;
	width: 100%;
}

.active {
	color: $uni-color-primary;
	border-bottom: 4rpx solid $uni-color-primary;
}

/* 设备维修 */
.repair_order_audit {
	width: 100%;
	height: auto;
	padding-bottom: 90rpx;
}

.cell-pd {
	padding: 22upx 30upx;
}

.list-pd {
	margin-top: 50upx;
}

.group {
	margin-bottom: 30rpx;
}

.radio-group {
	width: 100%;
	box-sizing: border-box;
}

.button {
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 999;
}

.uni-list {
	background-color: #FFFFFF;
	position: relative;
	width: 100%;
	display: flex;
	flex-direction: column;
}

.uni-list:after {
	position: absolute;
	z-index: 10;
	right: 0;
	bottom: 0;
	left: 0;
	height: 1px;
	content: '';
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: #c8c7cc;
}

.uni-list::before {
	position: absolute;
	z-index: 10;
	right: 0;
	top: 0;
	left: 0;
	height: 1px;
	content: '';
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: #c8c7cc;
}

.uni-list-cell {
	position: relative;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}

.uni-list-cell-hover {
	background-color: #eee;
}

.uni-list-cell-pd {
	padding: 22upx 30upx;
}

.uni-list-cell-left {
	font-size: 28upx;
	padding: 0 30upx;
}

.uni-list-cell-db,
.uni-list-cell-right {
	flex: 1;
}

.uni-list-cell::after {
	position: absolute;
	z-index: 3;
	right: 0;
	bottom: 0;
	left: 30upx;
	height: 1px;
	content: '';
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: #c8c7cc;
}


/* 上传 */
.uni-uploader {
	flex: 1;
	flex-direction: column;
}

.uni-uploader-head {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.uni-uploader-info {
	color: #B2B2B2;
}

.uni-uploader-body {
	margin-top: 16upx;
}

.uni-uploader__files {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}

.uni-uploader__file {
	margin: 10upx;
	width: 210upx;
	height: 210upx;
}

.uni-uploader__img {
	display: block;
	width: 210upx;
	height: 210upx;
}

.uni-uploader__input-box {
	position: relative;
	margin: 10upx;
	width: 208upx;
	height: 208upx;
	border: 2upx solid #D9D9D9;
}

.uni-uploader__input-box:before,
.uni-uploader__input-box:after {
	content: " ";
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	background-color: #D9D9D9;
}

.uni-uploader__input-box:before {
	width: 4upx;
	height: 79upx;
}

.uni-uploader__input-box:after {
	width: 79upx;
	height: 4upx;
}

.uni-uploader__input-box:active {
	border-color: #999999;
}

.uni-uploader__input-box:active:before,
.uni-uploader__input-box:active:after {
	background-color: #999999;
}

.uni-uploader__input {
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
}

.dtContent {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
