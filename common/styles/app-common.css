/* 一行一块 */
	.cu-form-group .title {
		min-width: calc(4em + 15px);
	}

	/* 灰色字体 */
	.cu-form-data-20 {
		font-size: 20px;
		color: #666666;
	}

	.cu-form-data-15 {
		font-size: 15px;
		color: #666666;
		word-wrap: break-word;
		word-break:break-all;
	}

	.cu-form-data-14 {
		font-size: 14px;
		color: #666666;
		word-wrap: break-word;
		word-break:break-all;
	}
	.cu-form-data-17 {
		font-size: 17px;
		color: #666666;
		word-wrap: break-word;
		word-break:break-all;
	}
	.uni-padding-wrap {
		width: 100%;
		padding: 0 20upx;
		height: 100%;
	}

	.uni-triplex-right {
		width: 30%;
	}

	.uni-triplex-row {
		/* height: 160upx; */
		padding: 0;
	}

	.page-list-left-row {
		display: flex;
		line-height: 1.5em;
		flex-direction: row;
	}
	/* 横向平均分布 */
	.cu-avg{
		-webkit-flex: 1;flex: 1;
	}
	.cu-form-data-weight{
		color: #FFB400;
		font-size: 18px;
	}
	.uni-triplex-left-80{
		width: 99%;
	}
	.panel-full{
		width: 100%;
	}
	/* 垂直居中显示 */
	.alin_x_center{
		height: 100%;
		margin-right: 0upx;
	}

	.detail-left {
		width: 50%;
		text-align: left;
	}

	.detail-right {
		width: 50%;
		text-align: right;
	}

	.uni-popup {
		position: fixed;
		/* #ifdef H5 */
		top: var(--window-top);
		/* #endif */
		/* #ifndef H5 */
		top: 0;
		/* #endif */
		bottom: 0;
		left: 0;
		right: 0;
		overflow: hidden;
	}

	.uni-popup__mask {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.4);
		opacity: 0;
	}

	.mask-ani {
		transition-property: opacity;
		transition-duration: 0.2s;
	}

	.green {
		background: #4cd964 !important;
	}
	
	.yellow {
		background: #e8ff82 !important;
	}

	.uni-top-mask {
		opacity: 1;
	}

	.uni-bottom-mask {
		opacity: 1;
	}

	.uni-center-mask {
		opacity: 1;
	}

	.uni-popup__wrapper {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		position: absolute;
	}

	.top {
		top: 0;
		left: 0;
		right: 0;
		transform: translateY(-500px);
	}

	.bottom {
		bottom: 0;
		left: 0;
		right: 0;
		transform: translateY(500px);
	}

	.center {
		/* #ifndef APP-NVUE */
		display: flex;
		flex-direction: column;
		/* #endif */
		bottom: 0;
		left: 0;
		right: 0;
		top: 0;
		justify-content: center;
		align-items: center;
		transform: scale(1.2);
		opacity: 0;
	}

	.uni-title {
		font-size: 14px;
		font-weight: 600;
		padding: 20upx 0;
		line-height: 1.2;
	}
