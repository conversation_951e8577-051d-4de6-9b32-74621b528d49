<script>
    export default {
        onLaunch: function() {
			uni.removeStorage({
							key:"plant_model",
							success() {
								// console.log("清除缓存 plant_model")
							}
						})
			uni.removeStorage({
			    key: 'updated',
			    success: function (res) {
			        // console.log('清楚缓存成功');
			    }
			});
            // console.log('App Launch');
        },
        onShow: function() {
            // console.log('App Show')
        },
        onHide: function() {
            // console.log('App Hide')
        },
		globalData: {
			test: ''
		}
    }
</script>

<style>
    /* uni.css - 通用组件、模板样式库，可以当作一套ui库应用 */
    @import './common/styles/uni.css';
	@import './common/styles/main.css';
	@import './common/styles/app-common.css';
	@import './common/styles/icon.css';
	@import './common/styles/self.css';
	@import './static/iconfont/iconfont.css';
</style>
