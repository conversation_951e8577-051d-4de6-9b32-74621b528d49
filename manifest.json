{
    "name" : "<PERSON>n<PERSON>ieMES",
    "appid" : "__UNI__5F0771F",
    "description" : "新界MES移动端",
    "versionName" : "1.2.2",
    "versionCode" : 39,
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /*版本弹出框*/
        "compatible" : {
            "ignoreVersion" : true
        },
        "modules" : {
            "Bluetooth" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\" />",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\" />",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.KILL_BACKGROUND_PROCESSES\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.NFC\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "x86" ]
            },
            "ios" : {
                "dSYMs" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {}
            },
            "icons" : {
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                },
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "ldpi" : "unpackage/res/icons/48x48.png",
                    "mdpi" : "unpackage/res/icons/48x48.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "static/images/banner/XJ.png",
                    "xhdpi" : "static/images/banner/XJ.png",
                    "xxhdpi" : "static/images/banner/XJ.png"
                }
            }
        },
        "usingComponents" : false
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : true,
            "es6" : false,
            "minified" : true
        },
        "usingComponents" : false,
        "permission" : {}
    },
    "mp-toutiao" : {
        "setting" : {
            "minified" : true,
            "es6" : false,
            "postcss" : true,
            "urlCheck" : false
        },
        "appid" : "",
        "usingComponents" : false
    },
    "mp-baidu" : {
        "appid" : "",
        "usingComponents" : false
    },
    "mp-alipay" : {
        "usingComponents" : false
    },
    "vueVersion" : "2",
    "fallbackLocale" : "zh-Hans",
    "h5" : {
        "sdkConfigs" : {
            "maps" : {}
        },
        "title" : "XinJieMES",
        "devServer" : {
            "port" : 5100,
            "https" : false
        },
        "router" : {
            "base" : "/XinjiePDA/XinJieWeb"
        }
    },
    "networkTimeout" : {
        "request" : 180000,
        "connectSocket" : 180000,
        "uploadFile" : 180000,
        "downloadFile" : 180000
    }
}
// "h5": {
//         "devServer": {
//             "proxy": {
//                 "/api": {
//                     "target": "http://***************:8090",
//                     "changeOrigin": true,
//                     "secure": false,
// 					"pathRewrite":{"^/api":""}
//                 }
//             }
//         }
//     }

